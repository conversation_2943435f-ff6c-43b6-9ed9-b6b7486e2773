import { ref } from 'vue';
import { Order, OrderItem } from '@/api/order';
import { formatDate } from '@/utils/date';

export function useOrder() {
  const order = ref<Order>({
    uuid: '',
    customer_uuid: '',
    customer_name: '',
    order_no: '',
    order_items: [],
    item_qty: 0,
    rebate: 0,
    discount: 0,
    subtotal: 0,
    service_fee: 0,
    tax: 0,
    tax_id: '',
    shipping_fee: 0,
    total_discount: 0,
    total: 0,
    pay_type: 'cash',
    status: 'pending',
    notes: '',
    void_reason: '',
    order_at: formatDate(new Date(), 'YYYY/MM/DD'),
  });

  const payTypes = [
    {
      label: 'cash',
      value: 'cash',
      icon: 'payments',
    },
    {
      label: 'creditCard',
      value: 'credit',
      icon: 'credit_card',
    },
    {
      label: 'Paypal',
      value: 'paypal',
      icon: 'paypal',
    },
  ];

  const newOrder = (data: Order | null) => {
    return { ...order.value, ...data };
  };

  const totalQuantity = (data: Order) => {
    if (!data.order_items) {
      return 0;
    }

    return data.order_items.reduce((acc, item) => acc + item.quantity, 0);
  };

  const FIXED_DIGITAL = 2;

  const fixedNumber = (num: number) => {
    if (isNaN(num)) {
      return 0;
    }

    return parseFloat(num.toFixed(FIXED_DIGITAL));
  };

  const getSubtotal = (data: Order) => {
    const result = data.order_items.reduce(
      (acc, item) => acc + getItemTotal(item),
      0
    );

    return fixedNumber(result);
  };

  const getItemTotal = (item: OrderItem) => {
    if (!item || item.is_free) {
      return 0;
    }

    const subtotal = getItemSubtotal(item);

    return fixedNumber(
      subtotal -
        getRebatePrice(subtotal, item.rebate) -
        getPercentageOff(subtotal, item.rebate, item.discount)
    );
  };

  const getItemSubtotal = (item: OrderItem) => {
    return fixedNumber(item.quantity * item.price);
  };

  // Calculate the rebate
  const getRebatePrice = (subtotal: number, rebate: number): number => {
    if (rebate <= 0) {
      return 0;
    }

    if (rebate >= subtotal) {
      return subtotal;
    }

    return fixedNumber(rebate);
  };

  // Calculate the percentage off amount
  const getPercentageOff = (
    subtotal: number,
    rebate: number,
    discount: number
  ) => {
    if (discount <= 0) {
      return 0;
    }

    let result = subtotal - getRebatePrice(subtotal, rebate);
    if (discount >= 100) {
      return fixedNumber(result);
    }

    result = (subtotal - getRebatePrice(subtotal, rebate)) * (discount * 0.01);

    return fixedNumber(result);
  };

  const grandTotal = (data: Order) => {
    const subtotal = getSubtotal(data);

    let result =
      subtotal -
      getRebatePrice(subtotal, data.rebate) -
      getPercentageOff(subtotal, data.rebate, data.discount);

    if (result < 0) {
      result = 0;
    }

    return fixedNumber(result);
  };

  const checkout = (data: Order) => {
    data.order_at = formatDate(data.order_at, 'YYYY-MM-DD HH:mm');
    data.subtotal = getSubtotal(data);
    data.total_discount =
      getRebatePrice(data.subtotal, data.rebate) +
      getPercentageOff(data.subtotal, data.rebate, data.discount);
    data.total = grandTotal(data);
  };

  return {
    payTypes,
    newOrder,
    totalQuantity,
    getSubtotal,
    getItemSubtotal,
    getItemTotal,
    getRebatePrice,
    getPercentageOff,
    grandTotal,
    checkout,
  };
}
