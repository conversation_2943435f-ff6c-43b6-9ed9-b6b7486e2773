import{u as S}from"./vue-i18n.1783a0cb.js";import{u as N}from"./use-quasar.3b603a60.js";import{h as t,L as h,bx as q,by as I,M as Q,ad as F,O as P,ai as k,c as _,aq as M,g as $,aC as z,r as w,d as B,o as D,a as V,w as E,p as C,k as x,j as p,m as g,q as L,y as T,C as v,bz as A,Q as j}from"./index.09f89dc4.js";import{_ as K}from"./plugin-vue_export-helper.21dcd24c.js";const O=[t("circle",{cx:"15",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"60",cy:"15",r:"9","fill-opacity":".3"},[t("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:".5",to:".5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"105",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})])];var R=h({name:"QSpinnerDots",props:q,setup(e){const{cSize:r,classes:s}=I(e);return()=>t("svg",{class:s.value,fill:"currentColor",width:r.value,height:r.value,viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg"},O)}}),U=h({name:"QInnerLoading",props:{...Q,...F,showing:Boolean,color:String,size:{type:[String,Number],default:"42px"},label:String,labelClass:String,labelStyle:[String,Array,Object]},setup(e,{slots:r}){const s=$(),c=P(e,s.proxy.$q),{transitionProps:d,transitionStyle:o}=k(e),i=_(()=>"q-inner-loading q--avoid-card-border absolute-full column flex-center"+(c.value===!0?" q-inner-loading--dark":"")),l=_(()=>"q-inner-loading__label"+(e.labelClass!==void 0?` ${e.labelClass}`:""));function n(){const a=[t(z,{size:e.size,color:e.color})];return e.label!==void 0&&a.push(t("div",{class:l.value,style:e.labelStyle},[e.label])),a}function m(){return e.showing===!0?t("div",{class:i.value,style:o.value},r.default!==void 0?r.default():n()):null}return()=>t(M,d.value,m)}});function W(e={}){const{timeout:r=20,barcodeField:s="barcode"}=e,c=w(!0);return{isReady:c,processBarcode:i=>{c.value=!1;const l=new CustomEvent("barcode-scanned",{detail:{barcode:i}});window.dispatchEvent(l),setTimeout(()=>{c.value=!0},r)},findProductByBarcode:(i,l)=>i.find(n=>n[s]===l)}}const G={class:"barcode-scanner",style:{width:"15rem","max-width":"80%"}},H=B({__name:"BarcodeScanner",props:{products:{type:Array,required:!0},barcodeField:{type:String,default:"barcode"}},emits:["update:modelValue","barcode-detected","product-found","product-not-found"],setup(e,{emit:r}){const{t:s}=S(),c=e,d=r,{isReady:o,processBarcode:i,findProductByBarcode:l}=W({timeout:200,barcodeField:c.barcodeField}),n=w(""),m=_(()=>o.value?"":s("barcodeScanner.scanning")),a=()=>{n.value&&o.value&&(i(n.value),n.value="")},y=f=>{const u=f.detail.barcode;d("barcode-detected",u);const b=l(c.products,u);b?d("product-found",b):d("product-not-found",u)};return D(()=>{window.addEventListener("barcode-scanned",y)}),V(()=>{window.removeEventListener("barcode-scanned",y)}),E(()=>n,f=>{d("update:modelValue",f)}),(f,u)=>(C(),x("div",G,[p(j,{modelValue:n.value,"onUpdate:modelValue":u[0]||(u[0]=b=>n.value=b),label:v(s)("barcode"),placeholder:m.value,onKeydown:A(a,["enter"]),dense:"",clearable:"",loading:!v(o)},{prepend:g(()=>[p(L,{name:"sym_o_barcode_scanner"})]),append:g(()=>[p(T,{round:"",flat:"",icon:"search",onClick:a,disable:!n.value||!v(o)},null,8,["disable"])]),_:1},8,["modelValue","label","placeholder","loading"]),p(U,{showing:!v(o)},{default:g(()=>[p(R,{size:"40px",color:"primary"})]),_:1},8,["showing"])]))}});var J=K(H,[["__scopeId","data-v-ccaebd76"]]);const X={class:"barcode-scanner-wrapper"},te=B({__name:"BarcodeScannerWrapper",props:{products:{type:Array,required:!0},barcodeField:{type:String,default:"barcode"},autoNotify:{type:Boolean,default:!0}},emits:["barcode-scanned","product-found","product-not-found"],setup(e,{emit:r}){const{t:s}=S(),c=N(),d=e,o=r,i=w(""),l=a=>{i.value=a,o("barcode-scanned",a)},n=a=>{d.autoNotify&&c.notify({message:`${a.name} ${s("barcodeScanner.scanCompleted")}`,color:"positive",position:"top",timeout:1500}),o("product-found",a)},m=a=>{d.autoNotify&&c.notify({message:s("barcodeScanner.notFound",{barcode:a}),color:"warning",position:"top",timeout:1500}),o("product-not-found",a)};return(a,y)=>(C(),x("div",X,[p(J,{products:e.products,"barcode-field":e.barcodeField,onBarcodeDetected:l,onProductFound:n,onProductNotFound:m},null,8,["products","barcode-field"])]))}});export{te as _};
