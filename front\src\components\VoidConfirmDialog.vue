<template>
  <q-dialog v-model="visible" persistent no-refocus>
    <q-card style="min-width: 350px">
      <q-card-section>
        <div class="text-h6">{{ $t('orderVoided.title') }}</div>
      </q-card-section>

      <q-card-section>
        <q-option-group
          v-model="voidedReason"
          :options="voidedReasons"
          type="radio"
        />

        <q-input
          v-if="voidedReason === 'other'"
          v-model.trim="otherReasonText"
          type="textarea"
          :label="$t('reason')"
          class="q-mt-md"
          outlined
          :rules="[
            (val) => val.length > 0 || $t('orderVoided.otherReasonRequired'),
          ]"
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          :label="$t('cancel')"
          color="primary"
          @click="closeDialog"
        />
        <q-btn :label="$t('void')" color="negative" @click="confirmVoided" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Notify } from 'quasar';
import { voidedReasons } from '@/types/order';

const { t } = useI18n();

const props = defineProps({
  orderId: {
    type: String,
    required: true,
  },
  otherReason: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['order-voided']);

const visible = ref(false);
const voidedReason = ref('');
const otherReasonText = ref('');

const openDialog = () => {
  visible.value = true;
  voidedReason.value = '';
  otherReasonText.value = props.otherReason || '';

  if (props.otherReason) {
    // 如果有傳入其他原因，則自動選擇"其他"選項
    voidedReason.value = 'other';
  } else {
    // 預設選擇第一個作廢原因
    voidedReason.value = voidedReasons[0].value;
  }
};

const closeDialog = () => {
  visible.value = false;
};

const confirmVoided = () => {
  // 檢查是否選擇了作廢原因
  if (!voidedReason.value) {
    Notify.create({
      type: 'warning',
      position: 'top',
      message: t('orderVoided.reasonRequired'),
    });
    return;
  }

  // 如果選擇了"其他"，檢查是否填寫說明
  if (voidedReason.value === 'other' && otherReasonText.value === '') {
    Notify.create({
      type: 'warning',
      position: 'top',
      message: t('orderVoided.otherReasonRequired'),
    });
    return;
  }

  // 準備作廢原因資料
  const voidedData = {
    orderId: props.orderId,
    reason: voidedReason.value,
    otherReason: voidedReason.value === 'other' ? otherReasonText.value : null,
  };

  // 觸發作廢訂單事件
  emit('order-voided', voidedData);

  // 關閉對話框
  closeDialog();
};

// 對外暴露方法
defineExpose({
  openDialog,
});
</script>
