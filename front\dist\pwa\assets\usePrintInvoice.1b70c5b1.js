import{d as k,r as h,c as w,w as R,a0 as V,p as x,v as U,m,j as d,G as D,t as E,A as b,C as X,bz as $,q as I,k as T,B,x as C,aK as L,y as F,H as O,E as q,Q as A}from"./index.09f89dc4.js";import{u as M}from"./vue-i18n.1783a0cb.js";import{_ as Q}from"./plugin-vue_export-helper.21dcd24c.js";import{f as P}from"./date.6d29930c.js";import{O as Y}from"./order.7fd0b308.js";import{X as N}from"./xero.34271ea4.js";const z={class:"text-h6"},j={class:"text-subtitle2 text-grey-7 q-mt-sm"},H={key:0,class:"text-caption text-grey-6 q-mt-sm"},S=k({__name:"EmailInputDialog",props:{modelValue:{type:Boolean},defaultEmail:{default:""},customerEmail:{default:""},loading:{type:Boolean,default:!1},hintMessage:{default:""}},emits:["update:modelValue","confirm","cancel"],setup(f,{expose:y,emit:i}){M();const n=f,a=i,r=h(),t=h(""),l=w({get:()=>n.modelValue,set:s=>a("update:modelValue",s)}),_=w(()=>n.hintMessage&&n.hintMessage.length>0);R(()=>n.modelValue,s=>{s&&(t.value=n.customerEmail||n.defaultEmail||"",V(()=>{var e;(e=r.value)==null||e.focus()}))});const u=async()=>{var s;if(!t.value||!/.+@.+\..+/.test(t.value)){(s=r.value)==null||s.focus();return}a("confirm",t.value)},p=()=>{a("cancel"),l.value=!1};return y({resetForm:()=>{t.value=""}}),(s,e)=>(x(),U(q,{modelValue:l.value,"onUpdate:modelValue":e[1]||(e[1]=o=>l.value=o),persistent:""},{default:m(()=>[d(O,{style:{"min-width":"400px","max-width":"500px"}},{default:m(()=>[d(D,null,{default:m(()=>[E("div",z,b(s.$t("sendEmail")),1),E("div",j,b(s.$t("emailInput.confirmEmailAddress")),1)]),_:1}),d(D,{class:"q-pt-none"},{default:m(()=>[d(X(A),{modelValue:t.value,"onUpdate:modelValue":e[0]||(e[0]=o=>t.value=o),filled:"",type:"email",label:s.$t("emailInput.emailAddress"),placeholder:s.$t("emailInput.emailPlaceholder"),rules:[o=>o&&o.length>0||s.$t("emailInput.emailRequired"),o=>/.+@.+\..+/.test(o)||s.$t("emailInput.invalidEmail")],"lazy-rules":"",ref_key:"emailInputRef",ref:r,onKeyup:$(u,["enter"])},{prepend:m(()=>[d(I,{name:"email"})]),_:1},8,["modelValue","label","placeholder","rules"]),_.value?(x(),T("div",H,[d(I,{name:"info",size:"xs",class:"q-mr-xs"}),B(" "+b(s.hintMessage),1)])):C("",!0)]),_:1}),d(L,{align:"right"},{default:m(()=>[d(F,{flat:"",label:s.$t("cancel"),color:"grey",onClick:p,disable:s.loading},null,8,["label","disable"]),d(F,{label:s.$t("sendEmail"),color:"primary",onClick:u,loading:s.loading},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var oe=Q(S,[["__scopeId","data-v-605d19b0"]]);function te(){const f=h({uuid:"",customer_uuid:"",customer_name:"",order_no:"",order_items:[],item_qty:0,rebate:0,discount:0,subtotal:0,service_fee:0,tax:0,tax_id:"",shipping_fee:0,total_discount:0,total:0,pay_type:"cash",status:"pending",notes:"",void_reason:"",order_at:P(new Date,"YYYY/MM/DD")}),y=[{label:"cash",value:"cash",icon:"payments"},{label:"creditCard",value:"credit",icon:"credit_card"},{label:"Paypal",value:"paypal",icon:"paypal"}],i=e=>({...f.value,...e}),n=e=>e.order_items?e.order_items.reduce((o,c)=>o+c.quantity,0):0,a=2,r=e=>isNaN(e)?0:parseFloat(e.toFixed(a)),t=e=>{const o=e.order_items.reduce((c,g)=>c+l(g),0);return r(o)},l=e=>{if(!e||e.is_free)return 0;const o=_(e);return r(o-u(o,e.rebate)-p(o,e.rebate,e.discount))},_=e=>r(e.quantity*e.price),u=(e,o)=>o<=0?0:o>=e?e:r(o),p=(e,o,c)=>{if(c<=0)return 0;let g=e-u(e,o);return c>=100||(g=(e-u(e,o))*(c*.01)),r(g)},v=e=>{const o=t(e);let c=o-u(o,e.rebate)-p(o,e.rebate,e.discount);return c<0&&(c=0),r(c)};return{payTypes:y,newOrder:i,totalQuantity:n,getSubtotal:t,getItemSubtotal:_,getItemTotal:l,getRebatePrice:u,getPercentageOff:p,grandTotal:v,checkout:e=>{e.order_at=P(e.order_at,"YYYY-MM-DD HH:mm"),e.subtotal=t(e),e.total_discount=u(e.subtotal,e.rebate)+p(e.subtotal,e.rebate,e.discount),e.total=v(e)}}}function ne(){const f=async i=>{var n,a,r;if(!i.xero_sync||i.xero_sync.sync_status=="syncing"){const t=await Y.get(i.uuid);if(i.xero_sync=t.result.xero_sync,!i.xero_sync)throw new Error("This order has not been synced to Xero, cannot print invoice")}if(((n=i.xero_sync)==null?void 0:n.xero_invoice_id)&&i.xero_sync.sync_status==="success")try{console.log(`Printing Xero invoice: ${i.xero_sync.xero_invoice_id}`),await y(i.xero_sync.xero_invoice_id);return}catch(t){console.error("Failed to print Xero invoice:",t);let l="Failed to print Xero invoice";throw t instanceof Error&&(t.message.includes("AUTHORISED")||t.message.includes("APPROVED")?l="This invoice needs to be authorised or approved in Xero before printing PDF":t.message.includes("popup blocked")?l="Browser popup blocked, please allow popups and try again":t.message.includes("too small")?l="Invalid PDF data received from Xero, please check invoice status":l=`Failed to print Xero invoice: ${t.message}`),new Error(l)}else throw((a=i.xero_sync)==null?void 0:a.sync_status)==="pending"?new Error("This order is still being synced to Xero, please try again later"):((r=i.xero_sync)==null?void 0:r.sync_status)==="failed"?new Error("Failed to sync this order to Xero, cannot print invoice"):new Error("This order has not been synced to Xero, cannot print invoice")},y=async i=>{try{console.log(`Requesting PDF for invoice: ${i}`);const n=await N.getInvoicePDF(i);if(console.log("PDF Blob received:",n),console.log(`PDF Blob size: ${n.size} bytes`),console.log(`PDF Blob type: ${n.type}`),n.size<100)throw new Error("PDF data too small, likely invalid");n.type.includes("application/pdf")||console.warn(`Unexpected blob type: ${n.type}`);const a=URL.createObjectURL(n);console.log(`PDF URL created: ${a}`),console.log("Testing PDF URL before opening print window...");const r=window.open(a,"_blank");if(!r)throw URL.revokeObjectURL(a),new Error("Failed to open print window - popup blocked?");console.log("Print window opened successfully"),r.onload=()=>{console.log("PDF loaded in print window"),setTimeout(()=>{r.print()},1e3)},r.onerror=t=>{console.error("Error loading PDF in print window:",t),URL.revokeObjectURL(a),r.close()},setTimeout(()=>{URL.revokeObjectURL(a)},15e3)}catch(n){throw console.error("Error printing Xero invoice:",n),n}};return{printInvoice:f}}export{oe as E,ne as a,te as u};
