<template>
  <q-layout view="hHh LpR fFf">
    <q-drawer
      v-if="showDrawer"
      show-if-above
      :width="60"
      :breakpoint="300"
      no-swipe-backdrop
      no-swipe-open
      no-swipe-close
      bordered
      style="background-color: #789dbc; color: #f8fafc"
    >
      <q-list class="q-list-bottom">
        <!-- 打卡 -->
        <q-item clickable v-ripple @click="showPunchDialog">
          <q-icon name="punch_clock" size="sm" />
        </q-item>

        <!-- 庫存 -->
        <q-item clickable v-ripple @click="inventoryDialog?.openDialog">
          <q-icon name="inventory" size="sm" />
        </q-item>

        <q-separator />

        <q-item clickable v-ripple>
          <q-icon name="language" size="sm" />

          <q-menu anchor="top right">
            <q-list style="min-width: 100px">
              <q-item clickable v-close-popup @click="changeLanguage('en-US')">
                <q-item-section>
                  <q-item-label>{{ $t('app.english') }}</q-item-label>
                </q-item-section>
                <q-item-section avatar v-if="locale === 'en-US'">
                  <q-icon name="check" color="primary" />
                </q-item-section>
              </q-item>

              <q-item clickable v-close-popup @click="changeLanguage('zh-TW')">
                <q-item-section>
                  <q-item-label>{{ $t('app.chinese') }}</q-item-label>
                </q-item-section>
                <q-item-section avatar v-if="locale === 'zh-TW'">
                  <q-icon name="check" color="primary" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-item>

        <q-separator />

        <template v-if="authStore.isAdmin()">
          <q-item clickable @click="router.push('/admin/dashboard')">
            <q-icon name="manage_accounts" size="sm" />
          </q-item>
          <q-separator />
        </template>

        <q-item clickable v-ripple @click="logout">
          <q-icon name="logout" size="sm" />
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />

      <PunchDialog v-model="punchDialog" />
      <InventoryDialog ref="inventoryDialog" />
    </q-page-container>
  </q-layout>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
// import { useQuasar } from 'quasar';
import { useRoute, useRouter } from 'vue-router';
import PunchDialog from './components/PunchDialog.vue';
import InventoryDialog from './components/InventoryDialog.vue';
import { AuthApi } from '@/api/auth';
import { useAuthStore } from '@/stores/auth-store';
import { useDialog } from '@/utils';

const route = useRoute();
const router = useRouter();
const dialog = useDialog();
const { t } = useI18n();
const { locale } = useI18n();

const changeLanguage = (lang: string) => {
  locale.value = lang;
  // 儲存用戶選擇的語言到localStorage
  localStorage.setItem('user-locale', lang);
};

onMounted(() => {
  const userLocale = localStorage.getItem('user-locale');
  if (userLocale) {
    locale.value = userLocale;
  }
});

// const $q = useQuasar();

const authStore = useAuthStore();

const inventoryDialog = ref();

// const isMobile = computed(
//   () => $q.platform.is.android || $q.platform.is.iphone
// );

// 在結帳頁面不顯示 drawer
const showDrawer = computed(() => {
  return !route.params.orderID;
  // && (!isMobile.value || (isMobile.value && $q.screen.width > $q.screen.height))
});

const punchDialog = ref(false);
const showPunchDialog = () => {
  punchDialog.value = true;
};

const logout = () => {
  dialog.showMessage({
    message: t('logoutConfirm'),
    timeout: 0,
    ok: async () => {
      await AuthApi.logout();
      authStore.logout();
      router.push('/login');
    },
  });
};
</script>

<style lang="scss">
.q-list-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
}
</style>
