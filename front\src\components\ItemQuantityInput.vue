<template>
  <q-item class="item-quantity-input q-px-none q-pb-none">
    <q-item-section side class="q-px-none">
      <q-btn
        type="button"
        unelevated
        square
        color="negative"
        icon="remove"
        align="center"
        @click.stop="minusQty"
        class="full-height"
      />
    </q-item-section>
    <q-item-section>
      <q-item-label>
        <NumberInput
          v-model="localItem.quantity"
          :min="1"
          :step="1"
          square
          dense
          outlined
          :input-class="['text-center', 'text-h6']"
          @update:model-value="updateQuantity"
        />
      </q-item-label>
    </q-item-section>
    <q-item-section side style="padding-left: 0">
      <q-btn
        type="button"
        unelevated
        square
        color="positive"
        icon="add"
        align="center"
        @click.stop="addQty"
        class="full-height"
      />
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { OrderItem } from '@/api/order';

const props = defineProps({
  item: {
    type: Object as () => OrderItem,
    required: true,
  },
  canLessThanZero: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['remove-item', 'update-quantity']);

const localItem = ref<OrderItem>(props.item);

onMounted(() => {
  localItem.value = props.item;
});

// 監聽外部item變化，同步到本地
watch(
  () => props.item,
  (newItem) => {
    localItem.value = newItem;
  },
  { deep: true }
);

const minusQty = () => {
  if (localItem.value.quantity <= 1) {
    if (props.canLessThanZero) {
      // 當數量為1時，直接刪除項目
      emit('remove-item', localItem.value);
      return;
    } else {
      return; // 不允許小於1，直接返回
    }
  }
  // 通知父組件更新數量
  const newQuantity = localItem.value.quantity - 1;
  emit('update-quantity', localItem.value, newQuantity);
};

const addQty = () => {
  // 通知父組件更新數量
  const newQuantity = localItem.value.quantity + 1;
  emit('update-quantity', localItem.value, newQuantity);
};

const updateQuantity = (value: number) => {
  if (value < 1) {
    if (props.canLessThanZero) {
      emit('remove-item', localItem.value);
      return;
    } else {
      // 不允許小於1，保持原值不變
      return;
    }
  }
  // 通知父組件更新數量
  emit('update-quantity', localItem.value, value);
};
</script>
