package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AnnouncementController struct {
	announcementService service.AnnouncementService
}

func NewAnnouncementController(r *gin.RouterGroup, announcementService service.AnnouncementService) {
	announcementController := AnnouncementController{announcementService}

	v1 := r.Group("/v1/announcements")
	{
		v1.GET("", announcementController.GetAnnouncementHandler)
		v1.PUT("", announcementController.UpdateAnnouncementHandler)
	}
}

func (ctr *AnnouncementController) GetAnnouncementHandler(c *gin.Context) {
	announcement, err := ctr.announcementService.Get(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch announcement")
		return
	}

	utils.HandleSuccess(c, announcement)
}

func (ctr *AnnouncementController) UpdateAnnouncementHandler(c *gin.Context) {
	var payload domain.AnnouncementUpdatePayload

	if err := c.ShouldBindJSON(&payload); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request payload")
		return
	}

	if err := ctr.announcementService.Update(c, &payload); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update announcement")
		return
	}

	utils.HandleSuccess(c, gin.H{"message": "Announcement updated successfully"})
}
