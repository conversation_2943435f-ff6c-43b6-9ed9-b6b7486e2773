<template>
  <q-page>
    <q-card flat square bordered class="bg-cream">
      <q-card-section>
        <q-table
          :rows="rows"
          :columns="columns"
          row-key="uuid"
          virtual-scroll
          v-model:pagination="pagination"
          hide-pagination
          binary-state-sort
          class="q-pa-sm full-height"
          table-header-class="bg-grey-2"
          @request="onRequest"
          :loading="isLoading"
        >
          <!-- actions -->
          <template v-slot:top>
            <div class="row q-gutter-sm q-mb-sm">
              <!-- search -->
              <q-input
                filled
                dense
                v-model="filter.search"
                :debounce="300"
                @update:model-value="getStockHistories"
                label="Search"
                class="q-pb-md-sm"
              >
                <template v-slot:prepend>
                  <q-icon name="search" class="cursor-pointer" />
                </template>
                <!-- clear -->
                <template v-slot:append v-if="filter.search">
                  <q-icon
                    name="close"
                    class="cursor-pointer"
                    @click.stop="clearSearch"
                  />
                </template>
              </q-input>
            </div>
          </template>

          <!-- pagination -->
          <template v-slot:bottom>
            <TablePagination
              v-model="pagination"
              @getData="getStockHistories"
            />
          </template>

          <!-- cell -->
          <template v-slot:body-cell-stock_quantity="props">
            <q-td :props="props">
              <!-- 安全庫存 -->
              <span
                class="text-positive text-h6"
                v-if="props.row.diff_stock_qty > 5"
              >
                {{ props.row.stock_qty }}
              </span>
              <!-- 接近低庫存 -->
              <span
                class="text-warning text-h6"
                v-else-if="props.row.diff_stock_qty > 0"
              >
                {{ props.row.stock_qty }}
              </span>
              <!-- 低庫存 -->
              <span class="text-negative text-h6" v-else>
                {{ props.row.stock_qty }}
              </span>
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  InventoryApi,
  ProductStock,
  ProductStockFilter,
} from '@/api/inventory';
import { Pagination, TableRequestProps } from '@/types';

const { t } = useI18n();

const rows = ref<ProductStock[]>([]);
const columns = computed(() => [
  {
    name: 'name',
    label: t('product.name'),
    align: 'left' as const,
    field: 'name',
    sortable: true,
  },
  {
    name: 'barcode',
    label: t('barcode'),
    align: 'left' as const,
    field: 'barcode',
    sortable: true,
  },
  {
    name: 'category',
    label: t('category'),
    align: 'left' as const,
    field: 'category',
    sortable: true,
  },
  {
    name: 'stock_quantity',
    label: t('stockQuantity'),
    align: 'left' as const,
    field: 'stock_qty',
    sortable: true,
  },
  {
    name: 'unit',
    label: t('unit'),
    align: 'left' as const,
    field: 'unit',
  },
]);
const pagination = ref<Pagination>({
  sortBy: 'diff_stock_qty',
  descending: false,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

const onRequest = (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  if (sortBy != '') {
    pagination.value.sortBy = sortBy;
    pagination.value.descending = descending;
  }

  getStockHistories();
};

const filter = ref<ProductStockFilter>({
  search: '',
});
const isLoading = ref(false);
const getStockHistories = async () => {
  try {
    isLoading.value = true;
    const response = await InventoryApi.listProductStocks({
      filter: filter.value,
      pagination: pagination.value,
    });

    rows.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const clearSearch = () => {
  filter.value.search = '';
  getStockHistories();
};

onMounted(() => {
  getStockHistories();
});
</script>
