<template>
  <q-dialog v-model="visible" class="card-dialog" no-refocus>
    <q-card class="column">
      <!-- close -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-mt-sm">
          <div class="text-h5 text-bold">
            {{ t('orderDetail') }}
          </div>
          <q-space />
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="visible = false"
          />
        </div>
      </q-card-section>

      <q-card-section class="col-10 text-h6 q-py-sm">
        <q-scroll-area class="full-height">
          <!-- data -->
          <div class="row q-mb-sm">
            <!-- order No -->
            <q-item class="col-12">
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('orderNo') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.id }}
              </q-item-section>
            </q-item>
            <!-- order date -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="event" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('orderDate') }}
              </q-item-section>
              <q-item-section>
                {{ formatDate(wcOrder?.date_created, 'YYYY-MM-DD HH:mm') }}
              </q-item-section>
            </q-item>
            <!-- customer -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="person" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('customer.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_first_name }}
                {{ wcOrder?.billing_last_name }}
              </q-item-section>
            </q-item>
            <!-- email -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="email" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('email.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_email }}
              </q-item-section>
            </q-item>
            <!-- phone -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="phone" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('phone') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_phone }}
              </q-item-section>
            </q-item>
            <!-- status -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="check_circle" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('status') }}
              </q-item-section>
              <q-item-section>
                <q-select
                  v-model="selectedStatus"
                  :options="statusOptions"
                  option-value="value"
                  option-label="label"
                  emit-value
                  map-options
                  outlined
                  dense
                  @update:model-value="updateOrderStatus"
                  :loading="isLoading"
                />
              </q-item-section>
            </q-item>
            <!-- payment method -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="payment" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('payment.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.payment_method_title }}
              </q-item-section>
            </q-item>
            <!-- total -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="attach_money" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('total') }}
              </q-item-section>
              <q-item-section>
                AU$ {{ formatNumber(wcOrder?.total, 2) }}
              </q-item-section>
            </q-item>

            <q-item class="col-12">
              <q-item-section>
                <q-separator color="black" size="2px" />
              </q-item-section>
            </q-item>

            <!-- 收貨人 -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="person" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('receiver') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.shipping_first_name }}
                {{ wcOrder?.shipping_last_name }}
              </q-item-section>
            </q-item>

            <!-- 運送方式 -->
            <q-item class="col-12" v-if="wcOrder?.shipping_method || wcOrder?.shipping_method_title">
              <q-item-section side>
                <q-icon name="local_shipping" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('shippingMethod') }}
              </q-item-section>
              <q-item-section>
                {{ getShippingMethodDisplay(wcOrder?.shipping_method, wcOrder?.shipping_method_title) }}
              </q-item-section>
            </q-item>

            <!-- address -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="location_on" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('address') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.shipping_address_1 }},
                {{ wcOrder?.shipping_city }},
                {{ wcOrder?.shipping_state }},
                {{ wcOrder?.shipping_postcode }},
                {{ wcOrder?.shipping_country }}
              </q-item-section>
            </q-item>

            <!-- 取消原因 - 只在訂單被取消時顯示 -->
            <q-item class="col-12" v-if="wcOrder?.status === 'wc-cancelled' && wcOrder?.customer_note">
              <q-item-section side>
                <q-icon name="cancel" size="sm" color="red" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('wcOrder.cancel.reasonLabel') }}
              </q-item-section>
              <q-item-section>
                <span class="text-red">{{ wcOrder.customer_note }}</span>
              </q-item-section>
            </q-item>
          </div>
          <!-- items -->
          <q-list bordered separator>
            <q-item class="bg-grey-3">
              <q-item-section class="text-bold">
                {{ t('product.label') }}
              </q-item-section>

              <q-item-section class="text-bold" side>
                {{ t('price') }}
              </q-item-section>
            </q-item>
            <!-- 商品列表 -->
            <q-item v-for="item in wcOrder?.line_items" :key="item.name">
              <q-item-section>
                {{ item.name }}
              </q-item-section>
              <q-item-section class="text-subtitle1" side>
                x {{ item.quantity }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ formatNumber(item.total, 2) }}
              </q-item-section>
            </q-item>
            <!-- 運費 -->
            <q-item v-if="wcOrder?.shipping_total">
              <q-item-section>
                {{ t('shippingFee') }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ formatNumber(wcOrder?.shipping_total, 2) }}
              </q-item-section>
            </q-item>
            <!-- 總計 -->
            <q-item class="bg-grey-3">
              <q-item-section>
                <q-item-label class="text-bold">{{ t('total') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label class="text-bold">
                  AU$ {{ formatNumber(wcOrder?.total, 2) }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </q-card-section>

      <!-- actions -->
      <q-card-actions align="between" class="col-1 bg-grey-2 q-pa-sm">
        <q-btn
          color="red"
          icon="delete"
          :label="t('wpOrder.actions.cancel')"
          no-caps
          @click="orderCancelDialog?.openDialog()"
          :loading="isLoading"
          v-if="wcOrder?.status != 'wc-cancelled'"
        />
      </q-card-actions>
    </q-card>

    <!-- 取消訂單對話框 -->
    <WCOrderCancelDialog
      ref="orderCancelDialog"
      :order-id="props.orderID"
      @order-cancelled="handleOrderCancelled"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { OrderApi, WCOrder } from '@/api/order';
import { formatDate, formatNumber, useDialog } from '@/utils';
import WCOrderCancelDialog from './WCOrderCancelDialog.vue';

const { t } = useI18n();
const dialog = useDialog();

// 運送方式顯示函數
const getShippingMethodDisplay = (method?: string, title?: string) => {
  if (!method && !title) return t('unknown');

  if (title?.includes('Parcel')) {
    return 'Parcel Post';
  } else if (title?.includes('Express')) {
    return 'Express Post';
  } else {
    return title || method || t('unknown');
  }
};

const props = defineProps<{
  modelValue: boolean;
  orderID: number;
}>();

const emit = defineEmits(['update:modelValue', 'refresh']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      getData();
    }
  }
);

const isLoading = ref(false);
const wcOrder = ref<WCOrder>();
const orderCancelDialog = ref();

// 訂單狀態選項
const statusOptions = computed(() => [
  { label: t('orderStatus.processing'), value: 'processing' },
  { label: t('orderStatus.on-hold'), value: 'on-hold' },
  { label: t('orderStatus.packing'), value: 'packing' },
  { label: t('orderStatus.shipping'), value: 'shipping' },
  { label: t('orderStatus.completed'), value: 'completed' },
  { label: t('orderStatus.cancelled'), value: 'cancelled' },
  { label: t('orderStatus.refunded'), value: 'refunded' },
]);

// 選中的狀態
const selectedStatus = computed({
  get: () => {
    const status = wcOrder.value?.status;
    if (!status) return '';
    return status.startsWith('wc-') ? status.substring(3) : status;
  },
  set: () => {
    // 這裡不直接設置，而是通過 updateOrderStatus 方法處理
  }
});

const getData = async () => {
  try {
    isLoading.value = true;

    const response = await OrderApi.getWCOrder(props.orderID);

    wcOrder.value = response.result;
    console.log(wcOrder.value);
  } finally {
    isLoading.value = false;
  }
};



// 更新訂單狀態
const updateOrderStatus = async (newStatus: string) => {
  // 確保狀態值不為空且有效
  if (!newStatus || typeof newStatus !== 'string' || newStatus.trim() === '') {
    console.error('Invalid status:', newStatus);
    return;
  }

  // 移除空白字符
  newStatus = newStatus.trim();

  if (newStatus === selectedStatus.value) {
    return;
  }

  // 如果選擇取消狀態，開啟取消對話框
  if (newStatus === 'cancelled') {
    orderCancelDialog.value?.openDialog();
    return;
  }

  dialog.showMessage({
    title: t('wcOrder.statusUpdate.title'),
    message: t('wcOrder.statusUpdate.confirm'),
    timeout: 0,
    persistent: true,
    ok: async () => {
      try {
        isLoading.value = true;
        await OrderApi.updateWCOrderStatus(props.orderID, newStatus);

        // 重新獲取訂單資料
        await getData();
      } finally {
        isLoading.value = false;
        emit('refresh');
      }
    },
  });
};

// 處理訂單取消
const handleOrderCancelled = async (cancelData: {
  orderId: number;
  reason: string;
  otherReason: string | null;
}) => {
  try {
    isLoading.value = true;

    // 準備客戶備註內容
    const customerNote = cancelData.reason === 'other' && cancelData.otherReason
      ? `${t('wcOrder.cancel.title')}: ${cancelData.otherReason}`
      : `${t('wcOrder.cancel.title')}: ${cancelData.reason}`;

    // 先更新客戶備註，再更新訂單狀態
    await OrderApi.updateWCOrderCustomerNote(props.orderID, customerNote);
    await OrderApi.updateWCOrderStatus(props.orderID, 'cancelled');

    // 重新獲取訂單資料
    await getData();
  } finally {
    isLoading.value = false;
    emit('refresh');
  }
};
</script>
