<!-- 現場訂單紀錄 -->
<template>
  <q-page>
    <q-card flat square bordered class="bg-cream">
      <q-card-section>
        <q-table
          virtual-scroll
          :rows="orders"
          :columns="columns"
          v-model:pagination="pagination"
          hide-pagination
          binary-state-sort
          table-header-class="bg-grey-3"
          @request="onRequest"
          :loading="isLoading"
        >
          <template v-slot:top>
            <q-toolbar>
              <q-toolbar-title>
                {{ t('orders') }} - {{ t('onsite') }}
              </q-toolbar-title>
            </q-toolbar>
            <!-- Date Picker -->
            <q-toolbar>
              <DateRangePicker
                v-model="dateRange"
                @update:model-value="getOrders"
              />
            </q-toolbar>
            <!-- search name -->
            <q-toolbar>
              <q-input
                v-model="search"
                outlined
                dense
                :placeholder="t('search.order')"
                clearable
                clear-icon="close"
                @keyup.enter.prevent="getOrders"
                @update:model-value="getOrders"
                input-debounce="500"
                style="width: 300px"
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </q-toolbar>

            <!-- Filter -->
            <q-toolbar>
              <!-- select customers -->
              <q-select
                v-model="customerUUID"
                :options="customerOptions"
                option-label="name"
                option-value="uuid"
                :label="t('customer.label')"
                emit-value
                map-options
                @update:model-value="getOrders"
              />

              <!-- select order status -->
              <q-select
                v-model="orderStatus"
                :options="statusOptions"
                option-label="label"
                option-value="value"
                :label="t('orderStatus.label')"
                emit-value
                map-options
                class="q-mx-md"
                style="width: 120px"
              />
            </q-toolbar>
          </template>

          <template v-slot:body="props">
            <q-tr :props="props" @click="showDetail(props.row.uuid)">
              <q-td :props="props" key="order_no">
                {{ props.row.order_no }}
              </q-td>
              <q-td :props="props" key="order_at">
                {{ formatDate(props.row.order_at, 'YYYY-MM-DD HH:mm') }}
              </q-td>
              <q-td :props="props" key="customer">
                <template v-if="props.row.customer.name">
                  {{ props.row.customer.name }}
                </template>
                <template v-else>
                  {{ t('unknown.customer') }}
                </template>
              </q-td>
              <q-td :props="props" key="total" class="text-bold">
                AU$ {{ props.row.total }}
              </q-td>
              <q-td :props="props" key="status" class="text-bold">
                {{ getOrderStatusLabel(props.row.status) }}
              </q-td>
            </q-tr>
          </template>
        </q-table>

        <TablePagination v-model="pagination" @getData="getOrders" />
      </q-card-section>
    </q-card>

    <OrderDetailDialog
      v-model="showDetailDialog"
      :orderID="selectedOrderID"
      @refresh="getOrders"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { CustomerApi } from '@/api/customer';
import { Order, OrderApi } from '@/api/order';
import {
  Pagination,
  TableRequestProps,
  getOrderStatusLabel,
  orderStatusOptions,
} from '@/types';
import { formatDate, formatNumber } from '@/utils';
import DateRangePicker from '@/components/DateRangePicker.vue';
import OrderDetailDialog from '@/components/OrderDetailDialog.vue';
import TablePagination from '@/components/TablePagination.vue';

const { t } = useI18n();

const orders = ref<Order[]>([]);
const dateRange = ref({
  from: '',
  to: '',
});
const columns = computed(() => [
  {
    name: 'order_no',
    label: t('orderNo'),
    field: 'order_no',
    align: 'center' as const,
  },
  {
    name: 'order_at',
    label: t('dateAt'),
    field: (row: Order) => formatDate(row.order_at, 'YYYY-MM-DD HH:mm'),
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'customer',
    label: t('customer.label'),
    field: (row: Order) => row.customer?.name || t('unknown.customer'),
    align: 'center' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: (row: Order) => formatNumber(row.total, 2),
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'status',
    label: t('status'),
    field: (row: Order) => t(row.status),
    align: 'center' as const,
    sortable: true,
  },
]);

const pagination = ref<Pagination>({
  sortBy: 'order_at',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

const isLoading = ref(false);
const search = ref('');
const customerUUID = ref('');
const orderStatus = ref('');
const statusOptions = computed(() => [
  {
    label: t('allStatus'),
    value: '',
  },
  ...orderStatusOptions.value,
]);

const getOrders = async () => {
  try {
    isLoading.value = true;
    const response = await OrderApi.fetch({
      filter: {
        search: search.value,
        customer_uuid: customerUUID.value,
        start_date: dateRange.value.from,
        end_date: dateRange.value.to,
        exclude_status: ['pending'],
      },
      pagination: pagination.value,
    });

    orders.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const customers = ref<
  {
    uuid: string;
    name: string;
  }[]
>([]);
const customerOptions = computed(() => [
  {
    uuid: '',
    name: t('customer.all'),
  },
  ...customers.value,
]);
const getCustomers = async () => {
  const response = await CustomerApi.fetch();

  for (let customer of response.result.data) {
    customers.value.push({
      uuid: customer.uuid,
      name: customer.name,
    });
  }
};

onMounted(() => {
  getOrders();
  getCustomers();
});

const showDetailDialog = ref(false);
const selectedOrderID = ref('');
const showDetail = (orderID: string) => {
  selectedOrderID.value = orderID;
  showDetailDialog.value = true;
};

const onRequest = (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  getOrders();
};
</script>
