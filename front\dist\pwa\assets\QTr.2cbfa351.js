import{L as a,c as e,h as s,Y as i}from"./index.09f89dc4.js";function c(t,r=0,o="en-US"){return t===void 0||(typeof t=="string"&&(t=parseFloat(t)),isNaN(t))?"":t.toLocaleString(o,{minimumFractionDigits:r,maximumFractionDigits:r})}var p=a({name:"QTr",props:{props:Object,noHover:Boolean},setup(t,{slots:r}){const o=e(()=>"q-tr"+(t.props===void 0||t.props.header===!0?"":" "+t.props.__trClass)+(t.noHover===!0?" q-tr--no-hover":""));return()=>s("tr",{class:o.value},i(r.default))}});export{p as Q,c as f};
