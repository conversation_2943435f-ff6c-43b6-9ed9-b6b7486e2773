<template>
  <div class="discount-options">
    <q-card-section>
      <div class="text-h6">{{ t('discount') }}</div>
      <div class="column">
        <div class="col">
          <NumberInput
            v-model="localOrder.rebate"
            :label="t('rebate')"
            :min="0"
            :step="0.01"
            :precision="2"
            prefix="AU$"
            @update:model-value="onRebateUpdate"
          />
        </div>
        <div class="col">
          <NumberInput
            v-model="localOrder.discount"
            :label="t('discount')"
            :max="100"
            :min="0"
            :step="0.01"
            :precision="2"
            append="percent"
            @update:model-value="onDiscountUpdate"
          />
        </div>
      </div>
    </q-card-section>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps<{
  rebate: number;
  discount: number;
}>();

const emit = defineEmits(['update:rebate', 'update:discount']);

const localOrder = ref<{
  rebate: number;
  discount: number;
}>({
  rebate: 0,
  discount: 0,
});

onMounted(() => {
  localOrder.value.rebate = props.rebate;
  localOrder.value.discount = props.discount;
});

watch(
  () => props.rebate,
  (value) => {
    localOrder.value.rebate = value;
  }
);

const onRebateUpdate = (value: number) => {
  emit('update:rebate', value);
};

watch(
  () => props.discount,
  (value) => {
    localOrder.value.discount = value;
  }
);

const onDiscountUpdate = (value: number) => {
  emit('update:discount', value);
};
</script>
