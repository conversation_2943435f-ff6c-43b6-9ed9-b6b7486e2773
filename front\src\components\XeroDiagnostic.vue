<template>
  <q-card class="q-ma-md">
    <q-card-section>
      <div class="text-h6">Xero 連接診斷</div>
      <div class="text-subtitle2 text-grey-7">
        診斷 Xero API 連接問題並提供解決方案
      </div>
    </q-card-section>

    <q-card-section>
      <q-btn
        color="primary"
        label="開始診斷"
        @click="runDiagnostic"
        :loading="diagnosing"
        :disable="diagnosing"
      />
    </q-card-section>

    <q-card-section v-if="diagnosticResults.length > 0">
      <div class="text-h6 q-mb-md">診斷結果</div>
      
      <q-timeline color="secondary">
        <q-timeline-entry
          v-for="(result, index) in diagnosticResults"
          :key="index"
          :title="result.title"
          :subtitle="result.subtitle"
          :icon="result.icon"
          :color="result.color"
        >
          <div v-html="result.description"></div>
          
          <q-btn
            v-if="result.action"
            :label="result.action.label"
            :color="result.action.color"
            @click="result.action.handler"
            class="q-mt-sm"
            size="sm"
          />
        </q-timeline-entry>
      </q-timeline>
    </q-card-section>

    <q-card-section v-if="showReconnectButton">
      <q-btn
        color="orange"
        label="重新連接 Xero"
        @click="reconnectXero"
        icon="refresh"
      />
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { XeroApi } from '@/api/xero';
import { useQuasar } from 'quasar';

interface DiagnosticResult {
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  color: string;
  action?: {
    label: string;
    color: string;
    handler: () => void;
  };
}

const $q = useQuasar();
const diagnosing = ref(false);
const diagnosticResults = ref<DiagnosticResult[]>([]);
const showReconnectButton = ref(false);

const runDiagnostic = async () => {
  diagnosing.value = true;
  diagnosticResults.value = [];
  showReconnectButton.value = false;

  try {
    // 步驟 1: 檢查配置
    await checkConfiguration();
    
    // 步驟 2: 檢查連接狀態
    await checkConnectionStatus();
    
    // 步驟 3: 驗證連接
    await validateConnection();
    
    // 步驟 4: 測試 API 調用
    await testApiCall();
    
  } catch (error) {
    console.error('Diagnostic error:', error);
  } finally {
    diagnosing.value = false;
  }
};

const checkConfiguration = async () => {
  try {
    const response = await XeroApi.getConfig();
    
    if (response.result) {
      diagnosticResults.value.push({
        title: '配置檢查',
        subtitle: '✓ 通過',
        description: 'Xero 應用配置已正確設置',
        icon: 'check_circle',
        color: 'positive'
      });
    }
  } catch (error: any) {
    diagnosticResults.value.push({
      title: '配置檢查',
      subtitle: '✗ 失敗',
      description: `配置檢查失敗: ${error.message || '未知錯誤'}`,
      icon: 'error',
      color: 'negative'
    });
  }
};

const checkConnectionStatus = async () => {
  try {
    const response = await XeroApi.getConnectionStatus();
    
    if (response.result.connected) {
      diagnosticResults.value.push({
        title: '連接狀態',
        subtitle: '✓ 已連接',
        description: `已連接到組織: ${response.result.tenant_name}<br>到期時間: ${new Date(response.result.expires_at).toLocaleString()}`,
        icon: 'link',
        color: 'positive'
      });
    } else {
      diagnosticResults.value.push({
        title: '連接狀態',
        subtitle: '✗ 未連接',
        description: 'Xero 未連接，需要重新授權',
        icon: 'link_off',
        color: 'negative'
      });
      showReconnectButton.value = true;
    }
  } catch (error: any) {
    diagnosticResults.value.push({
      title: '連接狀態',
      subtitle: '✗ 檢查失敗',
      description: `無法檢查連接狀態: ${error.message || '未知錯誤'}`,
      icon: 'error',
      color: 'negative'
    });
  }
};

const validateConnection = async () => {
  try {
    await XeroApi.validateConnection();
    
    diagnosticResults.value.push({
      title: '連接驗證',
      subtitle: '✓ 有效',
      description: 'Xero API 連接驗證成功',
      icon: 'verified',
      color: 'positive'
    });
  } catch (error: any) {
    const errorMessage = error.response?.data?.error || error.message || '未知錯誤';
    
    if (errorMessage.includes('403') || errorMessage.includes('forbidden')) {
      diagnosticResults.value.push({
        title: '連接驗證',
        subtitle: '✗ 權限被拒絕',
        description: `
          <strong>403 錯誤 - 權限被拒絕</strong><br>
          可能的原因：<br>
          • Token 已被撤銷或過期<br>
          • 應用權限不足<br>
          • 組織 ID 不匹配<br>
          • 應用未正確連接到組織
        `,
        icon: 'block',
        color: 'negative',
        action: {
          label: '嘗試刷新 Token',
          color: 'orange',
          handler: refreshToken
        }
      });
      showReconnectButton.value = true;
    } else {
      diagnosticResults.value.push({
        title: '連接驗證',
        subtitle: '✗ 失敗',
        description: `連接驗證失敗: ${errorMessage}`,
        icon: 'error',
        color: 'negative'
      });
    }
  }
};

const testApiCall = async () => {
  try {
    await XeroApi.getInvoices({ page: 1, pageSize: 1 });
    
    diagnosticResults.value.push({
      title: 'API 測試',
      subtitle: '✓ 成功',
      description: 'Xero API 調用測試成功',
      icon: 'api',
      color: 'positive'
    });
  } catch (error: any) {
    const errorMessage = error.response?.data?.error || error.message || '未知錯誤';
    
    diagnosticResults.value.push({
      title: 'API 測試',
      subtitle: '✗ 失敗',
      description: `API 調用失敗: ${errorMessage}`,
      icon: 'api',
      color: 'negative'
    });
  }
};

const refreshToken = async () => {
  try {
    await XeroApi.refreshToken();
    
    $q.notify({
      type: 'positive',
      message: 'Token 刷新成功，請重新運行診斷'
    });
    
    // 重新運行診斷
    setTimeout(() => {
      runDiagnostic();
    }, 1000);
    
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Token 刷新失敗: ${error.response?.data?.message || error.message}`
    });
  }
};

const reconnectXero = async () => {
  try {
    const response = await XeroApi.getAuthURL();
    window.open(response.result.auth_url, '_blank');
    
    $q.notify({
      type: 'info',
      message: '請在新窗口中完成 Xero 授權，然後重新運行診斷'
    });
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `無法獲取授權 URL: ${error.response?.data?.message || error.message}`
    });
  }
};
</script>
