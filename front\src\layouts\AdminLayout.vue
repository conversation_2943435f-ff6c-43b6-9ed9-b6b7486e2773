<template>
  <q-layout view="hHr Lpr lFr" class="shadow-2 rounded-borders">
    <q-header elevated>
      <q-toolbar>
        <q-btn
          flat
          round
          dense
          icon="menu"
          aria-label="Menu"
          @click="toggleMenu"
        />
        <q-toolbar-title>{{ pageInfo.pageTitle }}</q-toolbar-title>
        <!-- <q-btn flat round dense icon="notifications_none" /> -->
        <q-btn flat to="/order" size="sm" class="q-pa-sm">
          <div class="text-subtitle1">POS</div>
        </q-btn>
        <LanguageSwitcher />
        <span class="q-mr-sm">{{ authStore.getUserName }}</span>
        <q-btn flat round dense icon="logout" @click="logout" />
      </q-toolbar>
    </q-header>

    <q-drawer v-model="menuOpen" bordered overlay>
      <MenuLink v-model="linkList" />
    </q-drawer>

    <q-page-container style="background-color: #fef9f2">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import MenuLink from './components/Menu.vue';
import { AuthApi } from '@/api/auth';
import { useAuthStore } from '@/stores/auth-store';
import { usePageInfoStore } from '@/stores/pageInfo';
import { useDialog } from '@/utils';

const router = useRouter();
const { t } = useI18n();

const pageInfo = usePageInfoStore();
const authStore = useAuthStore();
const dialog = useDialog();

const menuOpen = ref(false);

const linkList = computed(() => [
  {
    title: t('user.menu'),
    icon: 'people',
    subMenus: [
      {
        title: t('user.menu'),
        link: '/admin/dashboard/user',
      },
      {
        title: t('attendance.history'),
        link: '/admin/dashboard/attendance',
      },
      {
        title: t('payroll.label'),
        link: '/admin/dashboard/payroll',
      },
    ],
  },
  {
    title: t('customers'),
    icon: 'person',
    link: '/admin/dashboard/customer',
  },
  {
    title: t('product.label'),
    icon: 'sym_o_local_mall',
    subMenus: [
      {
        title: t('product.label'),
        link: '/admin/dashboard/catalog/product',
      },
      // {
      //   title: t('inventory.stockHistory'),
      //   link: '/admin/dashboard/catalog/stock-history',
      // },
    ],
  },
  {
    title: t('order.label'),
    icon: 'shopping_cart',
    subMenus: [
      {
        title: t('onsite'),
        link: '/admin/dashboard/order/onsite',
      },
      {
        title: t('online'),
        link: '/admin/dashboard/order/online',
      },
    ],
  },
  {
    title: t('system'),
    icon: 'settings',
    subMenus: [
      {
        title: t('system'),
        link: '/admin/dashboard/system',
      },
      {
        title: t('announcement.settings'),
        link: '/admin/dashboard/announcement',
      },
    ],
  },
  {
    title: 'Xero',
    icon: 'sym_o_account_balance',
    subMenus: [
      {
        title: t('xero.menu.setup'),
        link: '/admin/dashboard/xero/setup',
      },
      {
        title: t('xero.menu.invoices'),
        link: '/admin/dashboard/xero/invoices',
      },
    ],
  },
]);

const toggleMenu = () => {
  menuOpen.value = !menuOpen.value;
};

const logout = () => {
  dialog.showMessage({
    message: t('logoutConfirm'),
    timeout: 0,
    ok: async () => {
      await AuthApi.logout();
      authStore.logout();
      router.push('/login');
    },
  });
};
</script>
