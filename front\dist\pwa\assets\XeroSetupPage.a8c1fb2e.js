import{d as E,aE as C,r as c,c as T,o as j,p as $,v as h,m as s,t as r,j as o,G as x,A as f,q as S,y as p,B as _,C as z,Q as m,H as N}from"./index.09f89dc4.js";import{Q as k}from"./QBanner.ce4d7588.js";import{Q as P}from"./QForm.c51f3f04.js";import{Q as R}from"./QPage.ce1b4cb5.js";import{u as A}from"./vue-i18n.1783a0cb.js";import{u as D}from"./use-quasar.3b603a60.js";import{X as u}from"./xero.34271ea4.js";import{f as F}from"./date.6d29930c.js";const H={class:"row justify-center"},O={class:"col-12 col-md-8 col-lg-6"},M={class:"text-h6 q-mb-md"},G={class:"row q-gutter-sm"},le=E({__name:"XeroSetupPage",setup(L){const{t:n}=A(),i=D(),l=C({client_id:"",client_secret:"",redirect_uri:window.location.origin+"/xero/redirect",scopes:"accounting.transactions accounting.contacts",default_email:""}),d=C({connected:!1,tenant_name:"",tenant_id:"",expires_at:""}),g=c(!1),y=c(!1),v=c(!1),b=c(!1),Q=c(!1),q=T(()=>l.client_id&&l.client_secret&&l.redirect_uri),I=async()=>{const e=await u.getConfig();e.result&&Object.assign(l,e.result)},w=async()=>{const e=await u.getConnectionStatus();Object.assign(d,e.result)},V=async()=>{g.value=!0;try{await u.saveConfig(l),i.notify({position:"top",type:"positive",message:n("success")})}finally{g.value=!1}},U=async()=>{y.value=!0;try{await V();const e=await u.getAuthURL(),{auth_url:a,state:t}=e.result;localStorage.setItem("xero_oauth_state",t),localStorage.getItem("xero_oauth_state")!==t&&(console.error("Failed to save state to localStorage"),i.notify({position:"top",type:"warning",message:n("xero.setup.error.stateStorageFailed")})),window.location.href=a}catch(e){console.error("Connect to Xero error:",e),i.notify({position:"top",type:"negative",message:(e==null?void 0:e.message)||n("failed")})}finally{y.value=!1}},X=async()=>{v.value=!0;try{await u.refreshToken(),await w(),i.notify({position:"top",type:"positive",message:n("success")})}finally{v.value=!1}},B=async()=>{i.dialog({title:n("xero.setup.dialog.disconnectTitle"),message:n("xero.setup.dialog.disconnectMessage"),cancel:!0,persistent:!0}).onOk(async()=>{b.value=!0;try{await u.disconnect(),await w(),i.notify({position:"top",type:"positive",message:n("success")})}finally{b.value=!1}})};return j(()=>{I(),w()}),(e,a)=>($(),h(R,{class:"q-pa-md"},{default:s(()=>[r("div",H,[r("div",O,[o(N,{class:"q-pa-md"},{default:s(()=>[o(x,null,{default:s(()=>[r("div",M,f(e.$t("xero.setup.title")),1),d.connected?($(),h(k,{key:0,class:"bg-positive text-white q-mb-md",rounded:""},{avatar:s(()=>[o(S,{name:"check_circle",color:"white"})]),action:s(()=>[o(p,{flat:"",color:"white",label:e.$t("xero.setup.connectionStatus.disconnect"),onClick:B,loading:b.value},null,8,["label","loading"]),o(p,{flat:"",color:"white",label:e.$t("xero.setup.connectionStatus.refreshToken"),onClick:X,loading:v.value},null,8,["label","loading"])]),default:s(()=>[_(" "+f(e.$t("xero.setup.connectionStatus.connected",{tenantName:d.tenant_name}))+" ",1),a[6]||(a[6]=r("br",null,null,-1)),r("small",null,f(e.$t("xero.setup.connectionStatus.tokenExpiry",{expiryDate:z(F)(d.expires_at)})),1)]),_:1})):($(),h(k,{key:1,class:"bg-warning text-dark q-mb-md",rounded:""},{avatar:s(()=>[o(S,{name:"warning",color:"orange"})]),default:s(()=>[_(" "+f(e.$t("xero.setup.connectionStatus.notConnected")),1)]),_:1}))]),_:1}),o(x,null,{default:s(()=>[o(P,{onSubmit:V,class:"q-gutter-md"},{default:s(()=>[o(m,{modelValue:l.client_id,"onUpdate:modelValue":a[0]||(a[0]=t=>l.client_id=t),filled:"",label:e.$t("xero.setup.form.clientId")+" *","lazy-rules":"",rules:[t=>t&&t.length>0||e.$t("xero.setup.validation.clientIdRequired")]},null,8,["modelValue","label","rules"]),o(m,{modelValue:l.client_secret,"onUpdate:modelValue":a[1]||(a[1]=t=>l.client_secret=t),filled:"",type:"password",label:e.$t("xero.setup.form.clientSecret")+" *","lazy-rules":"",rules:[t=>t&&t.length>0||e.$t("xero.setup.validation.clientSecretRequired")]},null,8,["modelValue","label","rules"]),o(m,{modelValue:l.redirect_uri,"onUpdate:modelValue":a[2]||(a[2]=t=>l.redirect_uri=t),filled:"",label:e.$t("xero.setup.form.redirectUri")+" *","lazy-rules":"",rules:[t=>t&&t.length>0||e.$t("xero.setup.validation.redirectUriRequired")]},null,8,["modelValue","label","rules"]),o(m,{modelValue:l.scopes,"onUpdate:modelValue":a[4]||(a[4]=t=>l.scopes=t),filled:"",label:e.$t("xero.setup.form.scopes"),hint:e.$t("xero.setup.form.scopesHint"),placeholder:e.$t("xero.setup.form.scopesPlaceholder")},{append:s(()=>[o(p,{flat:"",round:"",dense:"",icon:"info",onClick:a[3]||(a[3]=t=>Q.value=!0)})]),_:1},8,["modelValue","label","hint","placeholder"]),o(m,{modelValue:l.default_email,"onUpdate:modelValue":a[5]||(a[5]=t=>l.default_email=t),filled:"",type:"email",label:e.$t("xero.setup.form.defaultEmail"),hint:e.$t("xero.setup.form.defaultEmailHint"),placeholder:e.$t("xero.setup.form.defaultEmailPlaceholder"),"lazy-rules":"",rules:[t=>!t||/.+@.+\..+/.test(t)||e.$t("xero.setup.validation.invalidEmail")]},{prepend:s(()=>[o(S,{name:"email"})]),_:1},8,["modelValue","label","hint","placeholder","rules"]),r("div",G,[o(p,{label:e.$t("save"),type:"submit",color:"primary",loading:g.value},null,8,["label","loading"]),o(p,{label:e.$t("xero.setup.form.connectXero"),color:"secondary",disable:!q.value||d.connected,onClick:U,loading:y.value},null,8,["label","disable","loading"])])]),_:1})]),_:1})]),_:1})])])]),_:1}))}});export{le as default};
