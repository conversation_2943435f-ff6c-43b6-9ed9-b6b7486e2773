import{Q as J}from"./QSpace.2ea7fb32.js";import{r as V,c as U,p as y,v as q,m as l,j as e,H as G,G as N,t as $,A as t,bB as X,Q as Z,x as R,aK as T,y as A,E as j,b2 as F,d as ee,w as le,C as c,B as n,q as g,aV as ae,k as te,u as se,F as oe}from"./index.09f89dc4.js";import{Q as i,a}from"./QItemSection.3e7b5a38.js";import{Q as re}from"./QSelect.958fa87e.js";import{Q as H}from"./QItemLabel.88180eb1.js";import{Q as de}from"./QList.5d1c2d4f.js";import{Q as ne}from"./QScrollArea.e7fd209f.js";import{u as K}from"./vue-i18n.1783a0cb.js";import{O as S}from"./order.7fd0b308.js";import{f as ue}from"./date.6d29930c.js";import{f as z}from"./QTr.2cbfa351.js";import{u as ce}from"./dialog.27403fe4.js";const ie={class:"text-h6"},me={__name:"WCOrderCancelDialog",props:{orderId:{type:Number,required:!0},otherReason:{type:String,default:""}},emits:["order-cancelled"],setup(B,{expose:L,emit:r}){const{t:m}=K(),D=B,h=r,O=V(!1),p=V(""),f=V(""),d=V(!1),k=U(()=>[{label:m("wcOrder.cancel.reasons.outOfStock"),value:m("wcOrder.cancel.reasons.outOfStock")},{label:m("wcOrder.cancel.reasons.customerRequest"),value:m("wcOrder.cancel.reasons.customerRequest")},{label:m("wcOrder.cancel.reasons.paymentIssue"),value:m("wcOrder.cancel.reasons.paymentIssue")},{label:m("wcOrder.cancel.reasons.duplicateOrder"),value:m("wcOrder.cancel.reasons.duplicateOrder")},{label:m("wcOrder.cancel.reasons.incorrectInfo"),value:m("wcOrder.cancel.reasons.incorrectInfo")},{label:m("wcOrder.cancel.reasons.other"),value:"other"}]),W=()=>{O.value=!0,p.value="",f.value=D.otherReason||"",D.otherReason?p.value="other":p.value=k.value[0].value},C=()=>{O.value=!1,d.value=!1},Q=()=>{if(!p.value){F.create({type:"warning",position:"top",message:m("wcOrder.cancel.reasonRequired")});return}if(p.value==="other"&&f.value===""){F.create({type:"warning",position:"top",message:m("wcOrder.cancel.reasonRequired")});return}const b={orderId:D.orderId,reason:p.value,otherReason:p.value==="other"?f.value:null};h("order-cancelled",b),C()};return L({openDialog:W}),(b,w)=>(y(),q(j,{modelValue:O.value,"onUpdate:modelValue":w[2]||(w[2]=s=>O.value=s),persistent:"","no-refocus":""},{default:l(()=>[e(G,{style:{"min-width":"350px"}},{default:l(()=>[e(N,null,{default:l(()=>[$("div",ie,t(b.$t("wcOrder.cancel.title")),1)]),_:1}),e(N,null,{default:l(()=>[e(X,{modelValue:p.value,"onUpdate:modelValue":w[0]||(w[0]=s=>p.value=s),options:k.value,type:"radio"},null,8,["modelValue","options"]),p.value==="other"?(y(),q(Z,{key:0,modelValue:f.value,"onUpdate:modelValue":w[1]||(w[1]=s=>f.value=s),modelModifiers:{trim:!0},type:"textarea",label:b.$t("wcOrder.cancel.reasonLabel"),placeholder:b.$t("wcOrder.cancel.reasonPlaceholder"),class:"q-mt-md",outlined:"",rules:[s=>s.length>0||b.$t("wcOrder.cancel.reasonRequired")]},null,8,["modelValue","label","placeholder","rules"])):R("",!0)]),_:1}),e(T,{align:"right"},{default:l(()=>[e(A,{flat:"",label:b.$t("cancel"),color:"primary",onClick:C},null,8,["label"]),e(A,{label:b.$t("confirm"),color:"negative",onClick:Q,loading:d.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}},pe={class:"row q-mt-sm"},fe={class:"text-h5 text-bold"},_e={class:"row q-mb-sm"},ve={class:"text-red"},Qe=ee({__name:"WCOrderDetailDialog",props:{modelValue:{type:Boolean},orderID:{}},emits:["update:modelValue","refresh"],setup(B,{emit:L}){const{t:r}=K(),m=ce(),D=(s,u)=>!s&&!u?r("unknown"):u!=null&&u.includes("Parcel")?"Parcel Post":u!=null&&u.includes("Express")?"Express Post":u||s||r("unknown"),h=B,O=L,p=U({get:()=>h.modelValue,set:s=>O("update:modelValue",s)});le(()=>h.modelValue,s=>{s&&Q()});const f=V(!1),d=V(),k=V(),W=U(()=>[{label:r("orderStatus.processing"),value:"processing"},{label:r("orderStatus.on-hold"),value:"on-hold"},{label:r("orderStatus.packing"),value:"packing"},{label:r("orderStatus.shipping"),value:"shipping"},{label:r("orderStatus.completed"),value:"completed"},{label:r("orderStatus.cancelled"),value:"cancelled"},{label:r("orderStatus.refunded"),value:"refunded"}]),C=U({get:()=>{var u;const s=(u=d.value)==null?void 0:u.status;return s?s.startsWith("wc-")?s.substring(3):s:""},set:()=>{}}),Q=async()=>{try{f.value=!0;const s=await S.getWCOrder(h.orderID);d.value=s.result,console.log(d.value)}finally{f.value=!1}},b=async s=>{var u;if(!s||typeof s!="string"||s.trim()===""){console.error("Invalid status:",s);return}if(s=s.trim(),s!==C.value){if(s==="cancelled"){(u=k.value)==null||u.openDialog();return}m.showMessage({title:r("wcOrder.statusUpdate.title"),message:r("wcOrder.statusUpdate.confirm"),timeout:0,persistent:!0,ok:async()=>{try{f.value=!0,await S.updateWCOrderStatus(h.orderID,s),await Q()}finally{f.value=!1,O("refresh")}}})}},w=async s=>{try{f.value=!0;const u=s.reason==="other"&&s.otherReason?`${r("wcOrder.cancel.title")}: ${s.otherReason}`:`${r("wcOrder.cancel.title")}: ${s.reason}`;await S.updateWCOrderCustomerNote(h.orderID,u),await S.updateWCOrderStatus(h.orderID,"cancelled"),await Q()}finally{f.value=!1,O("refresh")}};return(s,u)=>(y(),q(j,{modelValue:p.value,"onUpdate:modelValue":u[3]||(u[3]=x=>p.value=x),class:"card-dialog","no-refocus":""},{default:l(()=>[e(G,{class:"column"},{default:l(()=>[e(N,{class:"col-1 q-py-none"},{default:l(()=>[$("div",pe,[$("div",fe,t(c(r)("orderDetail")),1),e(J),e(A,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:u[0]||(u[0]=x=>p.value=!1)})])]),_:1}),e(N,{class:"col-10 text-h6 q-py-sm"},{default:l(()=>[e(ne,{class:"full-height"},{default:l(()=>{var x,M,I,P;return[$("div",_e,[e(i,{class:"col-12"},{default:l(()=>[e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("orderNo")),1)]),_:1}),e(a,null,{default:l(()=>{var o;return[n(t((o=d.value)==null?void 0:o.id),1)]}),_:1})]),_:1}),e(i,{class:"col-12 col-md-6"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"event",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("orderDate")),1)]),_:1}),e(a,null,{default:l(()=>{var o;return[n(t(c(ue)((o=d.value)==null?void 0:o.date_created,"YYYY-MM-DD HH:mm")),1)]}),_:1})]),_:1}),e(i,{class:"col-12 col-md-6"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"person",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("customer.label")),1)]),_:1}),e(a,null,{default:l(()=>{var o,_;return[n(t((o=d.value)==null?void 0:o.billing_first_name)+" "+t((_=d.value)==null?void 0:_.billing_last_name),1)]}),_:1})]),_:1}),e(i,{class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"email",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("email.label")),1)]),_:1}),e(a,null,{default:l(()=>{var o;return[n(t((o=d.value)==null?void 0:o.billing_email),1)]}),_:1})]),_:1}),e(i,{class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"phone",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("phone")),1)]),_:1}),e(a,null,{default:l(()=>{var o;return[n(t((o=d.value)==null?void 0:o.billing_phone),1)]}),_:1})]),_:1}),e(i,{class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"check_circle",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("status")),1)]),_:1}),e(a,null,{default:l(()=>[e(re,{modelValue:C.value,"onUpdate:modelValue":[u[1]||(u[1]=o=>C.value=o),b],options:W.value,"option-value":"value","option-label":"label","emit-value":"","map-options":"",outlined:"",dense:"",loading:f.value},null,8,["modelValue","options","loading"])]),_:1})]),_:1}),e(i,{class:"col-12 col-md-6"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"payment",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("payment.label")),1)]),_:1}),e(a,null,{default:l(()=>{var o;return[n(t((o=d.value)==null?void 0:o.payment_method_title),1)]}),_:1})]),_:1}),e(i,{class:"col-12 col-md-6"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"attach_money",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("total")),1)]),_:1}),e(a,null,{default:l(()=>{var o;return[n(" AU$ "+t(c(z)((o=d.value)==null?void 0:o.total,2)),1)]}),_:1})]),_:1}),e(i,{class:"col-12"},{default:l(()=>[e(a,null,{default:l(()=>[e(ae,{color:"black",size:"2px"})]),_:1})]),_:1}),e(i,{class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"person",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("receiver")),1)]),_:1}),e(a,null,{default:l(()=>{var o,_;return[n(t((o=d.value)==null?void 0:o.shipping_first_name)+" "+t((_=d.value)==null?void 0:_.shipping_last_name),1)]}),_:1})]),_:1}),((x=d.value)==null?void 0:x.shipping_method)||((M=d.value)==null?void 0:M.shipping_method_title)?(y(),q(i,{key:0,class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"local_shipping",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("shippingMethod")),1)]),_:1}),e(a,null,{default:l(()=>{var o,_;return[n(t(D((o=d.value)==null?void 0:o.shipping_method,(_=d.value)==null?void 0:_.shipping_method_title)),1)]}),_:1})]),_:1})):R("",!0),e(i,{class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"location_on",size:"sm"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("address")),1)]),_:1}),e(a,null,{default:l(()=>{var o,_,v,E,Y;return[n(t((o=d.value)==null?void 0:o.shipping_address_1)+", "+t((_=d.value)==null?void 0:_.shipping_city)+", "+t((v=d.value)==null?void 0:v.shipping_state)+", "+t((E=d.value)==null?void 0:E.shipping_postcode)+", "+t((Y=d.value)==null?void 0:Y.shipping_country),1)]}),_:1})]),_:1}),((I=d.value)==null?void 0:I.status)==="wc-cancelled"&&((P=d.value)==null?void 0:P.customer_note)?(y(),q(i,{key:1,class:"col-12"},{default:l(()=>[e(a,{side:""},{default:l(()=>[e(g,{name:"cancel",size:"sm",color:"red"})]),_:1}),e(a,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[n(t(c(r)("wcOrder.cancel.reasonLabel")),1)]),_:1}),e(a,null,{default:l(()=>[$("span",ve,t(d.value.customer_note),1)]),_:1})]),_:1})):R("",!0)]),e(de,{bordered:"",separator:""},{default:l(()=>{var o,_;return[e(i,{class:"bg-grey-3"},{default:l(()=>[e(a,{class:"text-bold"},{default:l(()=>[n(t(c(r)("product.label")),1)]),_:1}),e(a,{class:"text-bold",side:""},{default:l(()=>[n(t(c(r)("price")),1)]),_:1})]),_:1}),(y(!0),te(oe,null,se((o=d.value)==null?void 0:o.line_items,v=>(y(),q(i,{key:v.name},{default:l(()=>[e(a,null,{default:l(()=>[n(t(v.name),1)]),_:2},1024),e(a,{class:"text-subtitle1",side:""},{default:l(()=>[n(" x "+t(v.quantity),1)]),_:2},1024),e(a,{class:"text-bold",side:""},{default:l(()=>[n(" AU$ "+t(c(z)(v.total,2)),1)]),_:2},1024)]),_:2},1024))),128)),(_=d.value)!=null&&_.shipping_total?(y(),q(i,{key:0},{default:l(()=>[e(a,null,{default:l(()=>[n(t(c(r)("shippingFee")),1)]),_:1}),e(a,{class:"text-bold",side:""},{default:l(()=>{var v;return[n(" AU$ "+t(c(z)((v=d.value)==null?void 0:v.shipping_total,2)),1)]}),_:1})]),_:1})):R("",!0),e(i,{class:"bg-grey-3"},{default:l(()=>[e(a,null,{default:l(()=>[e(H,{class:"text-bold"},{default:l(()=>[n(t(c(r)("total")),1)]),_:1})]),_:1}),e(a,{side:""},{default:l(()=>[e(H,{class:"text-bold"},{default:l(()=>{var v;return[n(" AU$ "+t(c(z)((v=d.value)==null?void 0:v.total,2)),1)]}),_:1})]),_:1})]),_:1})]}),_:1})]}),_:1})]),_:1}),e(T,{align:"between",class:"col-1 bg-grey-2 q-pa-sm"},{default:l(()=>{var x;return[((x=d.value)==null?void 0:x.status)!="wc-cancelled"?(y(),q(A,{key:0,color:"red",icon:"delete",label:c(r)("wpOrder.actions.cancel"),"no-caps":"",onClick:u[2]||(u[2]=M=>{var I;return(I=k.value)==null?void 0:I.openDialog()}),loading:f.value},null,8,["label","loading"])):R("",!0)]}),_:1})]),_:1}),e(me,{ref_key:"orderCancelDialog",ref:k,"order-id":h.orderID,onOrderCancelled:w},null,8,["order-id"])]),_:1},8,["modelValue"]))}});export{Qe as _};
