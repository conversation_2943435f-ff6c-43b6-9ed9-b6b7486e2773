<template>
  <q-dialog v-model="visible" class="card-dialog" no-refocus>
    <q-card class="column">
      <!-- header -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-pt-sm">
          <!-- title -->
          <div class="text-h5 text-bold">
            {{ t('history') }}
          </div>
          <q-space />
          <!-- close -->
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="visible = false"
          />
        </div>
      </q-card-section>

      <q-card-section class="col-11 q-pt-lg">
        <q-table
          virtual-scroll
          :rows="items"
          :columns="columns"
          row-key="uuid"
          :rows-per-page-options="[20]"
          class="sticky-scroll-table full-height"
        >
          <template v-slot:body="props">
            <q-tr
              :props="props"
              @click="showOrderDetail(props.row.id)"
              :class="getOrderStatusColor(props.row.status)"
            >
              <q-td :props="props" key="id">
                {{ props.row.id }}
              </q-td>
              <q-td :props="props" key="date_created">
                {{ formatDate(props.row.date_created, 'YYYY-MM-DD HH:mm') }}
              </q-td>
              <q-td :props="props" key="customer">
                <template v-if="props.row.customer_name">
                  {{ props.row.customer_name }}
                </template>
                <template v-else>
                  {{ t('unknown.customer') }}
                </template>
              </q-td>
              <q-td :props="props" key="total" class="text-bold">
                AU$ {{ formatNumber(props.row.total, 2) }}
              </q-td>
              <q-td :props="props" key="status" class="text-bold">
                {{ getOrderStatusLabel(props.row.status) }}
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-dialog>

  <WCOrderDetailDialog
    v-model="showOrderDetailDialog"
    :orderID="wcOrderID"
    @refresh="refreshData"
  />
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { OrderApi, WCOrderInfo } from '@/api/order';
import { getOrderStatusColor, getOrderStatusLabel } from '@/types/order';
import { formatDate, formatNumber } from '@/utils';
import WCOrderDetailDialog from './WCOrderDetailDialog.vue';

const { t } = useI18n();

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits(['update:modelValue', 'refresh']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const columns = computed(() => [
  {
    label: t('orderNo'),
    name: 'id',
    field: 'id',
    align: 'left' as const,
  },
  {
    name: 'date_created',
    label: t('orderDate'),
    field: 'date_created',
    align: 'left' as const,
  },
  {
    name: 'customer',
    label: t('customer.label'),
    field: 'customer',
    align: 'left' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'left' as const,
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'left' as const,
  },
]);

const items = ref<WCOrderInfo[]>([]);
const fetchData = async () => {
    const response = await OrderApi.listWCHistory();
    items.value = response.result.data;
};

const refreshData = () => {
  fetchData();
  emit('refresh');
};

onMounted(() => {
  fetchData();
});

const showOrderDetailDialog = ref(false);
const wcOrderID = ref(0);
const showOrderDetail = (orderID: number) => {
  wcOrderID.value = orderID;
  showOrderDetailDialog.value = true;
};
</script>
