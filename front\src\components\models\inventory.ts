import { CustomerInfo } from '@/api/customer';
import { Product } from '@/api/product';

export interface InventoryForm {
  title: string; // 標題
  type: string; // 類型
  uuid: string;
  no_number: string; //  編號
  form_date: string; // 日期
  order_no: string; // 訂單編號
  return_type?: string; // 退貨類型
  customer: CustomerInfo | null; // 客戶資訊
  is_supplier?: boolean; // 是否供應商
  status: string; // 狀態
  notes: string; // 備註
  items: {
    product: Product | null;
    quantity: number;
  }[]; // 項目
}
