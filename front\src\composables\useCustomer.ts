import { ref } from 'vue';
import { Customer } from '@/api/customer';

export function useCustomer() {
  const customer = ref<Customer>({
    uuid: '',
    name: '',
    email: '',
    phone: '',
    license_plate: '',
    tax_id: '',
    country: '',
    state: '',
    city: '',
    zipcode: '',
    address: '',
    is_supplier: false,
    is_vip: false,
    vip_end_date: null,
    is_active: true,
    note: '',

    products: [],
  });

  const newCustomer = (data: Customer = <Customer>{}) => {
    return { ...customer.value, ...data };
  };

  return {
    newCustomer,
  };
}
