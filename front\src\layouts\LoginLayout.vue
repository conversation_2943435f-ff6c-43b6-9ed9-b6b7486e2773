<template>
  <q-layout view="hHh LpR fFf" class="bg-main">
    <q-header class="bg-main text-center q-py-sm">
      <q-toolbar>
        <q-toolbar-title>
          <q-img src="~assets/logo.svg" style="width: 12rem" />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container>
      <router-view />
    </q-page-container>

    <q-footer bordered class="bg-footer text-footer q-py-md">
      <div class="q-mx-auto" style="max-width: min(100%, 40rem)">
        <div
          class="row q-pb-xs q-px-xs"
          style="border-bottom: 1pt solid #e8e8ec"
        >
          <div class="col">
            <q-img
              src="~assets/footer_logo.svg"
              class="q-mr-xs"
              style="width: 1.5rem"
            />
            <span>CHENGXING SOFT</span>
          </div>
          <div class="col text-right">客服專線: 0800-123123</div>
        </div>

        <div class="row q-px-xs">
          <div class="col q-pt-xs">
            Copyright © 2023 CHENGXING SOFT. All Rights Reserved.
          </div>
        </div>
      </div>
    </q-footer>
  </q-layout>
</template>

<script lang="ts">
import {
  fasSignal,
  fasWifi,
  fasBatteryFull,
} from '@quasar/extras/fontawesome-v6';

export default {
  setup() {
    return {
      fasSignal,
      fasWifi,
      fasBatteryFull,
    };
  },
};
</script>

<style lang="scss">
.bg-footer {
  background-color: #789dbc;
}

.text-footer {
  color: #e8e8ec;
}
</style>
