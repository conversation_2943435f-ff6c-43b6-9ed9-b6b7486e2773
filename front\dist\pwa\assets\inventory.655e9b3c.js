import{bk as t}from"./index.09f89dc4.js";const a={listProductStocks:({filter:r,pagination:e})=>t.get("/v1/inventory/products",{params:{...r,...e}}),listTransactions:({filter:r,pagination:e})=>t.get("/v1/inventory/transactions",{params:{...r,...e}})},c={listPurchaseOrders:()=>t.get("/v1/inventory/purchase-orders"),getPurchaseOrder:r=>t.get(`/v1/inventory/purchase-orders/${r}`),createPurchaseOrder:r=>t.post("/v1/purchase-orders",r)},o={listReturnOrders:()=>t.get("/v1/return-orders"),getReturnOrder:r=>t.get(`/v1/return-orders/${r}`),createReturnOrder:r=>t.post("/v1/return-orders",r)},n={listScrapStocks:()=>t.get("/v1/scraps"),getScrapStock:r=>t.get(`/v1/scraps/${r}`),createScrapStock:r=>t.post("/v1/scraps",r)},p={listStocktaking:()=>t.get("/v1/stocktaking"),getStocktaking:r=>t.get(`/v1/stocktaking/${r}`),createStocktaking:r=>t.post("/v1/stocktaking",r)};export{a as I,c as P,o as R,n as S,p as a};
