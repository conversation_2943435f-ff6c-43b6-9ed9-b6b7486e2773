<template>
  <div class="stock-page q-pa-md">
    <div class="text-center q-mb-lg">
      <h2 class="text-h4 page-title q-my-md">庫存管理</h2>
    </div>

    <div class="content-container q-mx-auto">
      <!-- 搜尋欄 -->
      <div class="search-section q-mb-md">
        <q-input
          v-model="searchText"
          filled
          dense
          label="搜尋商品"
          class="search-input"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </div>

      <!-- 功能按鈕區 -->
      <div class="action-buttons q-mb-lg q-mt-md">
        <q-btn
          color="primary"
          icon="add"
          label="新增庫存"
          @click="openAddDialog"
        />
        <q-btn
          color="purple"
          icon="qr_code_scanner"
          label="掃描商品"
          @click="openScanner"
        />
        <q-btn
          color="secondary"
          icon="refresh"
          label="更新"
          @click="refreshData"
        />
      </div>

      <!-- 掃描對話框 -->
      <q-dialog v-model="scannerDialog" no-refocus>
        <q-card style="min-width: 350px">
          <q-card-section>
            <div class="text-h6">掃描商品</div>
          </q-card-section>

          <q-card-section>
            <div class="scanner-container">
              <video id="scanner-video" class="scanner-video"></video>
            </div>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="取消" color="negative" @click="stopScanner" />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <!-- 資料表格 -->
      <div class="stock-table-container q-mt-md q-pa-sm">
        <q-table
          :rows="stockItems"
          :columns="[
            {
              name: 'id',
              label: 'ID',
              field: 'id',
              align: 'left',
              sortable: true,
            },
            {
              name: 'name',
              label: '商品名稱',
              field: 'name',
              align: 'left',
              sortable: true,
            },
            {
              name: 'quantity',
              label: '數量',
              field: 'quantity',
              align: 'right',
              sortable: true,
            },
            {
              name: 'price',
              label: '價格',
              field: 'price',
              align: 'right',
              sortable: true,
              format: (val) => `$${val}`,
            },
            {
              name: 'actions',
              label: '操作',
              field: 'actions',
              align: 'center',
            },
          ]"
          row-key="id"
          :filter="searchText"
          :pagination="{
            rowsPerPage: 10,
          }"
          class="stock-table"
        >
          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <q-btn
                flat
                round
                color="info"
                icon="edit"
                @click="editItem(props.row)"
              />
              <q-btn
                flat
                round
                color="negative"
                icon="delete"
                @click="deleteItem(props.row)"
              />
            </q-td>
          </template>
        </q-table>
      </div>

      <!-- 添加/編輯對話框 -->
      <q-dialog v-model="addDialog" no-refocus>
        <q-card style="min-width: 350px">
          <q-card-section>
            <div class="text-h6">{{ isEdit ? '編輯商品' : '新增商品' }}</div>
          </q-card-section>

          <q-card-section class="dialog-content">
            <q-input
              v-model="currentItem.name"
              label="商品名稱"
              :rules="[(val) => !!val || '請輸入商品名稱']"
            />

            <q-input
              v-model.number="currentItem.quantity"
              type="number"
              label="數量"
              class="q-mt-md"
              :rules="[(val) => val >= 0 || '數量不能為負數']"
            />

            <q-input
              v-model.number="currentItem.price"
              type="number"
              label="價格"
              class="q-mt-md"
              :rules="[(val) => val >= 0 || '價格不能為負數']"
            />
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="取消" color="grey" v-close-popup />
            <q-btn flat label="確定" color="primary" @click="saveItem" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import { BrowserMultiFormatReader } from '@zxing/library';
import '@/css/stock.scss';

// 獲取Quasar實例
const $q = useQuasar();

// 定義庫存項目接口
interface StockItem {
  id: number;
  name: string;
  quantity: number;
  price: number;
  [key: string]: number | string; // 允許添加其他類型為string或number的屬性
}

// 定義基本視頻設備接口
interface VideoDeviceInfo {
  deviceId: string;
  label: string;
}

// 確保我們的API接口中使用的是VideoDeviceInfo
interface ScanResult {
  getText: () => string;
}

// 定義 props 和 emits - 只接受VideoDeviceInfo
const props = defineProps<{
  modelValue?: VideoDeviceInfo | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: VideoDeviceInfo | null): void;
  (e: 'scan', value: string): void;
}>();

// 狀態定義
const videoDevices = ref<VideoDeviceInfo[]>([]);
const selectedDevice = computed({
  get: () => props.modelValue ?? null,
  set: (value: VideoDeviceInfo | null) => emit('update:modelValue', value),
});
const scannerDialog = ref(false);
let stream: MediaStream | null = null;
let codeReader = new BrowserMultiFormatReader();

// 庫存相關狀態
const addDialog = ref(false);
const isEdit = ref(false);
const searchText = ref('');
const stockItems = ref<StockItem[]>([]);
const currentItem = ref<StockItem>({
  id: 0,
  name: '',
  quantity: 0,
  price: 0,
});

// 模擬資料
const mockStockData: StockItem[] = [
  { id: 1, name: 'iPhone 14 Pro', quantity: 150, price: 32900 },
  { id: 2, name: 'MacBook Air M2', quantity: 75, price: 39900 },
  { id: 3, name: 'AirPods Pro', quantity: 200, price: 7990 },
  { id: 4, name: 'iPad Pro 12.9', quantity: 100, price: 34900 },
  { id: 5, name: 'Apple Watch Series 8', quantity: 120, price: 13900 },
];

// 初始化資料
onMounted(() => {
  refreshData();
});

// 重新整理
const refreshData = () => {
  // 實際應用中這裡應該從API獲取資料
  stockItems.value = [...mockStockData];
};

// 打開新增對話框
const openAddDialog = () => {
  isEdit.value = false;
  currentItem.value = {
    id: Date.now(), // 使用時間戳作為臨時ID
    name: '',
    quantity: 0,
    price: 0,
  };
  addDialog.value = true;
};

// 編輯項目
const editItem = (item: StockItem) => {
  isEdit.value = true;
  currentItem.value = { ...item };
  addDialog.value = true;
};

// 刪除項目 // ✅ 這裡呼叫 DELETE /api/stock/:id
const deleteItem = (item: StockItem) => {
  $q.dialog({
    title: '確認刪除',
    message: `確定要刪除 ${item.name} 嗎？`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 實際應用中這裡應該調用API刪除
    stockItems.value = stockItems.value.filter((i) => i.id !== item.id);
    $q.notify({
      color: 'positive',
      message: `已刪除 ${item.name}`,
    });
  });
};

// 保存項目
const saveItem = () => {
  if (!currentItem.value.name) {
    $q.notify({
      color: 'negative',
      message: '請輸入商品名稱',
    });
    return;
  }

  if (isEdit.value) {
    // 更新現有項目
    const index = stockItems.value.findIndex(
      (item) => item.id === currentItem.value.id
    );
    if (index !== -1) {
      stockItems.value[index] = { ...currentItem.value };
      $q.notify({
        color: 'positive',
        message: `已更新 ${currentItem.value.name}`,
      });
    }
  } else {
    // 增加新項目
    stockItems.value.push({ ...currentItem.value });
    $q.notify({
      color: 'positive',
      message: `已增加 ${currentItem.value.name}`,
    });
  }

  addDialog.value = false;
};

// 處理掃描到的條碼
const handleScan = (result: string) => {
  stopScanner();
  scannerDialog.value = false;

  // 處理掃描結果，查詢對應的商品
  $q.notify({
    color: 'positive',
    message: `掃描到條碼: ${result}`,
  });

  // 查詢模擬資料中是否有匹配的商品
  const foundItem = mockStockData.find(
    (item) => item.id.toString() === result || item.name === result
  );

  if (foundItem) {
    // 如果找到對應的商品，打開編輯視窗
    editItem(foundItem);
  } else {
    // 如果沒有找到，提示用戶
    $q.dialog({
      title: '未找到商品',
      message: `掃描的條碼 ${result} 沒有對應的商品，是否要新增？`,
      cancel: true,
      persistent: true,
    }).onOk(() => {
      // 打開新增對話框，並預填掃描到的條碼作為ID或名稱
      openAddDialog();
      // 可以根據條碼的格式決定如何預填
      if (!isNaN(Number(result))) {
        currentItem.value.id = Number(result);
      } else {
        currentItem.value.name = result;
      }
    });
  }
};

// 檢查瀏覽器支援
const checkBrowserSupport = () => {
  if (!navigator.mediaDevices?.enumerateDevices) {
    throw new Error('瀏覽器不支援相機');
  }
};

// 處理掃描結果
const handleScanResult = (result: ScanResult | null) => {
  if (result?.getText) {
    const audio = new Audio('/scan-beep.mp3');
    void audio.play().catch();
    const scannedText = result.getText();
    emit('scan', scannedText);
    handleScan(scannedText);
    stopScanner();
  }
};

// 初始化相機
const initVideoStream = async (deviceId: string): Promise<MediaStream> => {
  const constraints: MediaStreamConstraints = {
    video: {
      deviceId: { exact: deviceId },
    },
  };
  return navigator.mediaDevices.getUserMedia(constraints);
};

// 顯示錯誤消息
const showError = (error: unknown) => {
  const message = error instanceof Error ? error.message : '發生未知錯誤';

  console.error('操作失敗:', message);

  // 使用alert先顯示錯誤
  alert(`錯誤: ${message}`);

  // 使用Quasar的通知功能
  try {
    if ($q && typeof $q.notify === 'function') {
      $q.notify({
        color: 'negative',
        message: message,
        position: 'top',
        timeout: 3000,
      });
    }
  } catch (e) {
    console.error('無法顯示通知:', e);
  }
};

// 過濾有效的相機設備
function isVideoDevice(
  device: MediaDeviceInfo
): device is MediaDeviceInfo & { kind: 'videoinput'; deviceId: string } {
  return (
    device.kind === 'videoinput' &&
    typeof device.deviceId === 'string' &&
    device.deviceId.length > 0
  );
}

// 轉換為內部類型
function toVideoDeviceInfo(
  device: MediaDeviceInfo & { deviceId: string },
  index: number
): VideoDeviceInfo {
  return {
    deviceId: device.deviceId,
    label: device.label || `相機 ${index + 1}`,
  };
}

// 檢查相機並GET有效設備
const getCameraDevice = async (): Promise<VideoDeviceInfo | null> => {
  try {
    // 檢查瀏覽器支援
    checkBrowserSupport();

    try {
      // 請求相機權限
      await navigator.mediaDevices.getUserMedia({ video: true });
    } catch (err) {
      console.error('無法訪問相機:', err);
      if (err instanceof DOMException && err.name === 'NotFoundError') {
        throw new Error(
          '您的設備上未檢測到相機，掃描功能僅在擁有相機的設備上可用。'
        );
      } else {
        throw new Error('無法訪問相機，請確保允許瀏覽器訪問攝像頭權限。');
      }
    }

    // 獲取所有設備
    const devices = await navigator.mediaDevices.enumerateDevices();

    // 過濾並轉換視訊設備
    const cameras = devices.filter(isVideoDevice).map(toVideoDeviceInfo);

    if (cameras.length === 0) {
      throw new Error('未檢測到可用的相機設備');
    }

    return cameras[0] ? cameras[0] : null; // 返回 null 當陣列為空時
  } catch (error) {
    showError(error);
    return null;
  }
};

// 開啟掃描器 -
const openScanner = async (): Promise<void> => {
  try {
    // 獲取相機設備
    const camera = await getCameraDevice();

    // 如果沒有找到有效的相機
    if (!camera) {
      // 提供模擬資料選項
      const useDemo = confirm('未檢測到攝像頭。您想要使用測試數據嗎？');
      if (useDemo) {
        // 模擬掃描結果
        // 注意：這裡需要定義emit或使用其他方式處理結果
        // emit('scan', 'DEMO-PRODUCT-123456')
        alert('測試條碼: DEMO-PRODUCT-123456');
      }
      return; // 提前返回
    }

    // 如果找到攝像頭，設置UI
    const cameras = [camera];
    videoDevices.value = cameras;
    selectedDevice.value = camera;
    scannerDialog.value = true;

    try {
      // 初始化掃描器
      codeReader = new BrowserMultiFormatReader();
      stream = await initVideoStream(camera.deviceId);

      const video = document.getElementById('scanner-video');
      if (!(video instanceof HTMLVideoElement)) {
        throw new Error('無法找到影像元素');
      }

      video.srcObject = stream;
      await video.play();

      await codeReader.decodeFromVideoDevice(
        camera.deviceId,
        'scanner-video',
        handleScanResult
      );
    } catch (error) {
      console.error('掃描器初始化失敗:', error);
      throw new Error('掃描器初始化失敗，請確保相機權限已授予');
    }
  } catch (error) {
    showError(error);
    videoDevices.value = [];
    selectedDevice.value = null;
    stopScanner();
  }
};

// 停止掃描器
const stopScanner = (): void => {
  if (stream) {
    stream.getTracks().forEach((track) => track.stop());
    stream = null;
  }

  const video = document.getElementById('scanner-video');
  if (video instanceof HTMLVideoElement) {
    video.srcObject = null;
  }

  codeReader.reset();
  scannerDialog.value = false;
};

// 導出需要的方法
defineExpose({
  openScanner,
  stopScanner,
  openAddDialog,
  editItem,
  deleteItem,
  saveItem,
  refreshData,
});
</script>
