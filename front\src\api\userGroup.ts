import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';

export interface UserGroup {
  id: number;
  name: string;
}

export const UserGroupApi = {
  fetch: () => apiWrapper.get<UserGroup[]>('v1/user-groups'),
  get: (id: number) => apiWrapper.get<UserGroup>(`v1/user-groups/${id}`),
  create: (userGroup: UserGroup, permissionCodes: string[]) =>
    apiWrapper.post<CreateResponse>('v1/user-groups', {
      user_group: userGroup,
      permission_codes: permissionCodes,
    }),
  update: (userGroup: UserGroup, permissionCodes: string[]) =>
    apiWrapper.put(`v1/user-groups/${userGroup.id}`, {
      user_group: userGroup,
      permission_codes: permissionCodes,
    }),
  delete: (id: number) => apiWrapper.delete(`v1/user-groups/${id}`),
};
