import{d as te,aI as ae,r as b,aE as O,c as A,o as oe,p as x,v as U,m as a,t as d,j as t,G as P,A as i,q as le,y as w,B as r,x as E,k as se,H as B,C as F,aK as ne,E as ie}from"./index.09f89dc4.js";import{Q as re}from"./QBanner.ce4d7588.js";import{Q as ue,b as q}from"./QSelect.958fa87e.js";import{Q}from"./QTd.00e5e315.js";import{Q as ce}from"./QTooltip.6fa09534.js";import{Q as L}from"./QTable.5ba31f17.js";import{Q as de}from"./QSpace.2ea7fb32.js";import{Q as u}from"./QItemLabel.88180eb1.js";import{Q as f,a as p}from"./QItemSection.3e7b5a38.js";import{Q as me}from"./QPage.ce1b4cb5.js";import{u as ve}from"./vue-i18n.1783a0cb.js";import{u as fe}from"./use-quasar.3b603a60.js";import{X as $}from"./xero.34271ea4.js";import{f as C}from"./date.6d29930c.js";import{_ as pe}from"./DateRangePicker.2cfa5e9c.js";import"./QMenu.531c6599.js";import"./selection.2acb415c.js";import"./format.054b8074.js";import"./QList.5d1c2d4f.js";import"./use-fullscreen.2a1ec9b4.js";import"./QDate.f6067d5f.js";import"./QPopupProxy.f63a65d9.js";import"./ClosePopup.712518f2.js";const ge={class:"row justify-center"},be={class:"col-12"},ye={class:"text-h6 q-mb-md"},_e={key:1,class:"row q-gutter-md q-mb-md"},xe={class:"row"},we={class:"text-h6"},he={class:"q-mb-md bg-white rounded-borders",style:{border:"1px solid #e0e0e0"}},Ie={class:"row"},De={class:"q-pa-md q-mb-md bg-blue-grey-1 rounded-borders"},Ae={class:"text-subtitle1 q-mb-md text-primary"},Ue={class:"row q-gutter-sm"},We=te({__name:"XeroInvoicesPage",setup(Ee){const N=ae(),{t:s}=ve(),v=fe(),h=b(!1),S=b([]),n=b(null),I=b(!1),T=b(!1),R=b(!1),y=O({connected:!1,tenant_name:""}),g=O({status:"",dateRange:{from:"",to:""}}),c=b({sortBy:"date",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),M=A(()=>[{label:s("xero.invoices.status.draft"),value:"DRAFT"},{label:s("xero.invoices.status.submitted"),value:"SUBMITTED"},{label:s("xero.invoices.status.authorised"),value:"AUTHORISED"},{label:s("xero.invoices.status.paid"),value:"PAID"},{label:s("xero.invoices.status.voided"),value:"VOIDED"}]),j=A(()=>[{name:"invoice_number",label:s("xero.invoices.invoiceNumber"),field:"invoice_number",align:"left",sortable:!0},{name:"type",label:s("xero.invoices.type.label"),field:"type",align:"center",sortable:!0},{name:"contact",label:s("xero.invoices.contact"),field:e=>{var l;return((l=e.contact)==null?void 0:l.name)||""},align:"left",sortable:!0},{name:"date",label:s("xero.invoices.date"),field:"date",align:"center",sortable:!0,format:e=>C(e)},{name:"due_date",label:s("xero.invoices.dueDate"),field:"due_date",align:"center",sortable:!0,format:e=>C(e)},{name:"status",label:s("status"),field:"status",align:"center",sortable:!0},{name:"total",label:s("xero.invoices.total"),field:"total",align:"right",sortable:!0},{name:"actions",label:s("actions"),field:"",align:"center"}]),H=A(()=>[{name:"description",label:s("product.label"),field:"description",align:"left"},{name:"quantity",label:s("quantity"),field:"quantity",align:"center"},{name:"unit_amount",label:s("xero.invoices.unitAmount"),field:"unit_amount",align:"right",format:e=>{var l;return"AU "+_(e,((l=n.value)==null?void 0:l.currency_code)||"AUD")}},{name:"line_amount",label:s("xero.invoices.lineAmount"),field:"line_amount",align:"right"}]),z=async()=>{try{const e=await $.getConnectionStatus();Object.assign(y,e.result)}catch(e){console.error("Failed to load connection status:",e)}},k=async()=>{var e,l,o;if(!!y.connected){h.value=!0;try{const m={page:c.value.page,pageSize:c.value.rowsPerPage,status:g.status||void 0,dateFrom:g.dateRange.from||void 0,dateTo:g.dateRange.to||void 0},D=await $.getInvoices(m);S.value=((e=D.result)==null?void 0:e.invoices)||[],c.value.page=((l=D.result)==null?void 0:l.page)||1,c.value.rowsNumber=((o=D.result)==null?void 0:o.total_count)||0}catch(m){console.error("Failed to load invoices:",m),S.value=[],c.value.rowsNumber=0,v.notify({position:"top",type:"negative",message:(m==null?void 0:m.message)||s("failed")})}finally{h.value=!1}}},Y=e=>{const{page:l,rowsPerPage:o,sortBy:m,descending:D}=e.pagination;c.value.page=l,c.value.rowsPerPage=o,c.value.sortBy=m,c.value.descending=D,k()},X=async e=>{var l;try{h.value=!0;const o=await $.getInvoice(e.invoice_id);n.value=((l=o.result)==null?void 0:l.invoice)||null,n.value?I.value=!0:v.notify({position:"top",type:"negative",message:s("xero.invoices.invoiceNotFound")})}catch(o){console.error("Failed to load invoice details:",o),n.value=null,v.notify({position:"top",type:"negative",message:(o==null?void 0:o.message)||s("failed")})}finally{h.value=!1}},G=()=>{N.push("/admin/dashboard/xero/setup")},V=e=>({DRAFT:"grey",SUBMITTED:"orange",AUTHORISED:"blue",PAID:"positive",VOIDED:"negative"})[e]||"grey",_=(e,l="AUD")=>new Intl.NumberFormat("en-AU",{style:"currency",currency:l}).format(e),K=e=>({DRAFT:s("xero.invoices.status.draft"),SUBMITTED:s("xero.invoices.status.submitted"),AUTHORISED:s("xero.invoices.status.authorised"),PAID:s("xero.invoices.status.paid"),VOIDED:s("xero.invoices.status.voided")})[e]||e,W=A(()=>n.value?["AUTHORISED","PAID"].includes(n.value.status):!1),J=A(()=>{var e;return n.value?["AUTHORISED","PAID"].includes(n.value.status)&&((e=n.value.contact)==null?void 0:e.name):!1}),Z=async()=>{if(!!n.value)try{T.value=!0;const e=await $.getInvoicePDF(n.value.invoice_id);if(e.size<100)throw new Error("PDF data too small, likely invalid");const l=URL.createObjectURL(e),o=window.open(l,"_blank");if(!o)throw URL.revokeObjectURL(l),new Error("Failed to open print window - popup blocked?");o.onload=()=>{setTimeout(()=>{o.print()},1e3)},setTimeout(()=>{URL.revokeObjectURL(l)},15e3),v.notify({type:"positive",message:s("printInvoiceSuccess"),position:"top"})}catch(e){console.error("Print invoice error:",e),v.notify({type:"negative",message:(e==null?void 0:e.message)||s("printInvoiceError"),position:"top"})}finally{T.value=!1}},ee=async()=>{var e;!n.value||!((e=n.value.contact)!=null&&e.name)||v.dialog({title:s("sendEmail"),message:s("xero.invoices.enterEmailAddress"),prompt:{model:"",type:"email",placeholder:"<EMAIL>"},cancel:!0,persistent:!0}).onOk(async l=>{if(!(!l||!n.value))try{R.value=!0,await $.sendInvoiceEmail(n.value.invoice_id,l),v.notify({type:"positive",message:s("sendEmailSuccess"),position:"top"})}catch(o){console.error("Send email error:",o),v.notify({type:"negative",message:(o==null?void 0:o.message)||s("sendEmailError"),position:"top"})}finally{R.value=!1}})};return oe(async()=>{await z(),y.connected&&await k()}),(e,l)=>(x(),U(me,{class:"q-pa-md"},{default:a(()=>[d("div",ge,[d("div",be,[t(B,null,{default:a(()=>[t(P,null,{default:a(()=>[d("div",ye,i(e.$t("xero.invoices.title")),1),y.connected?E("",!0):(x(),U(re,{key:0,class:"bg-warning text-dark q-mb-md",rounded:""},{avatar:a(()=>[t(le,{name:"warning",color:"orange"})]),action:a(()=>[t(w,{flat:"",color:"dark",label:e.$t("xero.invoices.goToSetup"),onClick:G},null,8,["label"])]),default:a(()=>[r(" "+i(e.$t("xero.invoices.notConnected"))+" ",1)]),_:1})),y.connected?(x(),se("div",_e,[t(ue,{modelValue:g.status,"onUpdate:modelValue":[l[0]||(l[0]=o=>g.status=o),k],options:M.value,label:e.$t("status"),clearable:"",style:{"min-width":"150px"},"map-options":"","emit-value":"",dense:""},null,8,["modelValue","options","label"]),t(pe,{modelValue:g.dateRange,"onUpdate:modelValue":[l[1]||(l[1]=o=>g.dateRange=o),k],dateMask:"YYYY-MM-DD"},null,8,["modelValue"])])):E("",!0),y.connected?(x(),U(L,{key:2,rows:S.value,columns:j.value,pagination:c.value,"onUpdate:pagination":l[2]||(l[2]=o=>c.value=o),onRequest:Y,"row-key":"invoice_id","binary-state-sort":"",loading:h.value},{"body-cell-status":a(o=>[t(Q,{props:o},{default:a(()=>[t(q,{color:V(o.value),"text-color":"white",label:e.$t(`xero.invoices.status.${o.value.toLowerCase()}`)},null,8,["color","label"])]),_:2},1032,["props"])]),"body-cell-type":a(o=>[t(Q,{props:o},{default:a(()=>[t(q,{color:o.value==="ACCREC"?"positive":"info","text-color":"white",label:e.$t(`xero.invoices.type.${o.value.toLowerCase()}`)},null,8,["color","label"])]),_:2},1032,["props"])]),"body-cell-total":a(o=>[t(Q,{props:o},{default:a(()=>[r(" AU "+i(_(o.value,o.row.currency_code)),1)]),_:2},1032,["props"])]),"body-cell-actions":a(o=>[t(Q,{props:o},{default:a(()=>[t(w,{flat:"",round:"",color:"primary",icon:"visibility",size:"sm",onClick:m=>X(o.row)},{default:a(()=>[t(ce,null,{default:a(()=>[r(i(e.$t("view")),1)]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["props"])]),_:1},8,["rows","columns","pagination","loading"])):E("",!0)]),_:1})]),_:1})])]),t(ie,{modelValue:I.value,"onUpdate:modelValue":l[5]||(l[5]=o=>I.value=o),class:"card-dialog"},{default:a(()=>[t(B,{class:"column"},{default:a(()=>[t(P,{class:"col-1 q-py-none"},{default:a(()=>[d("div",xe,[d("div",we,i(e.$t("xero.invoices.invoiceDetails")),1),t(de),t(w,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:l[3]||(l[3]=o=>I.value=!1)})])]),_:1}),n.value?(x(),U(P,{key:0,class:"col-10"},{default:a(()=>[d("div",he,[d("div",Ie,[n.value.invoice_number?(x(),U(f,{key:0,class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.invoiceNumber")),1)]),_:1}),t(u,{class:"text-subtitle1 text-grey-9"},{default:a(()=>[r(i(n.value.invoice_number),1)]),_:1})]),_:1})]),_:1})):E("",!0),t(f,{class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("status")),1)]),_:1}),t(u,null,{default:a(()=>[t(q,{color:V(n.value.status),"text-color":"white",class:"text-subtitle1",size:"md"},{default:a(()=>[r(i(K(n.value.status)),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),t(f,{class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.contact")),1)]),_:1}),t(u,{class:"text-subtitle1 text-grey-9"},{default:a(()=>{var o;return[r(i(((o=n.value.contact)==null?void 0:o.name)||""),1)]}),_:1})]),_:1})]),_:1}),t(f,{class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.date")),1)]),_:1}),t(u,{class:"text-subtitle1 text-grey-9"},{default:a(()=>[r(i(F(C)(n.value.date)),1)]),_:1})]),_:1})]),_:1}),t(f,{class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.dueDate")),1)]),_:1}),t(u,{class:"text-subtitle1 text-grey-9"},{default:a(()=>[r(i(F(C)(n.value.due_date)),1)]),_:1})]),_:1})]),_:1}),t(f,{class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.subTotal")),1)]),_:1}),t(u,{class:"text-subtitle1 text-grey-9"},{default:a(()=>[r("AU "+i(_(n.value.sub_total,n.value.currency_code)),1)]),_:1})]),_:1})]),_:1}),t(f,{class:"col-12 col-md-6"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.totalTax")),1)]),_:1}),t(u,{class:"text-subtitle1 text-grey-9"},{default:a(()=>[r("AU "+i(_(n.value.total_tax,n.value.currency_code)),1)]),_:1})]),_:1})]),_:1}),t(f,{class:"col-12"},{default:a(()=>[t(p,null,{default:a(()=>[t(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:a(()=>[r(i(e.$t("xero.invoices.total")),1)]),_:1}),t(u,{class:"text-h6 text-primary text-weight-bold"},{default:a(()=>[r("AU "+i(_(n.value.total,n.value.currency_code)),1)]),_:1})]),_:1})]),_:1})])]),d("div",De,[d("div",Ae,i(e.$t("xero.invoices.lineItems")),1),t(L,{rows:n.value.line_items||[],columns:H.value,"hide-pagination":"",flat:"",bordered:"",class:"bg-white"},{"body-cell-line_amount":a(o=>[t(Q,{props:o},{default:a(()=>[r(" AU "+i(_(o.value,n.value.currency_code)),1)]),_:2},1032,["props"])]),_:1},8,["rows","columns"])])]),_:1})):E("",!0),t(ne,{align:"between",class:"col-1 full-width bg-grey-2"},{default:a(()=>[d("div",Ue,[t(w,{color:"primary",icon:"print",label:e.$t("printInvoice"),onClick:Z,loading:T.value,disable:!W.value},null,8,["label","loading","disable"]),t(w,{color:"secondary",icon:"email",label:e.$t("sendEmail"),onClick:ee,loading:R.value,disable:!J.value},null,8,["label","loading","disable"])]),t(w,{color:"negative",icon:"close",label:e.$t("close"),onClick:l[4]||(l[4]=o=>I.value=!1)},null,8,["label"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}});export{We as default};
