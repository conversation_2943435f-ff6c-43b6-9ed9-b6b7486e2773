import{bk as r}from"./index.09f89dc4.js";const d={fetch:({filter:e,pagination:t})=>r.get("/v1/orders",{params:{...e,...t}}),get:e=>r.get(`/v1/orders/${e}`),create:()=>r.post("/v1/orders"),updateNotes:(e,t)=>r.patch(`/v1/orders/${e}/notes`,{notes:t}),voidOrder:(e,t,o)=>r.post(`/v1/orders/${e}`,{reason:t,otherReason:o}),deleteOrder:e=>r.delete(`/v1/orders/${e}`),checkout:e=>r.post(`/v1/orders/${e.uuid}/checkout`,e),listWCHistory:({filter:e,pagination:t}={})=>r.get("/v1/wc-orders/history",{params:{...e,...t}}),wcFetchPending:()=>r.get("/v1/wc-orders/pending"),getWCOrder:e=>r.get(`/v1/wc-orders/${e}`),updateWCOrderStatus:(e,t)=>r.patch(`/v1/wc-orders/${e}/status`,{status:t}),updateWCOrderCustomerNote:(e,t)=>r.patch(`/v1/wc-orders/${e}/customer-note`,{customer_note:t})};export{d as O};
