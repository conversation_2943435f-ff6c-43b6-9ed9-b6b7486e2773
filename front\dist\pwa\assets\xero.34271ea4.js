import{bk as o,bC as c,aJ as p}from"./index.09f89dc4.js";const l=()=>{const e=c.create({baseURL:"/au-pos/api",timeout:3e4});return e.interceptors.request.use(s=>{const a=p();return a.accessToken&&(s.headers.Authorization=`Bearer ${a.accessToken}`,s.headers["X-Refresh-Token"]=a.refreshToken),s}),e},u={saveConfig:e=>o.post("/v1/xero/config",e),getConfig:()=>o.get("/v1/xero/config"),getAuthURL:()=>o.get("/v1/xero/auth-url"),callback:(e,s)=>o.post("/v1/xero/callback",{code:e,state:s}),getConnectionStatus:()=>o.get("/v1/xero/status"),validateConnection:()=>o.post("/v1/xero/validate-connection"),refreshToken:()=>o.post("/v1/xero/refresh"),disconnect:()=>o.delete("/v1/xero/disconnect"),getInvoices:e=>o.get("/v1/xero/invoices",{params:e}),getInvoice:e=>o.get(`/v1/xero/invoices/${e}`),syncOrderToXero:e=>o.post(`/v1/xero/sync-order/${e}`),getInvoicePDF:async e=>{var s,a,n,i;try{const r=l();console.log(`Requesting PDF for invoice: ${e}`);const t=await r.get(`/v1/xero/invoices/${e}/pdf`,{responseType:"blob"});if(console.log("Raw PDF response status:",t.status),console.log("Raw PDF response headers:",t.headers),console.log("Response data type:",typeof t.data),console.log("Response data instanceof Blob:",t.data instanceof Blob),console.log("Response data size:",((s=t.data)==null?void 0:s.size)||"unknown"),!(t.data instanceof Blob))throw console.error("Response data:",t.data),new Error("Response is not a Blob");if(t.data.size===0)throw new Error("Received empty PDF data");return t.data}catch(r){throw console.error("Error fetching PDF:",r),c.isAxiosError(r)&&console.error("Axios error details:",{status:(a=r.response)==null?void 0:a.status,statusText:(n=r.response)==null?void 0:n.statusText,data:(i=r.response)==null?void 0:i.data}),r}},sendInvoiceEmail:(e,s)=>o.post(`/v1/xero/invoices/${e}/email`,{email:s}),checkEmailLimitStatus:()=>o.get("/v1/xero/email-status")};export{u as X};
