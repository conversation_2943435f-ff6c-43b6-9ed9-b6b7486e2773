2025/07/09 18:06:53 Failed to fetch pending orders | 詳細錯誤: failed to get pending orders: failed to get pending orders: Error 1146 (42S02): Table 'autocare_wp.wp_wc_orders' doesn't exist | 500 (伺服器內部錯誤) | 請求: GET /api/v1/wc-orders/pending
2025/07/09 18:06:53 Failed to fetch pending orders
2025/07/09 18:06:53 Failed to fetch history orders | 詳細錯誤: failed to get history orders: failed to get history orders: Error 1146 (42S02): Table 'autocare_wp.wp_wc_orders' doesn't exist | 500 (伺服器內部錯誤) | 請求: GET /api/v1/wc-orders/history
2025/07/09 18:06:53 Failed to fetch history orders
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:01:41 token is expired
2025/07/09 19:21:30 token is expired
2025/07/09 19:21:30 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 19:58:06 token is expired
2025/07/09 20:06:56 Failed to update order status | 詳細錯誤: invalid order status: cancelled | 500 (伺服器內部錯誤) | 請求: PATCH /api/v1/wc-orders/1927/status
2025/07/09 20:06:56 Failed to update order status
2025/07/09 20:13:07 token is expired
2025/07/09 20:13:07 token is expired
2025/07/09 20:13:07 token is expired
2025/07/09 20:13:07 token is expired
2025/07/09 20:13:08 Failed to update customer note | 詳細錯誤: failed to update customer note: order not found or no changes made | 500 (伺服器內部錯誤) | 請求: PATCH /api/v1/wc-orders/1927/customer-note
2025/07/09 20:13:08 Failed to update customer note
2025/07/09 20:34:53 token is expired
2025/07/09 20:34:53 token is expired
2025/07/09 20:34:53 token is expired
2025/07/09 20:34:53 token is expired
2025/07/09 20:34:53 token is expired
2025/07/09 20:34:53 token is expired
2025/07/09 20:34:54 token is expired
2025/07/09 20:34:54 token is expired
2025/07/09 20:34:54 token is expired
2025/07/09 20:34:54 token is expired
2025/07/09 20:34:54 token is expired
2025/07/09 20:34:54 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:19 token is expired
2025/07/09 21:26:20 token is expired
2025/07/09 21:26:20 token is expired
2025/07/09 21:41:32 token is expired
2025/07/09 21:41:32 token is expired
2025/07/09 21:41:32 token is expired
2025/07/09 21:41:32 token is expired
2025/07/09 21:50:34 listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025/07/09 21:50:34 failed to start server
2025/07/09 22:16:43 token is expired
2025/07/09 22:16:43 token is expired
2025/07/09 22:16:44 Failed to fetch announcement | 詳細錯誤: Error 1146 (42S02): Table 'cx-crm.announcements' doesn't exist | 500 (伺服器內部錯誤) | 請求: GET /api/v1/announcements
2025/07/09 22:16:44 Failed to fetch announcement
2025/07/09 22:38:20 token is expired
2025/07/09 22:38:20 token is expired
2025/07/09 22:46:49 Failed to send invoice email | 錯誤詳情: Xero API error: 400 - {
  "ErrorNumber": 10,
  "Type": "ValidationException",
  "Message": "A validation exception occurred",
  "Elements": [
    {
      "ValidationErrors": [
        {
          "Message": "Invoices for contacts with no email address assigned cannot be emailed"
        }
      ]
    }
  ]
} | 500 (伺服器內部錯誤) | 請求: POST /api/v1/xero/invoices/41ced23b-6a46-464f-a885-ad20726692b7/email | 上下文: invoiceID: 41ced23b-6a46-464f-a885-ad20726692b7, email: <EMAIL>, operation: SendInvoiceEmail
2025/07/09 22:46:49 Failed to send invoice email
2025/07/09 22:47:01 Failed to send invoice email | 錯誤詳情: Xero API error: 400 - {
  "ErrorNumber": 10,
  "Type": "ValidationException",
  "Message": "A validation exception occurred",
  "Elements": [
    {
      "ValidationErrors": [
        {
          "Message": "Invoices for contacts with no email address assigned cannot be emailed"
        }
      ]
    }
  ]
} | 500 (伺服器內部錯誤) | 請求: POST /api/v1/xero/invoices/41ced23b-6a46-464f-a885-ad20726692b7/email | 上下文: email: <EMAIL>, operation: SendInvoiceEmail, invoiceID: 41ced23b-6a46-464f-a885-ad20726692b7
2025/07/09 22:47:01 Failed to send invoice email
2025/07/09 22:50:11 Failed to send invoice email | 錯誤詳情: Xero API error: 400 - {
  "ErrorNumber": 10,
  "Type": "ValidationException",
  "Message": "A validation exception occurred",
  "Elements": [
    {
      "ValidationErrors": [
        {
          "Message": "Invoices for contacts with no email address assigned cannot be emailed"
        }
      ]
    }
  ]
} | 500 (伺服器內部錯誤) | 請求: POST /api/v1/xero/invoices/41ced23b-6a46-464f-a885-ad20726692b7/email | 上下文: invoiceID: 41ced23b-6a46-464f-a885-ad20726692b7, email: <EMAIL>, operation: SendInvoiceEmail
2025/07/09 22:50:11 Failed to send invoice email
2025/07/09 22:50:57 empty authorization
2025/07/09 22:50:57 empty authorization
2025/07/09 23:05:51 Failed to create order | 詳細錯誤: record not found | 500 (伺服器內部錯誤) | 請求: POST /api/v1/orders
2025/07/09 23:05:51 Failed to create order
2025/07/09 23:06:02 Failed to create order | 詳細錯誤: record not found | 500 (伺服器內部錯誤) | 請求: POST /api/v1/orders
2025/07/09 23:06:02 Failed to create order
2025/07/09 23:08:51 Failed to send invoice email | 錯誤詳情: Xero API error: 400 - {
  "ErrorNumber": 10,
  "Type": "ValidationException",
  "Message": "A validation exception occurred",
  "Elements": [
    {
      "ValidationErrors": [
        {
          "Message": "Invoices for contacts with no email address assigned cannot be emailed"
        }
      ]
    }
  ]
} | 500 (伺服器內部錯誤) | 請求: POST /api/v1/xero/invoices/73c97302-eca9-4a95-bd10-415ea3c4666d/email | 上下文: operation: SendInvoiceEmail, invoiceID: 73c97302-eca9-4a95-bd10-415ea3c4666d, email: <EMAIL>
2025/07/09 23:08:51 Failed to send invoice email
