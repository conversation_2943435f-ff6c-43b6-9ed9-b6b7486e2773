import { ref } from 'vue';
import { Product } from '@/api/product';

interface BarcodeScannerOptions {
  // 掃描完成後的等待時間 (毫秒)
  timeout?: number;
  // 條碼字段名稱 (默認為barcode)
  barcodeField?: string;
}

export function useBarcodeScanner(options: BarcodeScannerOptions = {}) {
  const { timeout = 20, barcodeField = 'barcode' } = options;

  const isReady = ref<boolean>(true);

  // 處理掃描的條碼
  const processBarcode = (barcode: string) => {
    isReady.value = false;

    // 觸發條碼掃描事件
    const event = new CustomEvent('barcode-scanned', {
      detail: { barcode },
    });
    window.dispatchEvent(event);

    // 設置冷卻時間，避免重複掃描
    setTimeout(() => {
      isReady.value = true;
    }, timeout);
  };

  // 根據條碼查找商品
  const findProductByBarcode = (
    products: Product[],
    barcode: string
  ): Product | undefined => {
    return products.find(
      (product) => product[barcodeField as keyof Product] === barcode
    );
  };

  return {
    isReady,
    processBarcode,
    findProductByBarcode,
  };
}
