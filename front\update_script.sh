#!/bin/bash

cleanup_unused_images() {
    echo "正在清理未使用的 Docker 映像..."
    docker image prune -af
    echo "清理完成。"
}

# 構建新的映像
docker-compose build web

# 更新每個實例
for app in web; do
    echo "準備更新 $app..."

    # 獲取容器ID
    container_id=$(docker-compose ps -q $app)

    echo "正在更新 $app..."

    # 停止並移除舊的容器
    docker-compose stop $app
    docker-compose rm -f $app

    # 啟動新的容器
    docker-compose up -d $app

    echo "$app 更新成功。"
done

echo "所有實例已更新完成。"

# 清理未使用的映像
cleanup_unused_images
