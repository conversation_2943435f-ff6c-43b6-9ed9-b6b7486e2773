import { Dialog } from 'quasar';
import { useI18n } from 'vue-i18n';

interface ShowDialogOptions {
  message: string;
  title?: string;
  timeout?: number;
  persistent?: boolean;
  color?: string;
  ok?: () => void;
  onRedirect?: () => void;
}

export const useDialog = () => {
  const { t } = useI18n();

  const showMessage = ({
    message,
    title = t('hint'),
    timeout = 1500,
    persistent = false,
    color = 'primary',
    ok,
    onRedirect,
  }: ShowDialogOptions) => {
    const dialog = Dialog.create({
      title,
      message,
      color,
      persistent,
      html: true,
      style: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
      },
      ok: {
        label: t('confirm'),
        color: 'positive',
      },
      cancel:
        timeout > 0 || !ok
          ? undefined
          : {
              label: t('cancel'),
              color: 'negative',
            },
    }).onOk(() => {
      if (ok) {
        ok();
      } else if (onRedirect) {
        onRedirect();
      }
    });

    if (timeout > 0) {
      setTimeout(() => {
        dialog.hide();
        if (onRedirect) {
          onRedirect();
        }
      }, timeout);
    }
  };

  return {
    showMessage,
  };
};
