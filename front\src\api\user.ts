import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';

export interface UserInfo {
  uuid: string;
  name: string;
  email: string;
}

export interface User {
  uuid: string;
  name: string;
  group_id: number;
  username: string;
  password: string;
  email: string;
  is_admin: boolean;
  is_active: boolean;
  note: string;
}

export interface UserCreatePayload {
  name: string;
  group_id: number;
  username: string;
  password: string;
  email: string;
  note: string;
}

export const UserApi = {
  fetch: () => apiWrapper.get<User[]>('v1/users'),
  get: (uuid: string) => apiWrapper.get<User>(`v1/users/${uuid}`),
  create: (user: UserCreatePayload) =>
    apiWrapper.post<CreateResponse>('v1/users', user),
  update: (user: User) => apiWrapper.put(`v1/users/${user.uuid}`, user),
  delete: (uuid: string) => apiWrapper.delete(`v1/users/${uuid}`),
};
