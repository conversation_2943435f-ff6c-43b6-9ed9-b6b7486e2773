import{L as te,ac as ae,ad as T,ae as oe,af as ne,g as ie,r as k,c as f,ag as le,ah as se,ai as re,aj as ue,ak as ce,w as C,a6 as E,a7 as x,a5 as H,ap as de,h as q,aq as fe,Y as he,W as ve}from"./index.09f89dc4.js";import{u as ge,v as A,a as me,b as Te,c as pe,r as j,s as ye,p as D,d as Se}from"./QMenu.531c6599.js";import{c as L}from"./selection.2acb415c.js";var Oe=te({name:"QTooltip",inheritAttrs:!1,props:{...ge,...ae,...T,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{...T.transitionShow,default:"jump-down"},transitionHide:{...T.transitionHide,default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:A},self:{type:String,default:"top middle",validator:A},offset:{type:Array,default:()=>[14,14],validator:me},scrollTarget:oe,delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[...ne],setup(e,{slots:M,emit:p,attrs:h}){let i,l;const v=ie(),{proxy:{$q:a}}=v,s=k(null),c=k(!1),W=f(()=>D(e.anchor,a.lang.rtl)),Q=f(()=>D(e.self,a.lang.rtl)),B=f(()=>e.persistent!==!0),{registerTick:N,removeTick:R}=le(),{registerTimeout:d}=se(),{transitionProps:_,transitionStyle:I}=re(e),{localScrollTarget:y,changeScrollEvent:U,unconfigureScrollTarget:V}=Te(e,w),{anchorEl:o,canShow:Y,anchorEvents:r}=pe({showing:c,configureAnchorEl:X}),{show:$,hide:g}=ue({showing:c,canShow:Y,handleShow:F,handleHide:G,hideOnRouteChange:B,processOnMount:!0});Object.assign(r,{delayShow:J,delayHide:K});const{showPortal:S,hidePortal:b,renderPortal:z}=ce(v,s,ee,"tooltip");if(a.platform.is.mobile===!0){const t={anchorEl:o,innerRef:s,onClickOutside(n){return g(n),n.target.classList.contains("q-dialog__backdrop")&&ve(n),!0}},m=f(()=>e.modelValue===null&&e.persistent!==!0&&c.value===!0);C(m,n=>{(n===!0?Se:j)(t)}),E(()=>{j(t)})}function F(t){S(),N(()=>{l=new MutationObserver(()=>u()),l.observe(s.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),u(),w()}),i===void 0&&(i=C(()=>a.screen.width+"|"+a.screen.height+"|"+e.self+"|"+e.anchor+"|"+a.lang.rtl,u)),d(()=>{S(!0),p("show",t)},e.transitionDuration)}function G(t){R(),b(),P(),d(()=>{b(!0),p("hide",t)},e.transitionDuration)}function P(){l!==void 0&&(l.disconnect(),l=void 0),i!==void 0&&(i(),i=void 0),V(),x(r,"tooltipTemp")}function u(){ye({targetEl:s.value,offset:e.offset,anchorEl:o.value,anchorOrigin:W.value,selfOrigin:Q.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function J(t){if(a.platform.is.mobile===!0){L(),document.body.classList.add("non-selectable");const m=o.value,n=["touchmove","touchcancel","touchend","click"].map(O=>[m,O,"delayHide","passiveCapture"]);H(r,"tooltipTemp",n)}d(()=>{$(t)},e.delay)}function K(t){a.platform.is.mobile===!0&&(x(r,"tooltipTemp"),L(),setTimeout(()=>{document.body.classList.remove("non-selectable")},10)),d(()=>{g(t)},e.hideDelay)}function X(){if(e.noParentEvent===!0||o.value===null)return;const t=a.platform.is.mobile===!0?[[o.value,"touchstart","delayShow","passive"]]:[[o.value,"mouseenter","delayShow","passive"],[o.value,"mouseleave","delayHide","passive"]];H(r,"anchor",t)}function w(){if(o.value!==null||e.scrollTarget!==void 0){y.value=de(o.value,e.scrollTarget);const t=e.noParentEvent===!0?u:g;U(y.value,t)}}function Z(){return c.value===!0?q("div",{...h,ref:s,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",h.class],style:[h.style,I.value],role:"tooltip"},he(M.default)):null}function ee(){return q(fe,_.value,Z)}return E(P),Object.assign(v.proxy,{updatePosition:u}),z}});export{Oe as Q};
