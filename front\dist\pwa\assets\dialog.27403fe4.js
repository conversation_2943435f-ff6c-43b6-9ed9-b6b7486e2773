import{aQ as c}from"./index.09f89dc4.js";import{u as f}from"./vue-i18n.1783a0cb.js";const p=()=>{const{t:s}=f();return{showMessage:({message:o,title:a=s("hint"),timeout:l=1500,persistent:i=!1,color:n="primary",ok:t,onRedirect:e})=>{const r=c.create({title:a,message:o,color:n,persistent:i,html:!0,style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},ok:{label:s("confirm"),color:"positive"},cancel:l>0||!t?void 0:{label:s("cancel"),color:"negative"}}).onOk(()=>{t?t():e&&e()});l>0&&setTimeout(()=>{r.hide(),e&&e()},l)}}};export{p as u};
