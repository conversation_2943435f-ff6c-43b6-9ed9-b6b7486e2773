<template>
  <UserVerifyForm v-model="isVerifying" @login="changeUser" />

  <div
    class="punch-form column full-height q-px-md q-pt-lg"
    v-if="!isVerifying"
  >
    <div class="col-1">
      <div class="row">
        <div class="col text-h6">
          {{ userInfo.name }}
          <q-btn
            type="button"
            @click="openUserVerifyDialog"
            :label="t('changeUser')"
            icon="switch_account"
            color="primary"
            dense
            no-caps
            class="q-ml-md"
          />
          <q-separator class="q-mt-md q-mb-lg" />
        </div>
      </div>
    </div>

    <div class="col-11">
      <!-- Latest Punch -->
      <div class="col-1">
        <div class="row">
          <div class="col text-h6 q-py-md">
            <q-item v-if="latestPunch?.clock_time">
              <q-item-section>
                <q-item-label> {{ t('latestPunch') }}： </q-item-label>
              </q-item-section>
              <q-item-section>
                {{ formatDate(latestPunch.clock_time, 'YYYY-MM-DD HH:mm:ss') }}
              </q-item-section>
              <q-item-section side>
                <q-icon
                  name="arrow_upward"
                  color="positive"
                  v-if="latestPunch.type === 'clock_in'"
                />
                <q-icon name="arrow_downward" color="negative" v-else />
              </q-item-section>
            </q-item>
            <q-item v-else>
              <q-item-section>
                {{ t('error.noData') }}
              </q-item-section>
            </q-item>
          </div>
        </div>
      </div>
      <!-- Punch Button -->
      <div class="col-6 text-center q-pt-lg">
        <div class="row">
          <!-- Clock in -->
          <div class="col">
            <q-btn
              round
              @click="clockIn"
              class="punch"
              color="green"
              :loading="isLoading"
            >
              <div class="d-block">
                <q-icon size="xl" name="arrow_upward" />
                <div class="text-subtitle1">
                  {{ t('clockIn') }}
                </div>
              </div>
            </q-btn>
          </div>
          <!-- Clock out -->
          <div class="col">
            <q-btn
              round
              @click="clockOut"
              class="punch"
              color="red"
              :loading="isLoading"
            >
              <div class="d-block">
                <q-icon size="xl" name="arrow_downward" />
                <div class="text-subtitle1">
                  {{ t('clockOut') }}
                </div>
              </div>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { UserInfo } from '@/api/user';
import { AttendanceApi, Clock } from '@/api/attendance';
import { formatDate } from '@/utils';

const { t } = useI18n();

const props = defineProps<{
  userInfo: UserInfo;
}>();

const emit = defineEmits(['change-user']);

const isLoading = ref(false);
const clockIn = async () => {
  try {
    isLoading.value = true;
    await AttendanceApi.punch({
      user_uuid: props.userInfo.uuid,
      type: 'clock_in',
    });

    fetchData();
  } finally {
    isLoading.value = false;
  }
};

const clockOut = async () => {
  try {
    isLoading.value = true;
    await AttendanceApi.punch({
      user_uuid: props.userInfo.uuid,
      type: 'clock_out',
    });

    fetchData();
  } finally {
    isLoading.value = false;
  }
};

const latestPunch = ref<Clock>();
const fetchData = async () => {
  try {
    isLoading.value = true;
    const response = await AttendanceApi.getLatest(props.userInfo.uuid);

    latestPunch.value = response.result;
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchData();
});

watch(
  () => props.userInfo,
  () => {
    fetchData();
  }
);

const isVerifying = ref(false);
const openUserVerifyDialog = () => {
  isVerifying.value = true;
};

const changeUser = (data: UserInfo) => {
  emit('change-user', data);
};
</script>

<style lang="scss" scoped>
.punch {
  width: 8rem;
  aspect-ratio: 1;
}
</style>
