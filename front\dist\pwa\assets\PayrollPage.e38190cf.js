import{Q as z,a as te}from"./QToolbar.b89be0c0.js";import{bk as h,d as Te,r as k,c as W,o as He,w as oe,z as $e,p as c,v as I,m as n,j as e,G as $,B as b,A as u,C as r,t as s,y as g,n as O,x as C,k as v,F as w,q as J,H as X,aV as N,aK as se,E as ie,Q as P,u as re,K as ne,b2 as j}from"./index.09f89dc4.js";import{Q as Y}from"./QTd.00e5e315.js";import{Q as de,f as Ae}from"./QTr.2cbfa351.js";import{Q as ue}from"./QTable.5ba31f17.js";import{Q as ce}from"./QSpace.2ea7fb32.js";import{Q as me}from"./QScrollArea.e7fd209f.js";import{Q as ye}from"./QSelect.958fa87e.js";import{Q as ze}from"./QDate.f6067d5f.js";import{Q as We}from"./QPopupProxy.f63a65d9.js";import{Q as Ie}from"./QPage.ce1b4cb5.js";import{C as ve}from"./ClosePopup.712518f2.js";import{u as Ne}from"./vue-i18n.1783a0cb.js";import{U as Be}from"./user.c6f09a36.js";import{A as Fe}from"./attendance.6666db33.js";import"./order.46055623.js";import{f as x}from"./date.6d29930c.js";import{u as Le}from"./dialog.27403fe4.js";import{_ as Ee}from"./DateRangePicker.2cfa5e9c.js";import"./QList.5d1c2d4f.js";import"./use-fullscreen.2a1ec9b4.js";import"./QScrollObserver.942d75c7.js";import"./TouchPan.818d9316.js";import"./selection.2acb415c.js";import"./format.054b8074.js";import"./QItemSection.3e7b5a38.js";import"./QItemLabel.88180eb1.js";import"./QMenu.531c6599.js";import"./i18n.fac3fce5.js";function Ge(m,t){return(t.getTime()-m.getTime())/(1e3*60*60)}function Oe(m){let t=0;const Q=[],y=[];m.forEach(_=>{if(_.clock_in&&_.clock_out){const B=new Date(_.clock_in.clock_time),S=new Date(_.clock_out.clock_time);S>B?(t+=Ge(B,S),Q.push(_)):y.push(_)}else y.push(_)});const p=Q.length,A=p>0?t/p:0;return{workDays:p,totalWorkHours:Math.round(t*100)/100,averageWorkHours:Math.round(A*100)/100,validPairs:Q,invalidPairs:y}}const D={listPeriods:({filter:m,pagination:t})=>h.get("/v1/payrolls/periods",{params:{...m,...t}}),getPeriod:m=>h.get(`/v1/payrolls/periods/${m}`),createPeriod:m=>h.post("/v1/payrolls/periods",m),updatePeriod:m=>h.put(`/v1/payrolls/periods/${m.uuid}`,m),deletePeriod:m=>h.delete(`/v1/payrolls/periods/${m}`),listPayroll:({filter:m,pagination:t})=>h.get("/v1/payrolls",{params:{...m,...t}}),getPayroll:m=>h.get(`/v1/payrolls/${m}`),createPayroll:m=>h.post("/v1/payrolls",m),updatePayroll:(m,t)=>h.put(`/v1/payrolls/${m}`,t),sendMail:(m,t)=>h.post("/v1/payrolls/mails",{period_uuid:m,user_uuids:t})},je={listSalaryItems:()=>h.get("/v1/salary-items")},Ke={class:"row"},Re={class:"row"},Je={class:"q-mr-sm"},Xe={key:0,class:"text-bold text-negative"},Ze={key:1,class:"text-center"},el={key:0,class:"q-mt-sm"},ll={key:0,class:"text-positive text-bold"},al={key:1,class:"text-negative text-bold"},tl={class:"row"},ol={class:"text-h6"},sl={class:"row q-mb-sm items-center"},il={class:"col-12 col-md-3 text-md-center"},rl={class:"col-12 col-md-9"},nl={class:"row"},dl={class:"text-h6"},ul={class:"row q-mb-sm items-center"},cl={class:"col-12 col-md-3 text-md-center"},ml={class:"col-12 col-md-9"},yl={class:"row"},vl={class:"row q-mb-sm items-center"},pl={class:"col-12 col-md-3 text-md-center"},_l={class:"col-12 col-md-9"},fl={key:0,class:"row q-mb-sm items-center"},gl={class:"col-12 col-md-3 text-md-center"},bl={class:"col-12 col-md-9"},kl={key:1,class:"row q-mb-sm items-center"},wl={class:"col-12 col-md-3 text-md-center"},hl={class:"col-12 col-md-9"},Vl={class:"row q-mb-sm items-center"},Pl={class:"col-12 col-md-3 text-md-center"},Dl={class:"col-12 col-md-9"},Ml={class:"row q-mb-sm items-center"},xl={class:"col-12 col-md-3 text-md-center"},Cl={class:"col-12 col-md-9"},Yl={class:"row q-my-md"},ql={class:"col"},Ql={class:"text-h6 text-bold"},Sl={key:0,class:"row q-mb-sm items-center"},Ul={class:"col-12 col-md-3 text-md-center"},Tl={class:"col-12 col-md-9"},Hl={class:"row q-mb-sm items-center"},$l={class:"col-12 col-md-3 text-md-center"},Al={class:"col-12 col-md-9"},zl={class:"row q-my-md"},Wl={class:"col"},Il={class:"text-h6 text-bold"},Nl={key:0,class:"row q-mb-sm items-center"},Bl={class:"col-12 col-md-3 text-md-center"},Fl={class:"col-12 col-md-9"},Ll={class:"row q-mb-sm items-center"},El={class:"col-12 col-md-3 text-md-center"},Gl={class:"col-12 col-md-9"},Ol={class:"row q-my-md"},jl={class:"col"},Kl={class:"text-h6 text-bold"},Rl={class:"row q-mb-sm items-center"},Jl={class:"col-12 col-md-3 text-md-center"},Xl={class:"col-12 col-md-9"},Zl={class:"row q-mb-sm items-center"},ea={class:"col-12 col-md-3 text-md-center"},la={class:"col-12 col-md-9"},aa={class:"row items-center justify-end q-gutter-sm"},ta={class:"row q-mb-sm items-center"},oa={class:"col-12 col-md-3 text-md-center"},sa={class:"col-12 col-md-9"},Ha=Te({__name:"PayrollPage",setup(m){const{t}=Ne(),Q=Le(),y=k(!1),p=k({uuid:"",start_date:"",end_date:"",created_at:""}),A=k([]),_=k({sortBy:"created_at",descending:!0,page:1,rowsPerPage:20,rowsNumber:0}),B=W(()=>[{name:"payroll_period",label:t("payroll.period"),align:"left",field:"payroll_period"},{name:"created_at",label:t("createdAt"),align:"left",field:"created_at"},{name:"actions",label:t("actions"),align:"center",field:"actions"}]),S=async()=>{const i=await D.listPeriods({pagination:_.value});A.value=i.result.data,_.value=i.result.pagination},U=k(!1),M=k({uuid:"",start_date:"",end_date:"",created_at:""}),Z=W({get:()=>({from:M.value.start_date,to:M.value.end_date}),set:i=>{M.value.start_date=i.from,M.value.end_date=i.to}}),pe=()=>{U.value=!0,M.value={uuid:"",start_date:"",end_date:"",created_at:""}},_e=i=>{U.value=!0,M.value={...i}},fe=i=>{Q.showMessage({title:"",message:t("confirmDelete"),timeout:0,ok:async()=>{try{if(y.value=!0,!i)return;await D.deletePeriod(i),j.create({type:"positive",message:t("success"),position:"top"})}finally{y.value=!1,S()}}})},K=()=>{U.value=!1},ge=async()=>{try{y.value=!0,M.value.uuid?await D.updatePeriod(M.value):await D.createPeriod(M.value)}finally{y.value=!1,S(),K()}},T=k([]),be=W(()=>[{name:"user_name",label:t("name"),align:"left",field:i=>i.user.name},{name:"salary_type",label:t("salaryType.label"),align:"left",field:"salary_type"},{name:"net_salary",label:t("salary"),align:"left",field:"net_salary"},{name:"created_at",label:t("createdAt"),align:"left",field:"created_at"},{name:"mails",label:t("email.label"),align:"center",field:"mails"}]),F=k({sortBy:"",descending:!0,page:1,rowsPerPage:20,rowsNumber:0}),ke=i=>{p.value=i},we=()=>{p.value={uuid:"",start_date:"",end_date:"",created_at:""}},ee=k([]),L=k([]),he=async()=>{try{y.value=!0,await D.sendMail(p.value.uuid,T.value.map(i=>i.user.uuid)),j.create({type:"positive",message:t("success"),position:"top"})}finally{y.value=!1,E()}},Ve=async i=>{try{y.value=!0,await D.sendMail(p.value.uuid,[i.uuid]),j.create({type:"positive",message:t("success"),position:"top"})}finally{y.value=!1,E()}};He(async()=>{try{y.value=!0;const[i,a,V]=await Promise.all([Be.fetch(),je.listSalaryItems(),D.listPeriods({pagination:_.value})]);ee.value=i.result,L.value=a.result,A.value=V.result.data,_.value=V.result.pagination}finally{y.value=!1}});const Pe=i=>{T.value=[];for(const a of ee.value)if(!i.result.data.find(o=>o.user.uuid===a.uuid)){const o=[];for(const f of L.value)o.push({salary_item:f,amount:0});T.value.push({period:p.value,user:{uuid:a.uuid,name:a.name,email:a.email},net_salary:0,payroll_details:[...o],emails:[]})}T.value=[...T.value,...i.result.data],F.value=i.result.pagination},E=async()=>{try{y.value=!0;const i=await D.listPayroll({filter:{period_uuid:p.value.uuid},pagination:F.value});Pe(i)}finally{y.value=!1}},q=k(!1),l=k(),De=W(()=>[{value:1,name:t("salaryType.salary")},{value:2,name:t("salaryType.hourly")}]),Me=W(()=>[{value:1,name:t("payroll.unpaid")},{value:2,name:t("payroll.paid")}]),xe=i=>{l.value={...i,period:p.value,user_uuid:i.user.uuid,salary_type:1,status:1},le(),q.value=!0},Ce=i=>{!i.uuid||(l.value={...i,user_uuid:i.user.uuid},le(),q.value=!0)},H=k(),le=async()=>{var i;try{ae();const V=(await Fe.listClockPairs({filter:{start_date:p.value.start_date,end_date:p.value.end_date,user_uuid:(i=l.value)==null?void 0:i.user_uuid}})).result.data;H.value=Oe(V)}catch{ae()}},ae=()=>{H.value={workDays:0,totalWorkHours:0,averageWorkHours:0,validPairs:[],invalidPairs:[]}},G=i=>{var o,f;const a=(o=l.value)==null?void 0:o.payroll_details.find(d=>d.salary_item.id===i.id);if(a)return a;const V={salary_item:i,amount:0};return(f=l.value)==null||f.payroll_details.push(V),V},Ye=()=>{if(!l.value)return;let i=0;l.value.salary_type===1?i=l.value.basic_salary||0:l.value.salary_type===2&&(i=l.value.hourly_salary||0,i*=l.value.work_hours||0);for(const a of l.value.payroll_details)(a.salary_item.type===1||a.salary_item.type===3)&&(i+=a.amount||0);l.value.gross_salary=Math.round(i*100)/100},qe=()=>{if(!l.value)return;let i=0;for(const a of l.value.payroll_details)a.salary_item.type===2&&(i+=a.amount||0);l.value.total_deductions=Math.round(i*100)/100},Qe=()=>{if(!l.value)return;const i=(l.value.gross_salary||0)-(l.value.total_deductions||0);l.value.net_salary=Math.round(i*100)/100},Se=()=>{Ye(),qe(),Qe()},Ue=async()=>{var i,a;if(!!l.value)try{y.value=!0,l.value.salary_type===1?l.value.hourly_salary=0:l.value.salary_type===2&&(l.value.basic_salary=0),!l.value.work_days&&l.value.work_days!==0&&(l.value.work_days=((i=H.value)==null?void 0:i.workDays)||0),!l.value.work_hours&&l.value.work_hours!==0&&(l.value.work_hours=((a=H.value)==null?void 0:a.totalWorkHours)||0),l.value.uuid?await D.updatePayroll(l.value.uuid,l.value):await D.createPayroll({...l.value,period_uuid:p.value.uuid}),j.create({type:"positive",message:t("success"),position:"top"}),q.value=!1}finally{E()}};return oe(()=>p.value,()=>{E()}),oe(()=>l.value,()=>{Se()},{deep:!0}),(i,a)=>{const V=$e("TablePagination");return c(),I(Ie,null,{default:n(()=>[e(X,{flat:"",square:"",class:"bg-cream"},{default:n(()=>[p.value.uuid?(c(),I($,{key:1},{default:n(()=>[e(ue,{"virtual-scroll":"",rows:T.value,columns:be.value,pagination:F.value,"onUpdate:pagination":a[2]||(a[2]=o=>F.value=o),"hide-pagination":"","table-header-class":"bg-grey-3",loading:y.value},{top:n(()=>[e(z,null,{default:n(()=>[s("div",Re,[e(g,{type:"button",icon:"arrow_back",onClick:we,flat:"",dense:""}),e(te,null,{default:n(()=>[b(u(r(t)("payroll.label")),1)]),_:1})])]),_:1}),e(z,{class:"items-center q-mb-md"},{default:n(()=>[s("span",Je,u(r(t)("payroll.period"))+"\uFF1A",1),b(" "+u(r(x)(p.value.start_date,"YYYY-MM-DD"))+" ~ "+u(r(x)(p.value.end_date,"YYYY-MM-DD")),1)]),_:1}),e(z,{class:"items-center"},{default:n(()=>[e(g,{type:"button",onClick:he,label:r(t)("email.sendAll"),icon:"mail",color:"primary","no-caps":"",loading:y.value},null,8,["label","loading"])]),_:1})]),body:n(o=>[e(de,{clickable:"",onClick:f=>Ce(o.row),props:o},{default:n(()=>[e(Y,{key:"user_name",props:o},{default:n(()=>[o.row.uuid?C("",!0):(c(),I(g,{key:0,type:"button",onClick:O(f=>xe(o.row),["stop"]),icon:"add",color:"positive",size:"sm",class:"q-pa-xs q-mr-sm"},null,8,["onClick"])),b(" "+u(o.row.user.name),1)]),_:2},1032,["props"]),e(Y,{key:"salary_type",props:o},{default:n(()=>[o.row.salary_type===1?(c(),v(w,{key:0},[b(u(r(t)("salaryType.salary")),1)],64)):o.row.salary_type===2?(c(),v(w,{key:1},[b(u(r(t)("salaryType.hourly")),1)],64)):(c(),v(w,{key:2},[b(" - ")],64))]),_:2},1032,["props"]),e(Y,{key:"net_salary",props:o},{default:n(()=>[o.row.uuid?(c(),v(w,{key:0},[b(" AU$ "+u(r(Ae)(o.row.net_salary,2)),1)],64)):(c(),v(w,{key:1},[b(" 0 ")],64))]),_:2},1032,["props"]),e(Y,{key:"created_at",props:o},{default:n(()=>[o.row.uuid?(c(),v(w,{key:0},[b(u(r(x)(o.row.created_at,"YYYY-MM-DD HH:mm")),1)],64)):(c(),v(w,{key:1},[b(" - ")],64))]),_:2},1032,["props"]),e(Y,{key:"mails",props:o},{default:n(()=>{var f;return[o.row.uuid?(c(),v(w,{key:0},[o.row.user.email?(c(),v("span",Ze,[e(g,{type:"button",onClick:O(d=>Ve(o.row.user),["stop"]),icon:"mail",color:"primary",size:"sm",loading:y.value},null,8,["onClick","loading"]),((f=o.row.emails)==null?void 0:f.length)>0?(c(),v("div",el,[o.row.emails[o.row.emails.length-1].status==="sent"?(c(),v("span",ll,[e(J,{name:"check",size:"sm"})])):(c(),v("span",al,[e(J,{name:"close",size:"sm"}),a[20]||(a[20]=b('" '))])),b(" "+u(r(x)(o.row.emails[o.row.emails.length-1].sent_at,"YYYY-MM-DD HH:mm")),1)])):C("",!0)])):(c(),v("div",Xe,u(r(t)("email.notSet")),1))],64)):C("",!0)]}),_:2},1032,["props"])]),_:2},1032,["onClick","props"])]),_:1},8,["rows","columns","pagination","loading"])]),_:1})):(c(),I($,{key:0},{default:n(()=>[e(ue,{rows:A.value,columns:B.value,pagination:_.value,"onUpdate:pagination":a[0]||(a[0]=o=>_.value=o),"hide-pagination":"","table-header-class":"bg-grey-3",loading:y.value},{top:n(()=>[e(z,null,{default:n(()=>[e(te,{class:"text-h6 q-ml-sm"},{default:n(()=>[b(u(r(t)("payroll.label")),1)]),_:1})]),_:1}),e(z,null,{default:n(()=>[s("div",Ke,[e(g,{onClick:pe,color:"positive",icon:"add",size:"sm",class:"q-pa-sm"})])]),_:1})]),body:n(o=>[e(de,{clickable:"",onClick:f=>ke(o.row),props:o},{default:n(()=>[e(Y,{key:"payroll_period",props:o},{default:n(()=>[b(u(r(x)(o.row.start_date,"YYYY-MM-DD"))+" ~ "+u(r(x)(o.row.end_date,"YYYY-MM-DD")),1)]),_:2},1032,["props"]),e(Y,{key:"created_at",props:o},{default:n(()=>[b(u(r(x)(o.row.created_at,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"]),e(Y,{key:"actions",props:o},{default:n(()=>[e(g,{type:"button",icon:"visibility",color:"positive",size:"sm",class:"q-mr-sm q-pa-sm"}),e(g,{type:"button",icon:"edit",onClick:O(f=>_e(o.row),["stop"]),color:"positive",size:"sm",class:"q-mr-sm q-pa-sm"},null,8,["onClick"]),e(g,{type:"button",icon:"delete",onClick:O(f=>fe(o.row.uuid),["stop"]),color:"negative",size:"sm",class:"q-pa-sm"},null,8,["onClick"])]),_:2},1032,["props"])]),_:2},1032,["onClick","props"])]),_:1},8,["rows","columns","pagination","loading"]),e(V,{modelValue:_.value,"onUpdate:modelValue":a[1]||(a[1]=o=>_.value=o),onGetData:S},null,8,["modelValue"])]),_:1}))]),_:1}),e(ie,{modelValue:U.value,"onUpdate:modelValue":a[4]||(a[4]=o=>U.value=o),class:"card-dialog",persistent:""},{default:n(()=>[e(X,{class:"column full-height"},{default:n(()=>[e(me,{class:"col col-11"},{default:n(()=>[e($,null,{default:n(()=>[s("div",tl,[s("span",ol,u(r(t)("payroll.period")),1),e(ce),e(g,{type:"button",icon:"close",onClick:K,dense:"",flat:""})])]),_:1}),e(N),e($,null,{default:n(()=>[s("div",sl,[s("div",il,u(r(t)("payroll.period")),1),s("div",rl,[e(Ee,{modelValue:Z.value,"onUpdate:modelValue":a[3]||(a[3]=o=>Z.value=o)},null,8,["modelValue"])])])]),_:1})]),_:1}),e(se,{class:"col col-1 bg-grey-2",align:"between"},{default:n(()=>[e(g,{type:"button",label:r(t)("close"),color:"negative",onClick:K,loading:y.value},null,8,["label","loading"]),e(g,{type:"button",label:r(t)("submit"),color:"positive",onClick:ge,loading:y.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(ie,{modelValue:q.value,"onUpdate:modelValue":a[19]||(a[19]=o=>q.value=o),class:"card-dialog",persistent:""},{default:n(()=>[e(X,{class:"column full-height"},{default:n(()=>[e(me,{class:"col col-11"},{default:n(()=>[e($,null,{default:n(()=>{var o;return[s("div",nl,[s("span",dl,u(r(t)("payroll.label"))+" \uFF0D "+u((o=l.value)==null?void 0:o.user.name),1),e(ce),e(g,{type:"button",icon:"close",onClick:a[5]||(a[5]=f=>q.value=!1),dense:"",flat:""})])]}),_:1}),e(N),l.value?(c(),I($,{key:0},{default:n(()=>{var o,f;return[s("div",ul,[s("div",cl,u(r(t)("payroll.period")),1),s("div",ml,[s("div",yl,u(r(x)(l.value.period.start_date,"YYYY-MM-DD"))+" ~ "+u(r(x)(l.value.period.end_date,"YYYY-MM-DD")),1)])]),s("div",vl,[s("div",pl,u(r(t)("salaryType.label")),1),s("div",_l,[e(ye,{modelValue:l.value.salary_type,"onUpdate:modelValue":a[6]||(a[6]=d=>l.value.salary_type=d),options:De.value,"option-label":"name","option-value":"value","emit-value":"","map-options":"",dense:""},null,8,["modelValue","options"])])]),l.value.salary_type===1?(c(),v("div",fl,[s("div",gl,u(r(t)("payroll.basicSalary")),1),s("div",bl,[e(P,{modelValue:l.value.basic_salary,"onUpdate:modelValue":a[7]||(a[7]=d=>l.value.basic_salary=d),modelModifiers:{number:!0},type:"number",dense:""},null,8,["modelValue"])])])):C("",!0),l.value.salary_type===2?(c(),v("div",kl,[s("div",wl,u(r(t)("payroll.hourlyRate")),1),s("div",hl,[e(P,{modelValue:l.value.hourly_salary,"onUpdate:modelValue":a[8]||(a[8]=d=>l.value.hourly_salary=d),modelModifiers:{number:!0},type:"number",dense:""},null,8,["modelValue"])])])):C("",!0),s("div",Vl,[s("div",Pl,u(r(t)("payroll.workDays")),1),s("div",Dl,[e(P,{modelValue:l.value.work_days,"onUpdate:modelValue":a[9]||(a[9]=d=>l.value.work_days=d),modelModifiers:{number:!0},type:"number",placeholder:(o=H.value)==null?void 0:o.workDays,dense:""},null,8,["modelValue","placeholder"])])]),s("div",Ml,[s("div",xl,u(r(t)("payroll.workHours")),1),s("div",Cl,[e(P,{modelValue:l.value.work_hours,"onUpdate:modelValue":a[10]||(a[10]=d=>l.value.work_hours=d),modelModifiers:{number:!0},type:"number",placeholder:(f=H.value)==null?void 0:f.totalWorkHours,dense:""},null,8,["modelValue","placeholder"])])]),s("div",Yl,[s("div",ql,[s("div",Ql,u(r(t)("payroll.bonus")),1),e(N,{color:"grey",size:"2px"})])]),(c(!0),v(w,null,re(L.value,d=>(c(),v(w,{key:d.id},[d.type===1||d.type===3?(c(),v("div",Sl,[s("div",Ul,u(r(t)(`payroll.${d.name}`)),1),s("div",Tl,[e(P,{modelValue:G(d).amount,"onUpdate:modelValue":R=>G(d).amount=R,modelModifiers:{number:!0},type:"number",dense:""},null,8,["modelValue","onUpdate:modelValue"])])])):C("",!0)],64))),128)),s("div",Hl,[s("div",$l,u(r(t)("payroll.grossSalary")),1),s("div",Al,[e(P,{modelValue:l.value.gross_salary,"onUpdate:modelValue":a[11]||(a[11]=d=>l.value.gross_salary=d),modelModifiers:{number:!0},type:"number",dense:"",filled:"",readonly:""},null,8,["modelValue"])])]),s("div",zl,[s("div",Wl,[s("div",Il,u(r(t)("payroll.deduction")),1),e(N,{color:"grey",size:"2px"})])]),(c(!0),v(w,null,re(L.value,d=>(c(),v(w,{key:d.id},[d.type===2?(c(),v("div",Nl,[s("div",Bl,u(r(t)(`payroll.${d.name}`)),1),s("div",Fl,[e(P,{modelValue:G(d).amount,"onUpdate:modelValue":R=>G(d).amount=R,modelModifiers:{number:!0},type:"number",dense:""},null,8,["modelValue","onUpdate:modelValue"])])])):C("",!0)],64))),128)),s("div",Ll,[s("div",El,u(r(t)("payroll.totalDeductions")),1),s("div",Gl,[e(P,{modelValue:l.value.total_deductions,"onUpdate:modelValue":a[12]||(a[12]=d=>l.value.total_deductions=d),modelModifiers:{number:!0},type:"number",dense:"",filled:"",readonly:""},null,8,["modelValue"])])]),s("div",Ol,[s("div",jl,[s("div",Kl,u(r(t)("total")),1),e(N,{color:"grey",size:"2px"})])]),s("div",Rl,[s("div",Jl,u(r(t)("payroll.netSalary")),1),s("div",Xl,[e(P,{modelValue:l.value.net_salary,"onUpdate:modelValue":a[13]||(a[13]=d=>l.value.net_salary=d),modelModifiers:{number:!0},type:"number",dense:"",filled:"",readonly:""},null,8,["modelValue"])])]),s("div",Zl,[s("div",ea,u(r(t)("payroll.payDate")),1),s("div",la,[e(P,{modelValue:l.value.pay_date,"onUpdate:modelValue":a[16]||(a[16]=d=>l.value.pay_date=d),type:"text",mask:"date",dense:""},{prepend:n(()=>[e(J,{name:"event",class:"cursor-pointer"},{default:n(()=>[e(We,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:n(()=>[e(ze,{modelValue:l.value.pay_date,"onUpdate:modelValue":a[15]||(a[15]=d=>l.value.pay_date=d),"today-btn":""},{default:n(()=>[s("div",aa,[ne(e(g,{label:r(t)("clear"),color:"negative",onClick:a[14]||(a[14]=d=>l.value.pay_date=""),flat:""},null,8,["label"]),[[ve]]),ne(e(g,{label:r(t)("close"),color:"primary",flat:""},null,8,["label"]),[[ve]])])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),s("div",ta,[s("div",oa,u(r(t)("payroll.payStatus")),1),s("div",sa,[e(ye,{modelValue:l.value.status,"onUpdate:modelValue":a[17]||(a[17]=d=>l.value.status=d),options:Me.value,"option-label":"name","option-value":"value","emit-value":"","map-options":"",dense:""},null,8,["modelValue","options"])])])]}),_:1})):C("",!0)]),_:1}),e(se,{class:"col col-1 bg-grey-2",align:"between"},{default:n(()=>[e(g,{type:"button",label:r(t)("close"),color:"negative",onClick:a[18]||(a[18]=o=>q.value=!1),loading:y.value},null,8,["label","loading"]),e(g,{type:"button",label:r(t)("submit"),color:"positive",onClick:Ue,loading:y.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}});export{Ha as default};
