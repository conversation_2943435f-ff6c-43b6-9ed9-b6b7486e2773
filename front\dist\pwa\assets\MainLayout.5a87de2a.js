import{d as Q,aI as b,aJ as h,r as w,c as C,z as r,p as y,v as L,m as o,j as e,y as l,B as M,A as u,C as i,t as V,I as x}from"./index.09f89dc4.js";import{Q as B,a as I}from"./QToolbar.b89be0c0.js";import{Q as T}from"./QHeader.9ec4321b.js";import{Q as A}from"./QDrawer.e9e5d86a.js";import{Q as N,a as S}from"./QLayout.3422dc25.js";import{u as q}from"./vue-i18n.1783a0cb.js";import{u as D}from"./pageInfo.4e4da9b8.js";import{u as H}from"./dialog.27403fe4.js";import"./QScrollObserver.942d75c7.js";import"./TouchPan.818d9316.js";import"./selection.2acb415c.js";import"./format.054b8074.js";const P={class:"q-mr-sm"},$=Q({__name:"MainLayout",setup(U){const m=b(),d=D(),s=h(),c=H(),{t:a}=q(),t=w(!1),p=C(()=>[{title:a("attendance.label"),link:"/attendance"},{title:a("order.label"),link:"/order"}]),f=()=>{t.value=!t.value},g=()=>{c.showMessage({message:a("logoutConfirm"),timeout:0,ok:async()=>{await x.logout(),s.logout(),m.push("/login")}})};return(j,n)=>{const _=r("MenuLink"),k=r("router-view");return y(),L(S,{view:"hHr Lpr lFr",class:"shadow-2 rounded-borders"},{default:o(()=>[e(T,{elevated:""},{default:o(()=>[e(B,null,{default:o(()=>[e(l,{flat:"",round:"",dense:"",icon:"menu","aria-label":"Menu",onClick:f}),e(I,null,{default:o(()=>[M(u(i(d).pageTitle),1)]),_:1}),V("span",P,u(i(s).getUserName),1),e(l,{flat:"",round:"",dense:"",icon:"logout",onClick:g})]),_:1})]),_:1}),e(A,{modelValue:t.value,"onUpdate:modelValue":n[0]||(n[0]=v=>t.value=v),bordered:"",overlay:""},{default:o(()=>[e(_,{"link-list":p.value},null,8,["link-list"])]),_:1},8,["modelValue"]),e(N,{class:"q-mt-lg q-px-md",style:{"background-color":"#fef9f2"}},{default:o(()=>[e(k)]),_:1})]),_:1})}}});export{$ as default};
