<template>
  <q-page>
    <q-card square class="column q-pa-sm">
      <!-- 按鈕區 -->
      <div class="col-2 col-md-1 full-width">
        <template v-if="tab === 'onsite'">
          <!-- 新增訂單按鈕 -->
          <q-btn
            type="button"
            color="create"
            class="q-mr-md"
            @click="createNewOrder"
            :loading="isCreating"
          >
            <q-icon name="post_add" size="md" />
          </q-btn>
          <!-- 查看歷史紀錄 -->
          <q-btn type="button" color="origin" @click="showHistory">
            <q-icon name="history" size="md" />
          </q-btn>
        </template>
        <template v-else-if="tab === 'online'">
          <!-- 查看歷史紀錄 -->
          <q-btn type="button" color="origin" @click="showWCHistory">
            <q-icon name="history" size="md" />
          </q-btn>
        </template>

        <q-separator class="q-mt-md" />
      </div>

      <!-- 未結帳訂單列表 -->
      <div class="col-10 col-md-11 full-width q-pt-md">
        <!-- tabs -->
        <q-tabs v-model="tab" dense active-color="primary" align="left">
          <q-tab name="onsite" icon="storefront" :label="t('onsite')">
            <q-badge color="red" floating v-if="onsiteOrders.length > 0">
              {{ onsiteOrders.length }}
            </q-badge>
          </q-tab>
          <q-tab name="online" icon="cloud" :label="t('online')">
            <q-badge color="red" floating v-if="onlineOrders.length > 0">
              {{ onlineOrders.length }}
            </q-badge>
          </q-tab>
        </q-tabs>
        <!-- panels -->
        <q-tab-panels v-model="tab" class="full-height">
          <!-- onsite -->
          <q-tab-panel name="onsite">
            <q-table
              virtual-scroll
              :rows="onsiteOrders"
              :columns="columns"
              row-key="uuid"
              :rows-per-page-options="[0]"
              hide-pagination
              class="sticky-scroll-table"
              :loading="isLoading"
            >
              <template v-slot:body="props">
                <q-tr
                  clickable
                  :props="props"
                  @click="goToCheckout(props.row.uuid)"
                >
                  <q-td :props="props" key="order_no">
                    {{ props.row.order_no }}
                  </q-td>
                  <q-td :props="props" key="order_at">
                    {{ formatDate(props.row.order_at, 'YYYY/MM/DD') }}
                  </q-td>
                  <q-td :props="props" key="customer">
                    <template v-if="props.row.customer.name">
                      {{ props.row.customer.name }}
                    </template>
                    <template v-else>
                      {{ t('unknown.customer') }}
                    </template>
                  </q-td>
                  <q-td :props="props" key="total" class="text-bold">
                    AU$ {{ formatNumber(props.row.total, 2) }}
                  </q-td>
                  <q-td :props="props" key="actions">
                    <q-btn
                      flat
                      dense
                      type="button"
                      icon="delete"
                      color="negative"
                      @click.stop="deletePendingOrder(props.row.uuid)"
                    />
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </q-tab-panel>
          <!-- online  -->
          <q-tab-panel name="online">
            <q-table
              virtual-scroll
              :rows="onlineOrders"
              :columns="wcColumns"
              row-key="id"
              :rows-per-page-options="[0]"
              hide-pagination
              class="sticky-scroll-table"
            >
              <template v-slot:body="props">
                <q-tr
                  clickable
                  @click="showWCOrderDetail(props.row.id)"
                  :props="props"
                >
                  <q-td :props="props" key="id">
                    {{ props.row.id }}
                  </q-td>
                  <q-td :props="props" key="date_created">
                    {{ formatDate(props.row.date_created, 'YYYY/MM/DD') }}
                  </q-td>
                  <q-td :props="props" key="customer_name">
                    {{ props.row.customer_name }}
                  </q-td>
                  <q-td :props="props" key="total" class="text-bold">
                    AU$ {{ formatNumber(props.row.total, 2) }}
                  </q-td>
                  <q-td>
                    {{ t(props.row.status) }}
                  </q-td>
                </q-tr>
              </template>
            </q-table>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </q-card>

    <OrderHistoryDialog v-model="showHistoryDialog" />

    <WCOrderHistoryDialog
      v-model="showWCHistoryDialog"
      @refresh="getOnlineOrders"
    />
    <WCOrderDetailDialog
      v-model="showWCOrderDetailDialog"
      :orderID="wcOrderID"
      @refresh="getOnlineOrders"
    />
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import OrderHistoryDialog from './components/OrderHistoryDialog.vue';
import WCOrderHistoryDialog from './components/WCOrderHistoryDialog.vue';
import WCOrderDetailDialog from './components/WCOrderDetailDialog.vue';
import { OrderApi, Order, WCOrderInfo } from '@/api/order';
import { formatDate, formatNumber } from '@/utils';

const { t } = useI18n();
const router = useRouter();
const tab = ref('onsite');

const columns = computed(() => [
  {
    name: 'order_no',
    label: t('orderNo'),
    field: 'order_no',
    align: 'left' as const,
  },
  {
    name: 'order_at',
    label: t('orderDate'),
    field: 'order_at',
    align: 'left' as const,
  },
  {
    name: 'customer',
    label: t('customer.label'),
    field: 'customer',
    align: 'left' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'left' as const,
  },
  {
    name: 'actions',
    label: t('actions'),
    field: 'actions',
    align: 'center' as const,
  },
]);

const wcColumns = computed(() => [
  {
    name: 'id',
    label: t('orderNo'),
    field: 'id',
    align: 'left' as const,
  },
  {
    name: 'date_created',
    label: t('orderDate'),
    field: 'date_created',
    align: 'left' as const,
  },
  {
    name: 'customer_name',
    label: t('customer.label'),
    field: 'customer_name',
    align: 'left' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'left' as const,
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'left' as const,
  },
]);

const onsiteOrders = ref<Order[]>([]);
const onlineOrders = ref<WCOrderInfo[]>([]);

const isCreating = ref(false);
const createNewOrder = async () => {
  try {
    isCreating.value = true;
    const response = await OrderApi.create();
    const orderID = response.result.uuid;

    router.push(`/order/${orderID}`);
  } finally {
    isCreating.value = false;
  }
};

const deletePendingOrder = async (uuid: string) => {
  try {
    isLoading.value = true;

    await OrderApi.deleteOrder(uuid);
  } finally {
    getOnsiteOrders();
  }
};

const goToCheckout = (orderUUID: string) => {
  router.push(`/order/${orderUUID}`);
};

const isLoading = ref(false);
const getOnsiteOrders = async () => {
  try {
    isLoading.value = true;
    const response = await OrderApi.fetch({
      filter: {
        status: ['pending'],
      },
      pagination: {
        page: 1,
        rowsPerPage: -1,
        sortBy: 'order_at',
        descending: true,
        rowsNumber: 0,
      },
    });
    onsiteOrders.value = response.result.data;
  } finally {
    isLoading.value = false;
  }
};

const getOnlineOrders = async () => {
    const response = await OrderApi.wcFetchPending();

    onlineOrders.value = response.result.data;
};

const fetchData = () => {
  getOnsiteOrders();
  getOnlineOrders();
};

const showHistoryDialog = ref(false);
const showHistory = () => {
  showHistoryDialog.value = true;
};

// Wordpress
const showWCHistoryDialog = ref(false);
const showWCHistory = () => {
  showWCHistoryDialog.value = true;
};

let wcOrderID = ref(0);
const showWCOrderDetailDialog = ref(false);
const showWCOrderDetail = (id: number) => {
  wcOrderID.value = id;
  showWCOrderDetailDialog.value = true;
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss">
.q-page {
  > .q-card {
    min-height: 100vh;
    height: auto;
  }
}
</style>
