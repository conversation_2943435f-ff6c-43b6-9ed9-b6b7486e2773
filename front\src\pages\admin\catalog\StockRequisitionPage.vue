<template>
  <div class="stock-requisition-page">
    <div class="page-header">
      <h1 class="text-h4 q-mb-md">庫存領用</h1>

      <!-- 基本資訊卡片 -->
      <q-card class="basic-info-card">
        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- 領用單號 -->
            <div class="col-12 col-md-3">
              <q-input
                v-model="requisitionInfo.requisitionNumber"
                label="領用單號"
                readonly
                outlined
                dense
              />
            </div>

            <!-- 領用日期 -->
            <div class="col-12 col-md-3">
              <q-input
                v-model="requisitionInfo.date"
                label="領用日期"
                type="date"
                outlined
                dense
              />
            </div>

            <!-- 領用部門 -->
            <div class="col-12 col-md-3">
              <q-select
                v-model="requisitionInfo.department"
                :options="departmentOptions"
                label="領用部門"
                outlined
                dense
              />
            </div>

            <!-- 領用人員 -->
            <div class="col-12 col-md-3">
              <q-input
                v-model="requisitionInfo.requester"
                label="領用人員"
                outlined
                dense
              />
            </div>

            <!-- 用途說明 -->
            <div class="col-12">
              <q-input
                v-model="requisitionInfo.purpose"
                label="用途說明"
                type="textarea"
                outlined
                dense
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 商品列表卡片 -->
      <q-card class="products-card">
        <q-card-section>
          <div class="row items-center justify-between q-mb-md">
            <div class="text-h6">領用商品</div>
            <div class="action-buttons">
              <q-btn
                color="primary"
                icon="add"
                label="新增商品"
                @click="openAddProductDialog"
              />
              <q-btn
                color="secondary"
                icon="qr_code_scanner"
                label="掃描條碼"
                @click="scanBarcode"
              />
            </div>
          </div>

          <!-- 商品表格 -->
          <q-table
            :rows="requisitionItems"
            :columns="columns"
            row-key="id"
            flat
            bordered
          >
            <template #body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  flat
                  round
                  color="negative"
                  icon="delete"
                  @click="removeItem(props.row.id)"
                >
                  <q-tooltip>移除</q-tooltip>
                </q-btn>
              </q-td>
            </template>
          </q-table>

          <!-- 總計區域 -->
          <div class="summary-section q-mt-md">
            <div class="row justify-end">
              <div class="col-12 col-md-4">
                <div class="summary-card">
                  <div class="row q-mb-sm">
                    <div class="col-6 text-right q-pr-md">商品總數：</div>
                    <div class="col-6 text-right total-quantity">
                      {{ totalQuantity }} 件
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 提交按鈕 -->
      <div class="row justify-end q-mt-md">
        <q-btn color="primary" label="提交領用單" @click="submitRequisition" />
      </div>
    </div>

    <!-- 新增商品對話框 -->
    <q-dialog v-model="addProductDialog" no-refocus>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">新增領用商品</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-select
            v-model="newProduct"
            :options="productOptions"
            label="選擇商品"
            @update:model-value="onProductSelected"
            outlined
            dense
          />
          <q-input
            v-model.number="newProductQuantity"
            label="數量"
            type="number"
            outlined
            dense
            class="q-mt-md"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="確定" color="primary" @click="addProduct" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, reactive } from 'vue';
import { useQuasar } from 'quasar';

interface RequisitionInfo {
  requisitionNumber: string;
  date: string;
  department: string;
  requester: string;
  purpose: string;
}

interface RequisitionItem {
  id: string;
  productId: string;
  name: string;
  barcode: string;
  unit: string;
  quantity: number;
  currentStock: number;
}

interface Product {
  id: string;
  name: string;
  barcode: string;
  unit: string;
  currentStock: number;
}

interface SelectOption {
  label: string;
  value: Product;
}

export default defineComponent({
  name: 'StockRequisitionPage',
  setup() {
    const $q = useQuasar();

    // 領用單基本資訊
    const requisitionInfo = reactive<RequisitionInfo>({
      requisitionNumber: `REQ-${new Date().getTime().toString().slice(-6)}`,
      date: new Date().toISOString().slice(0, 10),
      department: '',
      requester: '',
      purpose: '',
    });

    // 部門選項
    const departmentOptions = [
      '行政部',
      '業務部',
      '倉儲部',
      '採購部',
      '人事部',
    ];

    // 領用商品列表
    const requisitionItems = ref<RequisitionItem[]>([]);

    // 新增商品對話框
    const addProductDialog = ref(false);
    const newProduct = ref<SelectOption | null>(null);
    const newProductQuantity = ref(1);

    // 商品選項（模擬資料）
    const productOptions = [
      {
        label: '商品A',
        value: {
          id: 'prod-001',
          name: '商品A',
          barcode: '4710001',
          unit: '個',
          currentStock: 100,
        },
      },
      {
        label: '商品B',
        value: {
          id: 'prod-002',
          name: '商品B',
          barcode: '4710002',
          unit: '個',
          currentStock: 50,
        },
      },
    ] as SelectOption[];

    // 表格列定義
    const columns = [
      {
        name: 'name',
        label: '商品名稱',
        field: 'name',
        align: 'left' as const,
      },
      {
        name: 'barcode',
        label: '條碼',
        field: 'barcode',
        align: 'left' as const,
      },
      { name: 'unit', label: '單位', field: 'unit', align: 'center' as const },
      {
        name: 'quantity',
        label: '領用數量',
        field: 'quantity',
        align: 'center' as const,
      },
      {
        name: 'currentStock',
        label: '目前庫存',
        field: 'currentStock',
        align: 'center' as const,
      },
      {
        name: 'actions',
        label: '操作',
        field: 'actions',
        align: 'center' as const,
      },
    ];

    // 計算總數量
    const totalQuantity = computed(() => {
      return requisitionItems.value.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
    });

    // 打開新增商品對話框
    function openAddProductDialog() {
      newProduct.value = null;
      newProductQuantity.value = 1;
      addProductDialog.value = true;
    }

    // 選擇商品時的處理
    function onProductSelected(selected: SelectOption | null) {
      newProduct.value = selected;
    }

    // 新增商品
    function addProduct() {
      const selected = newProduct.value;
      if (!selected) {
        $q.notify({
          type: 'negative',
          message: '請選擇商品',
          icon: 'warning',
        });
        return;
      }

      if (newProductQuantity.value <= 0) {
        $q.notify({
          type: 'negative',
          message: '請輸入有效的數量',
          icon: 'warning',
        });
        return;
      }

      if (newProductQuantity.value > selected.value.currentStock) {
        $q.notify({
          type: 'negative',
          message: '領用數量不能超過目前庫存',
          icon: 'warning',
        });
        return;
      }

      const newItem: RequisitionItem = {
        id: `item-${Date.now()}`,
        productId: selected.value.id,
        name: selected.value.name,
        barcode: selected.value.barcode,
        unit: selected.value.unit,
        quantity: newProductQuantity.value,
        currentStock: selected.value.currentStock,
      };

      requisitionItems.value.push(newItem);
      addProductDialog.value = false;

      $q.notify({
        type: 'positive',
        message: '商品已新增',
        icon: 'check',
      });
    }

    // 移除商品
    function removeItem(itemId: string) {
      const index = requisitionItems.value.findIndex(
        (item) => item.id === itemId
      );
      if (index !== -1) {
        requisitionItems.value.splice(index, 1);

        $q.notify({
          type: 'info',
          message: '商品已移除',
          icon: 'delete',
        });
      }
    }

    // 掃描條碼
    function scanBarcode() {
      $q.notify({
        type: 'info',
        message: '條碼掃描功能尚未實作',
        icon: 'qr_code_scanner',
      });
    }

    // 提交領用單
    function submitRequisition() {
      if (requisitionItems.value.length === 0) {
        $q.notify({
          type: 'negative',
          message: '請至少新增一項領用商品',
          icon: 'warning',
        });
        return;
      }

      if (!requisitionInfo.department || !requisitionInfo.requester) {
        $q.notify({
          type: 'negative',
          message: '請填寫完整的領用資訊',
          icon: 'warning',
        });
        return;
      }

      console.log('提交領用單', {
        info: requisitionInfo,
        items: requisitionItems.value,
        summary: {
          totalQuantity: totalQuantity.value,
        },
      });

      $q.notify({
        type: 'positive',
        message: '領用單已提交',
        icon: 'check_circle',
      });
    }

    return {
      requisitionInfo,
      departmentOptions,
      columns,
      requisitionItems,
      addProductDialog,
      newProduct,
      newProductQuantity,
      productOptions,
      totalQuantity,
      openAddProductDialog,
      onProductSelected,
      addProduct,
      removeItem,
      scanBarcode,
      submitRequisition,
    };
  },
});
</script>

<style lang="scss">
@import '@/css/stock-requisition.scss';
</style>
