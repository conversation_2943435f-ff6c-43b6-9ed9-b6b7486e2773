<template>
  <q-page>
    <q-card flat square class="bg-cream">
      <!-- Payroll Period -->
      <q-card-section v-if="!selectedPeriod.uuid">
        <q-table
          :rows="periods"
          :columns="periodColumns"
          v-model:pagination="periodPagination"
          hide-pagination
          table-header-class="bg-grey-3"
          :loading="isLoading"
        >
          <template v-slot:top>
            <q-toolbar>
              <q-toolbar-title class="text-h6 q-ml-sm">
                {{ t('payroll.label') }}
              </q-toolbar-title>
            </q-toolbar>

            <q-toolbar>
              <div class="row">
                <q-btn
                  @click="showPeriodCreateDialog"
                  color="positive"
                  icon="add"
                  size="sm"
                  class="q-pa-sm"
                />
              </div>
            </q-toolbar>
          </template>

          <template v-slot:body="props">
            <q-tr clickable @click="showPayrollList(props.row)" :props="props">
              <q-td key="payroll_period" :props="props">
                {{ formatDate(props.row.start_date, 'YYYY-MM-DD') }} ~
                {{ formatDate(props.row.end_date, 'YYYY-MM-DD') }}
              </q-td>
              <q-td key="created_at" :props="props">
                {{ formatDate(props.row.created_at, 'YYYY-MM-DD HH:mm') }}
              </q-td>
              <q-td key="actions" :props="props">
                <q-btn
                  type="button"
                  icon="visibility"
                  color="positive"
                  size="sm"
                  class="q-mr-sm q-pa-sm"
                />

                <q-btn
                  type="button"
                  icon="edit"
                  @click.stop="showPeriodUpdateDialog(props.row)"
                  color="positive"
                  size="sm"
                  class="q-mr-sm q-pa-sm"
                />

                <q-btn
                  type="button"
                  icon="delete"
                  @click.stop="showPeriodDeleteDialog(props.row.uuid)"
                  color="negative"
                  size="sm"
                  class="q-pa-sm"
                />
              </q-td>
            </q-tr>
          </template>
        </q-table>
        <TablePagination v-model="periodPagination" @getData="listPeriods" />
      </q-card-section>
      <!-- Users Payroll -->
      <q-card-section v-else>
        <q-table
          virtual-scroll
          :rows="items"
          :columns="columns"
          v-model:pagination="pagination"
          hide-pagination
          table-header-class="bg-grey-3"
          :loading="isLoading"
        >
          <template v-slot:top>
            <q-toolbar>
              <div class="row">
                <q-btn
                  type="button"
                  icon="arrow_back"
                  @click="backPeriodList"
                  flat
                  dense
                />
                <q-toolbar-title>{{ t('payroll.label') }}</q-toolbar-title>
              </div>
            </q-toolbar>
            <!-- Payroll Period Selection -->
            <q-toolbar class="items-center q-mb-md">
              <span class="q-mr-sm">{{ t('payroll.period') }}：</span>
              {{ formatDate(selectedPeriod.start_date, 'YYYY-MM-DD') }} ~
              {{ formatDate(selectedPeriod.end_date, 'YYYY-MM-DD') }}
            </q-toolbar>

            <!-- Send -->
            <q-toolbar class="items-center">
              <q-btn
                type="button"
                @click="sendMails"
                :label="t('email.sendAll')"
                icon="mail"
                color="primary"
                no-caps
                :loading="isLoading"
              />
            </q-toolbar>
          </template>

          <template v-slot:body="props">
            <q-tr
              clickable
              @click="openPayrollDialog(props.row)"
              :props="props"
            >
              <q-td key="user_name" :props="props">
                <q-btn
                  type="button"
                  @click.stop="openCreatePayrollDialog(props.row)"
                  icon="add"
                  color="positive"
                  size="sm"
                  class="q-pa-xs q-mr-sm"
                  v-if="!props.row.uuid"
                />
                {{ props.row.user.name }}
              </q-td>
              <q-td key="salary_type" :props="props">
                <template v-if="props.row.salary_type === 1">
                  {{ t('salaryType.salary') }}
                </template>
                <template v-else-if="props.row.salary_type === 2">
                  {{ t('salaryType.hourly') }}
                </template>
                <template v-else> - </template>
              </q-td>
              <q-td key="net_salary" :props="props">
                <template v-if="props.row.uuid">
                  AU$ {{ formatNumber(props.row.net_salary, 2) }}
                </template>
                <template v-else> 0 </template>
              </q-td>
              <q-td key="created_at" :props="props">
                <template v-if="props.row.uuid">
                  {{ formatDate(props.row.created_at, 'YYYY-MM-DD HH:mm') }}
                </template>
                <template v-else> - </template>
              </q-td>
              <q-td key="mails" :props="props">
                <template v-if="props.row.uuid">
                  <div
                    class="text-bold text-negative"
                    v-if="!props.row.user.email"
                  >
                    {{ t('email.notSet') }}
                  </div>
                  <span class="text-center" v-else>
                    <q-btn
                      type="button"
                      @click.stop="sendMail(props.row.user)"
                      icon="mail"
                      color="primary"
                      size="sm"
                      :loading="isLoading"
                    />
                    <div class="q-mt-sm" v-if="props.row.emails?.length > 0">
                      <span
                        class="text-positive text-bold"
                        v-if="
                          props.row.emails[props.row.emails.length - 1]
                            .status === 'sent'
                        "
                      >
                        <q-icon name="check" size="sm" />
                      </span>
                      <span class="text-negative text-bold" v-else>
                        <q-icon name="close" size="sm" />"
                      </span>

                      {{
                        formatDate(
                          props.row.emails[props.row.emails.length - 1].sent_at,
                          'YYYY-MM-DD HH:mm'
                        )
                      }}
                    </div>
                  </span>
                </template>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <!-- Period Dialog -->
    <q-dialog v-model="periodDialog" class="card-dialog" persistent>
      <q-card class="column full-height">
        <!-- Main -->
        <q-scroll-area class="col col-11">
          <!-- Header -->
          <q-card-section>
            <div class="row">
              <span class="text-h6">
                {{ t('payroll.period') }}
              </span>
              <q-space />
              <q-btn
                type="button"
                icon="close"
                @click="closePeriodDialog"
                dense
                flat
              />
            </div>
          </q-card-section>

          <q-separator />

          <q-card-section>
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.period') }}
              </div>
              <div class="col-12 col-md-9">
                <DateRangePicker v-model="periodDateRange" />
              </div>
            </div>
          </q-card-section>
        </q-scroll-area>

        <!-- Actions -->
        <q-card-actions class="col col-1 bg-grey-2" align="between">
          <q-btn
            type="button"
            :label="t('close')"
            color="negative"
            @click="closePeriodDialog"
            :loading="isLoading"
          />
          <q-btn
            type="button"
            :label="t('submit')"
            color="positive"
            @click="submitPeriod"
            :loading="isLoading"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 薪資單 -->
    <q-dialog v-model="payrollDialog" class="card-dialog" persistent>
      <q-card class="column full-height">
        <!-- Main -->
        <q-scroll-area class="col col-11">
          <!-- Header -->
          <q-card-section>
            <div class="row">
              <span class="text-h6">
                {{ t('payroll.label') }}
                －
                {{ selectedPayroll?.user.name }}
              </span>
              <q-space />
              <q-btn
                type="button"
                icon="close"
                @click="payrollDialog = false"
                dense
                flat
              />
            </div>
          </q-card-section>

          <q-separator />
          <!-- Body -->
          <q-card-section v-if="selectedPayroll">
            <!-- 薪資單時間 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.period') }}
              </div>
              <div class="col-12 col-md-9">
                <div class="row">
                  {{
                    formatDate(selectedPayroll.period.start_date, 'YYYY-MM-DD')
                  }}
                  ~
                  {{
                    formatDate(selectedPayroll.period.end_date, 'YYYY-MM-DD')
                  }}
                </div>
              </div>
            </div>
            <!-- 薪資類別 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('salaryType.label') }}
              </div>
              <div class="col-12 col-md-9">
                <q-select
                  v-model="selectedPayroll.salary_type"
                  :options="salaryTypeOptions"
                  option-label="name"
                  option-value="value"
                  emit-value
                  map-options
                  dense
                />
              </div>
            </div>
            <!-- 薪資 -->
            <!-- 月薪/固定薪水 -->
            <div
              class="row q-mb-sm items-center"
              v-if="selectedPayroll.salary_type === 1"
            >
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.basicSalary') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.basic_salary"
                  type="number"
                  dense
                />
              </div>
            </div>
            <!-- 時薪 -->
            <div
              class="row q-mb-sm items-center"
              v-if="selectedPayroll.salary_type === 2"
            >
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.hourlyRate') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.hourly_salary"
                  type="number"
                  dense
                />
              </div>
            </div>
            <!-- 工作天數 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.workDays') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.work_days"
                  type="number"
                  :placeholder="workStats?.workDays"
                  dense
                />
              </div>
            </div>
            <!-- 工作時數 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.workHours') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.work_hours"
                  type="number"
                  :placeholder="workStats?.totalWorkHours"
                  dense
                />
              </div>
            </div>

            <!-- separator 收入 -->
            <div class="row q-my-md">
              <div class="col">
                <div class="text-h6 text-bold">{{ t('payroll.bonus') }}</div>
                <q-separator color="grey" size="2px" />
              </div>
            </div>

            <!-- 收入項目 -->
            <template v-for="item of salaryItems" :key="item.id">
              <div
                class="row q-mb-sm items-center"
                v-if="item.type === 1 || item.type === 3"
              >
                <div class="col-12 col-md-3 text-md-center">
                  {{ t(`payroll.${item.name}`) }}
                </div>
                <div class="col-12 col-md-9">
                  <q-input
                    v-model.number="payrollDetails(item).amount"
                    type="number"
                    dense
                  />
                </div>
              </div>
            </template>

            <!-- 總收入 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.grossSalary') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.gross_salary"
                  type="number"
                  dense
                  filled
                  readonly
                />
              </div>
            </div>

            <!-- separator 扣款 -->
            <div class="row q-my-md">
              <div class="col">
                <div class="text-h6 text-bold">
                  {{ t('payroll.deduction') }}
                </div>
                <q-separator color="grey" size="2px" />
              </div>
            </div>

            <!-- 扣款項目 -->
            <template v-for="item of salaryItems" :key="item.id">
              <div class="row q-mb-sm items-center" v-if="item.type === 2">
                <div class="col-12 col-md-3 text-md-center">
                  {{ t(`payroll.${item.name}`) }}
                </div>
                <div class="col-12 col-md-9">
                  <q-input
                    v-model.number="payrollDetails(item).amount"
                    type="number"
                    dense
                  />
                </div>
              </div>
            </template>

            <!-- 總扣款 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.totalDeductions') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.total_deductions"
                  type="number"
                  dense
                  filled
                  readonly
                />
              </div>
            </div>

            <!-- separator 淨收入 -->
            <div class="row q-my-md">
              <div class="col">
                <div class="text-h6 text-bold">
                  {{ t('total') }}
                </div>
                <q-separator color="grey" size="2px" />
              </div>
            </div>

            <!-- 淨收入 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.netSalary') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model.number="selectedPayroll.net_salary"
                  type="number"
                  dense
                  filled
                  readonly
                />
              </div>
            </div>
            <!-- 發薪日期 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.payDate') }}
              </div>
              <div class="col-12 col-md-9">
                <q-input
                  v-model="selectedPayroll.pay_date"
                  type="text"
                  mask="date"
                  dense
                >
                  <template v-slot:prepend>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy
                        cover
                        transition-show="scale"
                        transition-hide="scale"
                      >
                        <q-date v-model="selectedPayroll.pay_date" today-btn>
                          <div class="row items-center justify-end q-gutter-sm">
                            <q-btn
                              :label="t('clear')"
                              color="negative"
                              @click="selectedPayroll.pay_date = ''"
                              flat
                              v-close-popup
                            />
                            <q-btn
                              :label="t('close')"
                              color="primary"
                              flat
                              v-close-popup
                            />
                          </div>
                        </q-date>
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
            </div>
            <!-- 發薪狀態 -->
            <div class="row q-mb-sm items-center">
              <div class="col-12 col-md-3 text-md-center">
                {{ t('payroll.payStatus') }}
              </div>
              <div class="col-12 col-md-9">
                <q-select
                  v-model="selectedPayroll.status"
                  :options="payStatusOptions"
                  option-label="name"
                  option-value="value"
                  emit-value
                  map-options
                  dense
                />
              </div>
            </div>
          </q-card-section>
        </q-scroll-area>

        <!-- Actions -->
        <q-card-actions class="col col-1 bg-grey-2" align="between">
          <q-btn
            type="button"
            :label="t('close')"
            color="negative"
            @click="payrollDialog = false"
            :loading="isLoading"
          />
          <q-btn
            type="button"
            :label="t('submit')"
            color="positive"
            @click="submitPayroll"
            :loading="isLoading"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Notify } from 'quasar';
import {
  PayrollApi,
  PayrollPeriod,
  Payroll,
  SalaryItemApi,
  SalaryItem,
  PayrollDetail,
} from '@/api/payroll';
import { UserApi, User } from '@/api/user';
import { AttendanceApi } from '@/api/attendance';
import { Pagination, calculateWorkStatistics, WorkStatistics } from '@/types';
import { formatNumber, formatDate, useDialog } from '@/utils';
import { ApiResponse } from '@/api/modules/response';
import DateRangePicker from '@/components/DateRangePicker.vue';

const { t } = useI18n();
const dialog = useDialog();
const isLoading = ref(false);

const selectedPeriod = ref<PayrollPeriod>({
  uuid: '',
  start_date: '',
  end_date: '',
  created_at: '',
});

const periods = ref<PayrollPeriod[]>([]);
const periodPagination = ref<Pagination>({
  sortBy: 'created_at',
  descending: true,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});
const periodColumns = computed(() => [
  {
    name: 'payroll_period',
    label: t('payroll.period'),
    align: 'left' as const,
    field: 'payroll_period',
  },
  {
    name: 'created_at',
    label: t('createdAt'),
    align: 'left' as const,
    field: 'created_at',
  },
  {
    name: 'actions',
    label: t('actions'),
    align: 'center' as const,
    field: 'actions',
  },
]);

const listPeriods = async () => {
  const response = await PayrollApi.listPeriods({
    pagination: periodPagination.value,
  });

  periods.value = response.result.data;
  periodPagination.value = response.result.pagination;
};

const periodDialog = ref(false);
const period = ref<PayrollPeriod>({
  uuid: '',
  start_date: '',
  end_date: '',
  created_at: '',
});
const periodDateRange = computed({
  get: () => ({
    from: period.value.start_date,
    to: period.value.end_date,
  }),
  set: (value) => {
    period.value.start_date = value.from;
    period.value.end_date = value.to;
  },
});

const showPeriodCreateDialog = () => {
  periodDialog.value = true;
  period.value = {
    uuid: '',
    start_date: '',
    end_date: '',
    created_at: '',
  };
};

const showPeriodUpdateDialog = (data: PayrollPeriod) => {
  periodDialog.value = true;
  period.value = {
    ...data,
  };
};

const showPeriodDeleteDialog = (periodUUID: string) => {
  dialog.showMessage({
    title: '',
    message: t('confirmDelete'),
    timeout: 0,
    ok: async () => {
      try {
        isLoading.value = true;

        if (!periodUUID) return;

        await PayrollApi.deletePeriod(periodUUID);

        Notify.create({
          type: 'positive',
          message: t('success'),
          position: 'top',
        });
      } finally {
        isLoading.value = false;
        listPeriods();
      }
    },
  });
};

const closePeriodDialog = () => {
  periodDialog.value = false;
};

const submitPeriod = async () => {
  try {
    isLoading.value = true;

    if (period.value.uuid) {
      await PayrollApi.updatePeriod(period.value);
    } else {
      await PayrollApi.createPeriod(period.value);
    }
  } finally {
    isLoading.value = false;
    listPeriods();
    closePeriodDialog();
  }
};

const items = ref<Payroll[]>([]);
const columns = computed(() => [
  {
    name: 'user_name',
    label: t('name'),
    align: 'left' as const,
    field: (row: Payroll) => row.user.name,
  },
  {
    name: 'salary_type',
    label: t('salaryType.label'),
    align: 'left' as const,
    field: 'salary_type',
  },
  {
    name: 'net_salary',
    label: t('salary'),
    align: 'left' as const,
    field: 'net_salary',
  },
  {
    name: 'created_at',
    label: t('createdAt'),
    align: 'left' as const,
    field: 'created_at',
  },
  {
    name: 'mails',
    label: t('email.label'),
    align: 'center' as const,
    field: 'mails',
  },
]);
const pagination = ref<Pagination>({
  sortBy: '',
  descending: true,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

const showPayrollList = (period: PayrollPeriod) => {
  selectedPeriod.value = period;
};

const backPeriodList = () => {
  selectedPeriod.value = {
    uuid: '',
    start_date: '',
    end_date: '',
    created_at: '',
  };
};

const users = ref<User[]>([]);
const salaryItems = ref<SalaryItem[]>([]);

const sendMails = async () => {
  try {
    isLoading.value = true;

    await PayrollApi.sendMail(
      selectedPeriod.value.uuid,
      items.value.map((item) => item.user.uuid)
    );

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } finally {
    isLoading.value = false;
    listPayroll();
  }
};

const sendMail = async (user: User) => {
  try {
    isLoading.value = true;

    await PayrollApi.sendMail(selectedPeriod.value.uuid, [user.uuid]);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } finally {
    isLoading.value = false;
    listPayroll();
  }
};

onMounted(async () => {
  try {
    isLoading.value = true;

    const [responseUsers, responseSalaryItems, responsePeriods] =
      await Promise.all([
        UserApi.fetch(),
        SalaryItemApi.listSalaryItems(),
        PayrollApi.listPeriods({
          pagination: periodPagination.value,
        }),
      ]);

    // users
    users.value = responseUsers.result;

    // salary item
    salaryItems.value = responseSalaryItems.result;

    // periods
    periods.value = responsePeriods.result.data;
    periodPagination.value = responsePeriods.result.pagination;
  } finally {
    isLoading.value = false;
  }
});

const handlePayrollData = (
  responsePayroll: ApiResponse<{
    data: Payroll[];
    pagination: Pagination;
  }>
) => {
  items.value = [];
  for (const user of users.value) {
    const payroll = responsePayroll.result.data.find(
      (item: Payroll) => item.user.uuid === user.uuid
    );

    if (!payroll) {
      const initPayrollDetails: PayrollDetail[] = [];

      for (const item of salaryItems.value) {
        initPayrollDetails.push({
          salary_item: item,
          amount: 0,
        });
      }

      items.value.push({
        period: selectedPeriod.value,
        user: {
          uuid: user.uuid,
          name: user.name,
          email: user.email,
        },
        net_salary: 0,
        payroll_details: [...initPayrollDetails],
        emails: [],
      });
    }
  }

  items.value = [...items.value, ...responsePayroll.result.data];
  pagination.value = responsePayroll.result.pagination;
};

const listPayroll = async () => {
  try {
    isLoading.value = true;

    const response = await PayrollApi.listPayroll({
      filter: {
        period_uuid: selectedPeriod.value.uuid,
      },
      pagination: pagination.value,
    });

    handlePayrollData(response);
  } finally {
    isLoading.value = false;
  }
};

const payrollDialog = ref(false);
const selectedPayroll = ref<Payroll>();

const salaryTypeOptions = computed(() => [
  {
    value: 1,
    name: t('salaryType.salary'),
  },
  {
    value: 2,
    name: t('salaryType.hourly'),
  },
]);

const payStatusOptions = computed(() => [
  {
    value: 1,
    name: t('payroll.unpaid'),
  },
  {
    value: 2,
    name: t('payroll.paid'),
  },
]);

const openCreatePayrollDialog = (payroll: Payroll) => {
  selectedPayroll.value = {
    ...payroll,
    period: selectedPeriod.value,
    user_uuid: payroll.user.uuid,
    salary_type: 1,
    status: 1,
  };
  getWorkStatistics();
  payrollDialog.value = true;
};

const openPayrollDialog = (payroll: Payroll) => {
  if (!payroll.uuid) return;

  selectedPayroll.value = {
    ...payroll,
    user_uuid: payroll.user.uuid,
  };
  getWorkStatistics();
  payrollDialog.value = true;
};

const workStats = ref<WorkStatistics>();
const getWorkStatistics = async () => {
  try {
    initWorkStats();

    const response = await AttendanceApi.listClockPairs({
      filter: {
        start_date: selectedPeriod.value.start_date,
        end_date: selectedPeriod.value.end_date,
        user_uuid: selectedPayroll.value?.user_uuid,
      },
    });
    const clockPairs = response.result.data;

    workStats.value = calculateWorkStatistics(clockPairs);
  } catch (err) {
    initWorkStats();
  }
};

const initWorkStats = () => {
  workStats.value = {
    workDays: 0,
    totalWorkHours: 0,
    averageWorkHours: 0,
    validPairs: [],
    invalidPairs: [],
  };
};

const payrollDetails = (salaryItem: SalaryItem) => {
  const payrollDetail = selectedPayroll.value?.payroll_details.find(
    (item) => item.salary_item.id === salaryItem.id
  );

  if (payrollDetail) {
    return payrollDetail;
  }

  const initPayrollDetail: PayrollDetail = {
    salary_item: salaryItem,
    amount: 0,
  };

  selectedPayroll.value?.payroll_details.push(initPayrollDetail);

  return initPayrollDetail;
};

// 總收入
const calculateGrossSalary = () => {
  if (!selectedPayroll.value) return;

  let grossSalary = 0;

  if (selectedPayroll.value.salary_type === 1) {
    grossSalary = selectedPayroll.value.basic_salary || 0;
  } else if (selectedPayroll.value.salary_type === 2) {
    grossSalary = selectedPayroll.value.hourly_salary || 0;
    grossSalary *= selectedPayroll.value.work_hours || 0;
  }

  for (const detail of selectedPayroll.value.payroll_details) {
    if (detail.salary_item.type === 1 || detail.salary_item.type === 3) {
      grossSalary += detail.amount || 0;
    }
  }

  selectedPayroll.value.gross_salary = Math.round(grossSalary * 100) / 100;
};

// 總扣款
const calculateTotalDeductions = () => {
  if (!selectedPayroll.value) return;

  let totalDeductions = 0;

  for (const detail of selectedPayroll.value.payroll_details) {
    if (detail.salary_item.type === 2) {
      totalDeductions += detail.amount || 0;
    }
  }

  selectedPayroll.value.total_deductions =
    Math.round(totalDeductions * 100) / 100;
};

// 淨收入 (總收入-總扣款)
const calculateNetSalary = () => {
  if (!selectedPayroll.value) return;

  const netSalary =
    (selectedPayroll.value.gross_salary || 0) -
    (selectedPayroll.value.total_deductions || 0);

  selectedPayroll.value.net_salary = Math.round(netSalary * 100) / 100;
};

const calculatePayroll = () => {
  calculateGrossSalary();
  calculateTotalDeductions();
  calculateNetSalary();
};

const submitPayroll = async () => {
  if (!selectedPayroll.value) return;

  try {
    isLoading.value = true;

    if (selectedPayroll.value.salary_type === 1) {
      selectedPayroll.value.hourly_salary = 0;
    } else if (selectedPayroll.value.salary_type === 2) {
      selectedPayroll.value.basic_salary = 0;
    }

    if (
      !selectedPayroll.value.work_days &&
      selectedPayroll.value.work_days !== 0
    ) {
      selectedPayroll.value.work_days = workStats.value?.workDays || 0;
    }

    if (
      !selectedPayroll.value.work_hours &&
      selectedPayroll.value.work_hours !== 0
    ) {
      selectedPayroll.value.work_hours = workStats.value?.totalWorkHours || 0;
    }

    if (!selectedPayroll.value.uuid) {
      await PayrollApi.createPayroll({
        ...selectedPayroll.value,
        period_uuid: selectedPeriod.value.uuid,
      });
    } else {
      await PayrollApi.updatePayroll(
        selectedPayroll.value.uuid,
        selectedPayroll.value
      );
    }

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });

    payrollDialog.value = false;
  } finally {
    listPayroll();
  }
};

watch(
  () => selectedPeriod.value,
  () => {
    listPayroll();
  }
);

watch(
  () => selectedPayroll.value,
  () => {
    calculatePayroll();
  },
  { deep: true }
);
</script>
