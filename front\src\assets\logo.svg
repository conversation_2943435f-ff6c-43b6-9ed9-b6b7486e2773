<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="271.925" height="50.053" viewBox="0 0 271.925 50.053">
  <defs>
    <linearGradient id="linear-gradient" x1="-0.307" y1="0.919" x2="1.334" y2="-0.107" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0e1a4a"/>
      <stop offset="0.5" stop-color="#137abf"/>
      <stop offset="0.782" stop-color="#1dbaba"/>
      <stop offset="1" stop-color="#accd00"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-1.978" y1="1.365" x2="2.026" y2="-0.002" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-3" x1="-2.897" y1="1.384" x2="1.089" y2="0.364" xlink:href="#linear-gradient"/>
  </defs>
  <g id="logo" transform="translate(-179.075 -448)">
    <g id="text" transform="translate(301 448.018)">
      <g id="Eng" transform="translate(0 36.988)">
        <g id="Group_3" data-name="Group 3" transform="translate(0 0)">
          <path id="Path_5" data-name="Path 5" d="M188.578,118.584a8.482,8.482,0,0,1-3.43.6c-3.66,0-6.416-2.251-6.416-6.392,0-3.955,2.756-6.636,6.782-6.636a7.173,7.173,0,0,1,3.086.56l-.408,1.333a6.235,6.235,0,0,0-2.62-.526c-3.044,0-5.068,1.895-5.068,5.211,0,3.093,1.831,5.079,4.993,5.079a6.844,6.844,0,0,0,2.734-.524Z" transform="translate(-178.732 -106.157)" fill="#8e8e8e"/>
          <path id="Path_6" data-name="Path 6" d="M189.278,106.305v5.286h6.287v-5.286h1.692V118.94h-1.692v-5.924h-6.287v5.924H187.6V106.305Z" transform="translate(-175.269 -106.099)" fill="#8e8e8e"/>
          <path id="Path_7" data-name="Path 7" d="M203.643,113.016h-5.049v4.554h5.625v1.37h-7.3V106.305h7.014v1.37h-5.337v3.992h5.049Z" transform="translate(-171.635 -106.099)" fill="#8e8e8e"/>
          <path id="Path_8" data-name="Path 8" d="M204.014,118.94V106.305h1.831l4.162,6.393a35.379,35.379,0,0,1,2.332,4.106l.039-.02c-.154-1.687-.193-3.224-.193-5.194v-5.286h1.58V118.94h-1.694l-4.124-6.412a39.155,39.155,0,0,1-2.429-4.218l-.059.018c.1,1.6.137,3.112.137,5.214v5.4Z" transform="translate(-168.864 -106.099)" fill="#8e8e8e"/>
          <path id="Path_9" data-name="Path 9" d="M223.515,118.413a12.362,12.362,0,0,1-3.989.693,6.781,6.781,0,0,1-4.854-1.667,6.4,6.4,0,0,1-1.814-4.706c.022-3.767,2.68-6.522,7.033-6.522a8.214,8.214,0,0,1,3.24.581l-.405,1.331a6.893,6.893,0,0,0-2.871-.543c-3.162,0-5.222,1.911-5.222,5.08,0,3.206,1.985,5.1,5.01,5.1a5.606,5.606,0,0,0,2.234-.338v-3.767h-2.639v-1.311h4.277Z" transform="translate(-165.413 -106.136)" fill="#8e8e8e"/>
          <path id="Path_10" data-name="Path 10" d="M230.03,118.94l-1.658-2.8c-.671-1.067-1.1-1.76-1.5-2.491h-.039c-.366.731-.732,1.406-1.406,2.511l-1.56,2.776h-1.929l3.972-6.393-3.818-6.243h1.948l1.714,2.961c.483.826.847,1.462,1.194,2.139h.061c.364-.751.693-1.332,1.174-2.139l1.772-2.961h1.929l-3.953,6.149,4.048,6.487Z" transform="translate(-161.869 -106.099)" fill="#8e8e8e"/>
          <path id="Path_11" data-name="Path 11" d="M232.513,106.305V118.94h-1.677V106.305Z" transform="translate(-158.395 -106.099)" fill="#8e8e8e"/>
          <path id="Path_12" data-name="Path 12" d="M234.426,118.94V106.305h1.831l4.162,6.393a35.764,35.764,0,0,1,2.334,4.106l.037-.02c-.154-1.687-.193-3.224-.193-5.194v-5.286h1.582V118.94h-1.7l-4.123-6.412a39.155,39.155,0,0,1-2.429-4.218l-.061.018c.1,1.6.139,3.112.139,5.214v5.4Z" transform="translate(-156.995 -106.099)" fill="#8e8e8e"/>
          <path id="Path_13" data-name="Path 13" d="M253.927,118.413a12.344,12.344,0,0,1-3.987.693,6.791,6.791,0,0,1-4.856-1.667,6.4,6.4,0,0,1-1.814-4.706c.022-3.767,2.678-6.522,7.034-6.522a8.2,8.2,0,0,1,3.24.581l-.405,1.331a6.893,6.893,0,0,0-2.871-.543c-3.162,0-5.222,1.911-5.222,5.08,0,3.206,1.985,5.1,5.01,5.1a5.629,5.629,0,0,0,2.236-.338v-3.767h-2.642v-1.311h4.277Z" transform="translate(-153.541 -106.136)" fill="#8e8e8e"/>
          <path id="Path_14" data-name="Path 14" d="M256.225,117.009a6.064,6.064,0,0,0,3.008.825c1.714,0,2.717-.881,2.717-2.156,0-1.18-.7-1.857-2.449-2.511-2.119-.731-3.428-1.8-3.428-3.58,0-1.97,1.675-3.43,4.2-3.43a6.051,6.051,0,0,1,2.871.618l-.461,1.332a5.288,5.288,0,0,0-2.468-.6c-1.77,0-2.449,1.03-2.449,1.893,0,1.18.793,1.763,2.585,2.436,2.2.826,3.313,1.857,3.313,3.712,0,1.949-1.482,3.636-4.548,3.636a6.9,6.9,0,0,1-3.315-.8Z" transform="translate(-148.651 -106.157)" fill="#8e8e8e"/>
          <path id="Path_15" data-name="Path 15" d="M274.7,112.547c0,4.351-2.72,6.656-6.035,6.656-3.433,0-5.84-2.588-5.84-6.411,0-4.011,2.563-6.636,6.032-6.636C272.408,106.157,274.7,108.8,274.7,112.547Zm-10.081.208c0,2.7,1.5,5.117,4.143,5.117,2.659,0,4.16-2.382,4.16-5.248,0-2.512-1.345-5.138-4.143-5.138C266.009,107.486,264.622,109.981,264.622,112.755Z" transform="translate(-145.91 -106.157)" fill="#8e8e8e"/>
          <path id="Path_16" data-name="Path 16" d="M273.21,106.305h7v1.37H274.89v4.2H279.8v1.348H274.89v5.719h-1.68Z" transform="translate(-141.854 -106.099)" fill="#8e8e8e"/>
          <path id="Path_17" data-name="Path 17" d="M283.121,107.693h-3.95v-1.388h9.609v1.388h-3.965V118.94h-1.694Z" transform="translate(-139.53 -106.099)" fill="#8e8e8e"/>
        </g>
      </g>
      <g id="chinese" transform="translate(1.39 0)">
        <g id="Group_1" data-name="Group 1" transform="translate(0 0)">
          <path id="Path_1" data-name="Path 1" d="M179.981,99.381a18.242,18.242,0,0,0-.784-1.65,47.137,47.137,0,0,0,4.177-10.852H179.8V85.558h3.628V79.832h1.448v5.726h2.847v1.321H184.88v4.15l.664-.475q1.476,1.549,3.113,3.5l-1.179.89q-1.256-1.609-2.6-3.216V107.2h-1.448V90.268A42.413,42.413,0,0,1,179.981,99.381Zm7.378,5.47h11.274q1.567-2.225,2.966-4.593l1.389.674q-.725,1.077-2.273,3.317c-.178.267-.315.468-.415.6h7.129V106.2H187.359Zm1.816-19.608,1.091-.833q1.135,1.165,2.346,2.468a18.4,18.4,0,0,0,2.642-4.132H189V81.425h7.764V82.89a20.9,20.9,0,0,1-7.851,9,10.572,10.572,0,0,0-1.033-1.15,22.162,22.162,0,0,0,3.777-2.855Q190.089,86.162,189.175,85.243Zm1.091,7.722H204.8v6.36H190.266Zm1.477,1.334V98h11.57V94.3Zm.029,6.733,1.15-.806q1.622,1.8,2.776,3.274l-1.243.819Q193.086,102.511,191.773,101.033Zm1.548-12.258h8.635v1.291H193.32Zm13.533,3.115q-7.467-4.378-9.031-11.384l1.328-.315a20.51,20.51,0,0,0,1.182,3.143q2.007-1.379,3.806-2.8l.93,1.089q-2.406,1.723-4.162,2.814a15.272,15.272,0,0,0,1.714,2.384,44.9,44.9,0,0,0,3.66-2.728l.945,1.093-.444.3q-1.9,1.337-3.276,2.252a18.146,18.146,0,0,0,4.294,2.915A9.52,9.52,0,0,0,206.853,91.89Z" transform="translate(-179.197 -79.444)" fill="#8e8e8e"/>
          <path id="Path_2" data-name="Path 2" d="M210.352,82.015a20.368,20.368,0,0,0,4.6-1.378l.752,1.2a23.864,23.864,0,0,1-3.865,1.178v3.99h3.467v1.263h-3.467v4.349h3.467v1.262h-3.467v4.451h4.736V81.111h11.187V98.336h4.707V93.885h-3.35V92.623h3.35V88.274h-3.35V87.01h3.35V82.633h-3.586V81.34h5.078v17h2.109v1.292H208.243V98.336h2.109Zm7.556,19.162,1.033,1.291q-4.321,2.325-9.431,4.508a8.541,8.541,0,0,0-1-1.351A80.36,80.36,0,0,0,217.908,101.177ZM218,82.4V98.336h8.352V82.4Zm1.46,2.341h5.4v1.2h-5.4Zm.264,3.558h4.9v7.379h-4.9Zm1.3,1.206v4.967h2.3V89.508Zm13.872,17.383q-4.823-2.3-9.695-4.292l.9-1.307q6.687,2.831,9.683,4.178Z" transform="translate(-167.859 -79.13)" fill="#8e8e8e"/>
          <path id="Path_3" data-name="Path 3" d="M247.212,103.609l.757,1.263q-5.713,1.595-10.361,2.613a6.5,6.5,0,0,0-.754-1.378Q241.712,105.134,247.212,103.609Zm-9.973-14.081q2.373-1.335,6.506-4.147a10.909,10.909,0,0,0,.813,1.233l-3.086,2.053q-2.018,1.335-3.288,2.181Zm6.2-5.239q-2.593-1.765-4.736-3l.93-1.09q1.652.947,4.751,2.884Zm-2.922,7.034h20.825v11.4H240.512Zm1.536,1.193v2.211h17.739V92.515Zm0,3.386v2.212h17.739V95.9Zm0,3.389v2.224h17.739V99.29ZM247.036,86.7c-.234-.172-.669-.465-1.3-.876a16.869,16.869,0,0,0,4.531-6.273l1.443.416c-.315.7-.632,1.355-.957,1.968h13.015V83.17l-1.931,3.145-1.416-.59,1.636-2.555h-6.228a4.259,4.259,0,0,1-.146.762q1.783,4.206,8.823,5.3a7.228,7.228,0,0,0-.825,1.376q-6.848-1.436-8.454-5.081-1.2,3.6-9.255,4.936l-.813-1.32q9.448-1.306,9.153-5.972H250A19.163,19.163,0,0,1,247.036,86.7Zm6.746,18.17.693-1.263q5.343,1.135,10.034,2.341l-.574,1.393Q260.493,106.439,253.781,104.872Z" transform="translate(-156.694 -79.553)" fill="#8e8e8e"/>
          <path id="Path_4" data-name="Path 4" d="M275.31,85.175h-9.783V83.784h9.783Zm-8.884,21.717V97.132h8.252v9.618H273.2v-1.321h-5.3v1.464Zm8.042-17.311h-7.979V88.205h7.979Zm.032,4.377h-8.01V92.583h8.01Zm-6.6,4.521v5.585h5.3V98.481Zm.769-18.071,1.27-.7q1.014,1.419,1.99,3.027l-1.326.7A29.833,29.833,0,0,0,268.671,80.41Zm7.732,2.355v-1.45h13.034V87.76q-.062,12.488,1.208,15.788.37,1.2.989,1.234t.781-1.234a43.658,43.658,0,0,0,.366-4.479,9.79,9.79,0,0,0,1.328.474q-.143,2.629-.486,4.91-.355,2.124-1.931,2.239-1.516-.086-2.349-2.065-1.564-3.345-1.487-16.866v-5H282v9.6h4.233v1.422H282v13.519h-1.58V93.789h-4.194V92.367h4.194v-9.6Z" transform="translate(-145.501 -79.493)" fill="#8e8e8e"/>
        </g>
      </g>
    </g>
    <g id="logo-2" data-name="logo" transform="translate(179.075 448)" style="mix-blend-mode: normal;isolation: isolate">
      <g id="Group_5" data-name="Group 5" transform="translate(0 0)">
        <path id="Path_18" data-name="Path 18" d="M104.632,79.541,86.087,110.808l11.129,18.766,29.691,0,11.139-18.763h-29.69L115.779,98.3h29.69l-3.71-6.254h-29.7l-11.123,18.761,3.71,6.252,22.265.007-3.716,6.247H100.931L93.51,110.808l14.841-25,51.956-.01,3.708,6.257,3.712-6.252-3.712-6.25Z" transform="translate(-86.087 -79.541)" fill="#606060"/>
        <path id="Path_19" data-name="Path 19" d="M135.628,88.54l3.71,6.255-18.55,31.262h22.268L154.181,107.3,143.052,88.542ZM146.759,107.3l-7.415,12.5h-7.424l11.133-18.76Z" transform="translate(-72.543 -76.028)" fill="#606060"/>
        <path id="Path_20" data-name="Path 20" d="M178.2,79.547l-22.268,0L144.809,98.3l14.844,25.011h-7.422l-3.708-6.252-3.713,6.252,3.713,6.25,22.265,0L159.653,110.81ZM155.942,104.56,152.233,98.3l7.415-12.5h7.424Z" transform="translate(-63.169 -79.539)" fill="#606060"/>
      </g>
      <g id="Group_8" data-name="Group 8" transform="translate(0 0)" style="mix-blend-mode: screen;isolation: isolate">
        <g id="Group_6" data-name="Group 6" transform="translate(81.652 0.005)">
          <path id="Path_21" data-name="Path 21" d="M151.2,93.039l-3.711,6.254h7.422Z" transform="translate(-143.776 -74.277)" fill="#767676" fill-rule="evenodd"/>
          <path id="Path_22" data-name="Path 22" d="M148.53,88.539l-3.711,6.252h7.422Z" transform="translate(-144.817 -76.034)" fill="#494949" fill-rule="evenodd"/>
          <path id="Path_23" data-name="Path 23" d="M154.911,97.536h-7.424l3.711,6.251Z" transform="translate(-143.775 -72.522)" fill="#8c8c8c" fill-rule="evenodd"/>
          <path id="Path_24" data-name="Path 24" d="M148.529,99.289l3.713-6.252h-7.424Z" transform="translate(-144.816 -74.278)" fill="#5f5f5f" fill-rule="evenodd"/>
          <path id="Path_25" data-name="Path 25" d="M148.528,106.529l3.711,6.252h-7.422Z" transform="translate(-144.817 -69.011)" fill="#232323" fill-rule="evenodd"/>
          <path id="Path_26" data-name="Path 26" d="M148.53,117.277l-3.713-6.251h7.422Z" transform="translate(-144.817 -67.257)" fill="#363636" fill-rule="evenodd"/>
          <path id="Path_27" data-name="Path 27" d="M153.868,108.284l-3.711-6.254h7.422Z" transform="translate(-142.734 -70.767)" fill="#b7b7b7" fill-rule="evenodd"/>
          <path id="Path_28" data-name="Path 28" d="M160.247,108.285h-7.422l3.708-6.252Z" transform="translate(-141.692 -70.767)" fill="#d4d4d4" fill-rule="evenodd"/>
          <path id="Path_29" data-name="Path 29" d="M157.578,103.786h-7.422l3.708-6.251Z" transform="translate(-142.734 -72.523)" fill="#a1a1a1" fill-rule="evenodd"/>
          <path id="Path_30" data-name="Path 30" d="M156.533,112.782l-3.708-6.254h7.422Z" transform="translate(-141.692 -69.011)" fill="#adadad" fill-rule="evenodd"/>
          <path id="Path_31" data-name="Path 31" d="M162.915,112.783h-7.422l3.708-6.254Z" transform="translate(-140.651 -69.011)" fill="#888" fill-rule="evenodd"/>
          <path id="Path_32" data-name="Path 32" d="M159.207,117.279l-3.713-6.254h7.424Z" transform="translate(-140.649 -67.257)" fill="#5d5d5d" fill-rule="evenodd"/>
          <path id="Path_33" data-name="Path 33" d="M165.584,117.279h-7.422l3.711-6.252Z" transform="translate(-139.609 -67.256)" fill="#676767" fill-rule="evenodd"/>
          <path id="Path_34" data-name="Path 34" d="M151.2,111.027l3.711,6.254h-7.422Z" transform="translate(-143.777 -67.256)" fill="#3c3c3c" fill-rule="evenodd"/>
          <path id="Path_35" data-name="Path 35" d="M156.533,111.026l3.713,6.254h-7.422Z" transform="translate(-141.692 -67.257)" fill="#4f4f4f" fill-rule="evenodd"/>
          <path id="Path_36" data-name="Path 36" d="M150.156,111.025h7.422l-3.713,6.252Z" transform="translate(-142.734 -67.257)" fill="#454545" fill-rule="evenodd"/>
          <path id="Path_37" data-name="Path 37" d="M162.914,93.038h-7.422l3.711,6.251Z" transform="translate(-140.65 -74.277)" fill="#717171" fill-rule="evenodd"/>
          <path id="Path_38" data-name="Path 38" d="M159.2,88.541l-3.711,6.254h7.422Z" transform="translate(-140.65 -76.032)" fill="#636363" fill-rule="evenodd"/>
          <path id="Path_39" data-name="Path 39" d="M160.245,97.536h-7.422l3.708,6.251Z" transform="translate(-141.693 -72.521)" fill="#767676" fill-rule="evenodd"/>
          <path id="Path_40" data-name="Path 40" d="M156.532,93.039l-3.708,6.254h7.422Z" transform="translate(-141.693 -74.277)" fill="#909090" fill-rule="evenodd"/>
          <path id="Path_41" data-name="Path 41" d="M165.583,88.541h-7.422l3.708,6.251Z" transform="translate(-139.61 -76.032)" fill="#4f4f4f" fill-rule="evenodd"/>
          <path id="Path_42" data-name="Path 42" d="M161.872,84.044,158.162,90.3h7.422Z" transform="translate(-139.608 -77.787)" fill="#3a3a3a" fill-rule="evenodd"/>
          <path id="Path_43" data-name="Path 43" d="M168.252,84.044H160.83l3.711,6.252Z" transform="translate(-138.566 -77.788)" fill="#2f2f2f" fill-rule="evenodd"/>
          <path id="Path_44" data-name="Path 44" d="M151.2,94.792l3.708-6.251h-7.422Z" transform="translate(-143.775 -76.032)" fill="#434343" fill-rule="evenodd"/>
          <path id="Path_45" data-name="Path 45" d="M153.863,90.295l3.711-6.252h-7.422Z" transform="translate(-142.736 -77.788)" fill="#454545" fill-rule="evenodd"/>
          <path id="Path_46" data-name="Path 46" d="M147.482,90.3h7.424l-3.713-6.252Z" transform="translate(-143.777 -77.788)" fill="#3d3d3d" fill-rule="evenodd"/>
          <path id="Path_47" data-name="Path 47" d="M164.541,79.546,160.83,85.8h7.422Z" transform="translate(-138.567 -79.543)" fill="#252525" fill-rule="evenodd"/>
          <path id="Path_48" data-name="Path 48" d="M170.923,79.545H163.5L167.21,85.8Z" transform="translate(-137.525 -79.544)" fill="#1c1c1c" fill-rule="evenodd"/>
          <path id="Path_49" data-name="Path 49" d="M156.534,85.8l3.711-6.254h-7.424Z" transform="translate(-141.693 -79.544)" fill="#3d3d3d" fill-rule="evenodd"/>
          <path id="Path_50" data-name="Path 50" d="M150.152,85.8h7.422l-3.708-6.254Z" transform="translate(-142.736 -79.544)" fill="#565656" fill-rule="evenodd"/>
          <path id="Path_51" data-name="Path 51" d="M161.87,85.8l3.713-6.254h-7.424Z" transform="translate(-139.611 -79.544)" fill="#2f2f2f" fill-rule="evenodd"/>
          <path id="Path_52" data-name="Path 52" d="M155.49,85.8h7.422L159.2,79.545Z" transform="translate(-140.651 -79.544)" fill="#3b3b3b" fill-rule="evenodd"/>
        </g>
        <path id="Path_53" data-name="Path 53" d="M140.521,103.785l3.708-6.252h-7.422Z" transform="translate(-66.291 -72.518)" fill="#767676" fill-rule="evenodd"/>
        <path id="Path_54" data-name="Path 54" d="M143.189,108.287l3.708-6.254h-7.422Z" transform="translate(-65.249 -70.761)" fill="#494949" fill-rule="evenodd"/>
        <path id="Path_55" data-name="Path 55" d="M136.807,99.289h7.422l-3.708-6.252Z" transform="translate(-66.291 -74.273)" fill="#8c8c8c" fill-rule="evenodd"/>
        <path id="Path_56" data-name="Path 56" d="M143.189,97.537l-3.713,6.252H146.9Z" transform="translate(-65.249 -72.516)" fill="#5f5f5f" fill-rule="evenodd"/>
        <path id="Path_57" data-name="Path 57" d="M143.189,90.3l-3.711-6.252H146.9Z" transform="translate(-65.249 -77.783)" fill="#232323" fill-rule="evenodd"/>
        <path id="Path_58" data-name="Path 58" d="M143.188,79.547,146.9,85.8h-7.424Z" transform="translate(-65.248 -79.538)" fill="#363636" fill-rule="evenodd"/>
        <path id="Path_59" data-name="Path 59" d="M137.85,88.541l3.71,6.251h-7.422Z" transform="translate(-67.332 -76.027)" fill="#b7b7b7" fill-rule="evenodd"/>
        <path id="Path_60" data-name="Path 60" d="M131.469,88.54h7.423l-3.711,6.251Z" transform="translate(-68.373 -76.028)" fill="#d4d4d4" fill-rule="evenodd"/>
        <path id="Path_61" data-name="Path 61" d="M134.138,93.038h7.422l-3.71,6.251Z" transform="translate(-67.332 -74.272)" fill="#a1a1a1" fill-rule="evenodd"/>
        <path id="Path_62" data-name="Path 62" d="M132.511,79.546l3.713,6.254H128.8Z" transform="translate(-69.415 -79.539)" fill="#5d5d5d" fill-rule="evenodd"/>
        <path id="Path_63" data-name="Path 63" d="M126.132,79.545h7.423L129.843,85.8Z" transform="translate(-70.457 -79.538)" fill="#676767" fill-rule="evenodd"/>
        <path id="Path_64" data-name="Path 64" d="M140.522,85.8l-3.711-6.254h7.419Z" transform="translate(-66.29 -79.538)" fill="#3c3c3c" fill-rule="evenodd"/>
        <path id="Path_65" data-name="Path 65" d="M135.182,85.8l-3.712-6.255h7.422Z" transform="translate(-68.374 -79.539)" fill="#4f4f4f" fill-rule="evenodd"/>
        <path id="Path_66" data-name="Path 66" d="M141.562,85.8h-7.423l3.713-6.252Z" transform="translate(-67.333 -79.538)" fill="#454545" fill-rule="evenodd"/>
        <path id="Path_67" data-name="Path 67" d="M128.8,103.786h7.423l-3.711-6.252Z" transform="translate(-69.414 -72.517)" fill="#717171" fill-rule="evenodd"/>
        <path id="Path_68" data-name="Path 68" d="M132.515,108.283l3.71-6.252H128.8Z" transform="translate(-69.415 -70.763)" fill="#636363" fill-rule="evenodd"/>
        <path id="Path_69" data-name="Path 69" d="M131.471,99.289h7.422l-3.71-6.252Z" transform="translate(-68.373 -74.272)" fill="#767676" fill-rule="evenodd"/>
        <path id="Path_70" data-name="Path 70" d="M135.182,103.786l3.711-6.254h-7.421Z" transform="translate(-68.374 -72.518)" fill="#909090" fill-rule="evenodd"/>
        <path id="Path_71" data-name="Path 71" d="M126.134,108.284h7.423l-3.712-6.252Z" transform="translate(-70.457 -70.762)" fill="#4f4f4f" fill-rule="evenodd"/>
        <path id="Path_72" data-name="Path 72" d="M129.845,112.782l3.71-6.252h-7.421Z" transform="translate(-70.456 -69.007)" fill="#3a3a3a" fill-rule="evenodd"/>
        <path id="Path_73" data-name="Path 73" d="M123.465,112.782h7.423l-3.71-6.254Z" transform="translate(-71.498 -69.007)" fill="#2f2f2f" fill-rule="evenodd"/>
        <path id="Path_74" data-name="Path 74" d="M140.523,102.032l-3.711,6.252h7.422Z" transform="translate(-66.289 -70.762)" fill="#434343" fill-rule="evenodd"/>
        <path id="Path_75" data-name="Path 75" d="M137.855,106.529l-3.711,6.252h7.421Z" transform="translate(-67.331 -69.006)" fill="#454545" fill-rule="evenodd"/>
        <path id="Path_76" data-name="Path 76" d="M144.234,106.529h-7.422l3.713,6.254Z" transform="translate(-66.289 -69.007)" fill="#3d3d3d" fill-rule="evenodd"/>
        <path id="Path_77" data-name="Path 77" d="M127.175,117.279l3.712-6.252h-7.422Z" transform="translate(-71.499 -67.252)" fill="#252525" fill-rule="evenodd"/>
        <path id="Path_78" data-name="Path 78" d="M120.8,117.281h7.423l-3.712-6.254Z" transform="translate(-72.541 -67.251)" fill="#1c1c1c" fill-rule="evenodd"/>
        <path id="Path_79" data-name="Path 79" d="M135.188,111.028l-3.713,6.254H138.9Z" transform="translate(-68.372 -67.25)" fill="#3d3d3d" fill-rule="evenodd"/>
        <path id="Path_80" data-name="Path 80" d="M141.566,111.027h-7.423l3.711,6.254Z" transform="translate(-67.33 -67.251)" fill="#565656" fill-rule="evenodd"/>
        <path id="Path_81" data-name="Path 81" d="M129.849,111.028l-3.712,6.254h7.422Z" transform="translate(-70.456 -67.25)" fill="#2f2f2f" fill-rule="evenodd"/>
        <path id="Path_82" data-name="Path 82" d="M136.226,111.027H128.8l3.71,6.254Z" transform="translate(-69.414 -67.251)" fill="#3b3b3b" fill-rule="evenodd"/>
        <g id="Group_7" data-name="Group 7">
          <path id="Path_83" data-name="Path 83" d="M104.781,108.281H112.2l-3.708-6.245Z" transform="translate(-78.791 -70.76)" fill="#434343"/>
          <path id="Path_84" data-name="Path 84" d="M106.861,97.54l0-.007H99.444l0,.009,3.706,6.244h.01Z" transform="translate(-80.877 -72.518)" fill="#a1a1a1"/>
          <path id="Path_85" data-name="Path 85" d="M109.526,102.03h-7.413l0,.009,3.708,6.245h.007l3.707-6.245Z" transform="translate(-79.835 -70.763)" fill="#494949"/>
          <path id="Path_86" data-name="Path 86" d="M107.452,102.03l0,.009,3.707,6.245h.009l3.708-6.246,0-.007Z" transform="translate(-77.75 -70.763)" fill="#3d3d3d"/>
          <path id="Path_87" data-name="Path 87" d="M102.1,102.03l0,.009,0-.009Z" transform="translate(-79.836 -70.763)" fill="#919191"/>
          <path id="Path_88" data-name="Path 88" d="M112.787,102.037l0-.007h-.007Z" transform="translate(-75.668 -70.763)" fill="#919191"/>
          <path id="Path_89" data-name="Path 89" d="M118.127,93.044l0-.01h-.01Z" transform="translate(-73.584 -74.274)" fill="#919191"/>
          <path id="Path_90" data-name="Path 90" d="M115.463,88.536h-.011l0,.01Z" transform="translate(-74.626 -76.029)" fill="#919191"/>
          <path id="Path_91" data-name="Path 91" d="M103.149,93.042l-3.706,6.244h7.412Z" transform="translate(-80.875 -74.271)" fill="#b7b7b7"/>
          <path id="Path_92" data-name="Path 92" d="M119.161,94.789h.011l3.706-6.244,0-.009h-7.412l0,.01Z" transform="translate(-74.625 -76.029)" fill="#494949"/>
          <path id="Path_93" data-name="Path 93" d="M111.158,88.544l-3.706,6.243h7.412Z" transform="translate(-77.749 -76.026)" fill="#8c8c8c"/>
          <path id="Path_94" data-name="Path 94" d="M107.449,102.039l0-.009h-.01Z" transform="translate(-77.751 -70.763)" fill="#919191"/>
          <path id="Path_95" data-name="Path 95" d="M116.5,88.544l-3.706,6.243H120.2Z" transform="translate(-75.665 -76.027)" fill="#5f5f5f"/>
          <path id="Path_96" data-name="Path 96" d="M112.795,93.034h-.012l.006.01Z" transform="translate(-75.668 -74.274)" fill="#919191"/>
          <path id="Path_97" data-name="Path 97" d="M107.455,93.034h-.011l.006.011Z" transform="translate(-77.752 -74.274)" fill="#919191"/>
          <path id="Path_98" data-name="Path 98" d="M105.826,99.289l3.707-6.244-.006-.011h-7.411l-.007.011,3.707,6.244Z" transform="translate(-79.834 -74.274)" fill="#d4d4d4"/>
          <path id="Path_99" data-name="Path 99" d="M104.774,97.532l0,.007.006-.007Z" transform="translate(-78.794 -72.518)" fill="#919191"/>
          <path id="Path_100" data-name="Path 100" d="M110.117,88.546l3.706,6.243h.011l3.706-6.243-.006-.01h-7.411Z" transform="translate(-76.708 -76.029)" fill="#767676"/>
          <path id="Path_101" data-name="Path 101" d="M99.436,106.528l0,.007,0-.007Z" transform="translate(-80.878 -69.007)" fill="#919191"/>
          <path id="Path_102" data-name="Path 102" d="M115.456,106.534l0-.006h-.007Z" transform="translate(-74.625 -69.007)" fill="#919191"/>
          <path id="Path_103" data-name="Path 103" d="M118.125,102.037l3.707,6.246h.006l3.708-6.247,0-.006h-7.417Z" transform="translate(-73.583 -70.763)" fill="#3d3d3d"/>
          <path id="Path_104" data-name="Path 104" d="M115.458,108.281h7.416l-3.708-6.246Z" transform="translate(-74.623 -70.76)" fill="#3b3b3b"/>
          <path id="Path_105" data-name="Path 105" d="M120.2,102.03H112.79l0,.007,3.706,6.246h.009l3.708-6.246Z" transform="translate(-75.666 -70.763)" fill="#565656"/>
          <path id="Path_106" data-name="Path 106" d="M104.787,88.536h-.013l.007.01Z" transform="translate(-78.794 -76.029)" fill="#919191"/>
          <path id="Path_107" data-name="Path 107" d="M99.447,88.536h-.012l.006.011Z" transform="translate(-80.878 -76.029)" fill="#919191"/>
          <path id="Path_108" data-name="Path 108" d="M108.484,94.789h.011l3.706-6.243-.007-.01h-7.41l-.006.01Z" transform="translate(-78.792 -76.029)" fill="#a1a1a1"/>
          <path id="Path_109" data-name="Path 109" d="M101.523,88.547l-.006-.011h-7.41l-.007.011,3.706,6.241h.012Z" transform="translate(-82.959 -76.029)" fill="#3d3d3d"/>
          <path id="Path_110" data-name="Path 110" d="M104.192,102.039l0-.009H96.774l0,.01,3.707,6.244h.009Z" transform="translate(-81.918 -70.763)" fill="#767676"/>
          <path id="Path_111" data-name="Path 111" d="M96.766,102.03l0,.01,0-.01Z" transform="translate(-81.919 -70.763)" fill="#919191"/>
          <path id="Path_112" data-name="Path 112" d="M96.765,93.034l.006.012.007-.012Z" transform="translate(-81.92 -74.274)" fill="#919191"/>
          <path id="Path_113" data-name="Path 113" d="M130.881,94.787l-3.706-6.244-3.707,6.244Z" transform="translate(-71.497 -76.026)" fill="#454545"/>
          <path id="Path_114" data-name="Path 114" d="M123.469,102.03h-.007l0,.006Z" transform="translate(-71.5 -70.763)" fill="#919191"/>
          <path id="Path_115" data-name="Path 115" d="M115.465,79.541h-.013l.007.012Z" transform="translate(-74.627 -79.541)" fill="#919191"/>
          <path id="Path_116" data-name="Path 116" d="M121.834,79.549l-3.7,6.243h7.41Z" transform="translate(-73.581 -79.538)" fill="#d4d4d4"/>
          <path id="Path_117" data-name="Path 117" d="M112.786,111.031l0,0h-.006Z" transform="translate(-75.667 -67.251)" fill="#919191"/>
          <path id="Path_118" data-name="Path 118" d="M107.449,111.032l0-.006h-.006Z" transform="translate(-77.751 -67.251)" fill="#919191"/>
          <path id="Path_119" data-name="Path 119" d="M102.108,111.032l0-.006h-.006Z" transform="translate(-79.835 -67.251)" fill="#919191"/>
          <path id="Path_120" data-name="Path 120" d="M120.8,106.533v0h0Z" transform="translate(-72.542 -69.007)" fill="#919191"/>
          <path id="Path_121" data-name="Path 121" d="M120.205,112.78l-3.708-6.248-3.708,6.248Z" transform="translate(-75.666 -69.006)" fill="#252525"/>
          <path id="Path_122" data-name="Path 122" d="M126.138,106.528h-.006l0,0Z" transform="translate(-70.457 -69.007)" fill="#919191"/>
          <path id="Path_123" data-name="Path 123" d="M110.113,88.536l.007.01.006-.01Z" transform="translate(-76.71 -76.029)" fill="#919191"/>
          <path id="Path_124" data-name="Path 124" d="M128.808,102.03H128.8l0,.006Z" transform="translate(-69.415 -70.763)" fill="#919191"/>
          <path id="Path_125" data-name="Path 125" d="M123.464,111.03l0,0h0Z" transform="translate(-71.499 -67.251)" fill="#919191"/>
          <path id="Path_126" data-name="Path 126" d="M86.1,103.782h7.412L89.8,97.54Z" transform="translate(-86.084 -72.516)" fill="#2f2f2f"/>
          <path id="Path_127" data-name="Path 127" d="M88.757,97.532l.006.01,0-.01Z" transform="translate(-85.045 -72.518)" fill="#919191"/>
          <path id="Path_128" data-name="Path 128" d="M100.479,79.55l-3.7,6.24h7.407Z" transform="translate(-81.916 -79.537)" fill="#3d3d3d"/>
          <path id="Path_129" data-name="Path 129" d="M104.773,79.541l.007.013.009-.013Z" transform="translate(-78.795 -79.541)" fill="#919191"/>
          <path id="Path_130" data-name="Path 130" d="M91.432,111.033l0-.007h-.009Z" transform="translate(-84.003 -67.251)" fill="#919191"/>
          <path id="Path_131" data-name="Path 131" d="M88.757,106.528l.006.009,0-.009Z" transform="translate(-85.045 -69.007)" fill="#919191"/>
          <path id="Path_132" data-name="Path 132" d="M94.1,117.278h7.417l-3.707-6.247Z" transform="translate(-82.959 -67.25)" fill="#767676"/>
          <path id="Path_133" data-name="Path 133" d="M117.534,108.281l-3.706-6.246-3.708,6.246Z" transform="translate(-76.707 -70.761)" fill="#454545"/>
          <path id="Path_134" data-name="Path 134" d="M104.779,106.534l0-.006h-.009Z" transform="translate(-78.793 -69.007)" fill="#919191"/>
          <path id="Path_135" data-name="Path 135" d="M125.543,94.787l-3.707-6.244-3.706,6.244Z" transform="translate(-73.581 -76.027)" fill="#434343"/>
          <path id="Path_136" data-name="Path 136" d="M118.13,102.03h-.007l0,.007Z" transform="translate(-73.583 -70.763)" fill="#919191"/>
          <path id="Path_137" data-name="Path 137" d="M110.118,106.534l0-.006h-.009Z" transform="translate(-76.71 -69.007)" fill="#919191"/>
          <path id="Path_138" data-name="Path 138" d="M99.442,108.281h7.414l-3.707-6.245Z" transform="translate(-80.875 -70.76)" fill="#5f5f5f"/>
          <path id="Path_139" data-name="Path 139" d="M105.819,88.544l-3.706,6.244h7.411Z" transform="translate(-79.833 -76.027)" fill="#b7b7b7"/>
          <path id="Path_140" data-name="Path 140" d="M102.1,93.034l0,.011.007-.011Z" transform="translate(-79.836 -74.274)" fill="#919191"/>
          <path id="Path_141" data-name="Path 141" d="M96.773,103.783h7.413l-3.706-6.244Z" transform="translate(-81.916 -72.515)" fill="#8c8c8c"/>
          <path id="Path_142" data-name="Path 142" d="M99.435,97.532l.006.009.006-.009Z" transform="translate(-80.877 -72.518)" fill="#919191"/>
          <path id="Path_143" data-name="Path 143" d="M91.435,94.786h7.41l-3.7-6.241Z" transform="translate(-84 -76.026)" fill="#3b3b3b"/>
          <path id="Path_144" data-name="Path 144" d="M94.1,97.532l.006.009,0-.009Z" transform="translate(-82.962 -72.518)" fill="#919191"/>
          <path id="Path_145" data-name="Path 145" d="M97.809,84.048l-3.7,6.24h7.41Z" transform="translate(-82.958 -77.782)" fill="#565656"/>
          <path id="Path_146" data-name="Path 146" d="M94.1,106.535l0-.007H94.1Z" transform="translate(-82.961 -69.007)" fill="#919191"/>
          <path id="Path_147" data-name="Path 147" d="M112.8,84.038h-.013l.006.011Z" transform="translate(-75.669 -77.785)" fill="#919191"/>
          <path id="Path_148" data-name="Path 148" d="M120.8,88.536h-.011l.006.009Z" transform="translate(-72.542 -76.029)" fill="#919191"/>
          <path id="Path_149" data-name="Path 149" d="M111.156,79.55l-3.7,6.241h7.408Z" transform="translate(-77.749 -79.537)" fill="#767676"/>
          <path id="Path_150" data-name="Path 150" d="M107.443,84.038l.006.012.007-.012Z" transform="translate(-77.752 -77.785)" fill="#919191"/>
          <path id="Path_151" data-name="Path 151" d="M89.807,108.284l3.707-6.244-.006-.01H86.1l-.006.01,3.706,6.244Z" transform="translate(-86.086 -70.763)" fill="#3a3a3a"/>
          <path id="Path_152" data-name="Path 152" d="M86.087,102.03l.006.01.006-.01Z" transform="translate(-86.087 -70.763)" fill="#919191"/>
          <path id="Path_153" data-name="Path 153" d="M115.456,79.553l3.706,6.241h.012l3.7-6.243-.007-.011h-7.408Z" transform="translate(-74.624 -79.541)" fill="#b7b7b7"/>
          <path id="Path_154" data-name="Path 154" d="M120.805,79.541h-.015l.007.011Z" transform="translate(-72.542 -79.541)" fill="#919191"/>
          <path id="Path_155" data-name="Path 155" d="M99.451,79.541h-.017l.009.013Z" transform="translate(-80.878 -79.541)" fill="#919191"/>
          <path id="Path_156" data-name="Path 156" d="M103.143,85.794h.015l3.7-6.24-.007-.013H99.448l-.009.013Z" transform="translate(-80.876 -79.541)" fill="#434343"/>
          <path id="Path_157" data-name="Path 157" d="M124.5,85.794h.012l3.711-6.254H120.8l-.007.011Z" transform="translate(-72.54 -79.541)" fill="#adadad"/>
          <path id="Path_158" data-name="Path 158" d="M98.853,111.032l0-.006H91.434l0,.007,3.707,6.246h.006Z" transform="translate(-84.002 -67.251)" fill="#909090"/>
          <path id="Path_159" data-name="Path 159" d="M131.475,97.537l0,0h-.009Z" transform="translate(-68.374 -72.518)" fill="#919191"/>
          <path id="Path_160" data-name="Path 160" d="M99.436,115.524l0,.006,0-.006Z" transform="translate(-80.877 -65.496)" fill="#919191"/>
          <path id="Path_161" data-name="Path 161" d="M104.776,115.524l0,0,0,0Z" transform="translate(-78.793 -65.496)" fill="#919191"/>
          <path id="Path_162" data-name="Path 162" d="M94.1,115.524l0,.006,0-.006Z" transform="translate(-82.961 -65.496)" fill="#919191"/>
          <path id="Path_163" data-name="Path 163" d="M110.115,115.524l0,0v0Z" transform="translate(-76.709 -65.496)" fill="#919191"/>
          <path id="Path_164" data-name="Path 164" d="M120.794,115.528l0,0h0Z" transform="translate(-72.541 -65.496)" fill="#919191"/>
          <path id="Path_165" data-name="Path 165" d="M123.465,93.044l0-.01h-.009Z" transform="translate(-71.5 -74.274)" fill="#919191"/>
          <path id="Path_166" data-name="Path 166" d="M115.454,115.524l0,0,0,0Z" transform="translate(-74.625 -65.496)" fill="#919191"/>
          <path id="Path_167" data-name="Path 167" d="M94.109,88.536H94.1l.006.011Z" transform="translate(-82.962 -76.029)" fill="#919191"/>
          <path id="Path_168" data-name="Path 168" d="M102.12,84.038H102.1l.009.012Z" transform="translate(-79.836 -77.785)" fill="#919191"/>
          <path id="Path_169" data-name="Path 169" d="M91.426,93.034l.007.012.007-.012Z" transform="translate(-84.003 -74.274)" fill="#919191"/>
          <path id="Path_170" data-name="Path 170" d="M96.779,84.038h-.015l.007.013Z" transform="translate(-81.92 -77.785)" fill="#919191"/>
          <path id="Path_171" data-name="Path 171" d="M100.475,90.292h.013l3.7-6.241-.007-.012H96.777l-.007.013Z" transform="translate(-81.918 -77.785)" fill="#454545"/>
          <path id="Path_172" data-name="Path 172" d="M95.148,99.289l3.7-6.243,0-.012H91.437l-.006.012,3.7,6.243Z" transform="translate(-84.002 -74.274)" fill="#2f2f2f"/>
          <path id="Path_173" data-name="Path 173" d="M91.427,102.03l0,.01,0-.01Z" transform="translate(-84.004 -70.763)" fill="#919191"/>
          <path id="Path_174" data-name="Path 174" d="M92.47,102.037l-3.706,6.244h7.413Z" transform="translate(-85.043 -70.76)" fill="#4f4f4f"/>
          <path id="Path_175" data-name="Path 175" d="M92.471,93.042l-3.706,6.243h7.411Z" transform="translate(-85.043 -74.27)" fill="#1c1c1c"/>
          <path id="Path_176" data-name="Path 176" d="M96.184,97.541l-.006-.009H88.767l-.006.01,3.706,6.243h.01Z" transform="translate(-85.044 -72.518)" fill="#252525"/>
          <path id="Path_177" data-name="Path 177" d="M130.892,85.794l-3.711-6.254-3.712,6.254Z" transform="translate(-71.497 -79.541)" fill="#888"/>
          <path id="Path_178" data-name="Path 178" d="M123.474,84.038h-.013l.006.011Z" transform="translate(-71.501 -77.785)" fill="#919191"/>
          <path id="Path_179" data-name="Path 179" d="M120.8,88.536l0,.009,3.706,6.244h.01l3.706-6.244,0-.009Z" transform="translate(-72.541 -76.029)" fill="#3d3d3d"/>
          <path id="Path_180" data-name="Path 180" d="M118.133,84.038h-.012l.006.011Z" transform="translate(-73.584 -77.785)" fill="#919191"/>
          <path id="Path_181" data-name="Path 181" d="M126.135,88.545l.006-.009h-.011Z" transform="translate(-70.458 -76.029)" fill="#919191"/>
          <path id="Path_182" data-name="Path 182" d="M108.5,85.794l3.7-6.241-.007-.012h-7.407l-.009.013,3.7,6.24Z" transform="translate(-78.792 -79.541)" fill="#5f5f5f"/>
          <path id="Path_183" data-name="Path 183" d="M105.819,79.55l-3.7,6.241h7.41Z" transform="translate(-79.832 -79.537)" fill="#494949"/>
          <path id="Path_184" data-name="Path 184" d="M110.127,79.541h-.015l.007.012Z" transform="translate(-76.71 -79.541)" fill="#919191"/>
          <path id="Path_185" data-name="Path 185" d="M113.822,85.794h.013l3.7-6.241-.007-.012h-7.407l-.009.012Z" transform="translate(-76.709 -79.541)" fill="#8c8c8c"/>
          <path id="Path_186" data-name="Path 186" d="M120.2,85.791,116.5,79.549l-3.7,6.241Z" transform="translate(-75.665 -79.537)" fill="#a1a1a1"/>
          <path id="Path_187" data-name="Path 187" d="M96.179,106.528H88.765l0,.009,3.706,6.245h.009l3.707-6.246Z" transform="translate(-85.044 -69.007)" fill="#636363"/>
          <path id="Path_188" data-name="Path 188" d="M128.805,93.043l0-.009h-.01Z" transform="translate(-69.417 -74.274)" fill="#919191"/>
          <path id="Path_189" data-name="Path 189" d="M122.875,106.528H115.46l0,.006,3.708,6.248h0l3.71-6.249Z" transform="translate(-74.624 -69.007)" fill="#2f2f2f"/>
          <path id="Path_190" data-name="Path 190" d="M104.78,117.277H112.2l-3.708-6.247Z" transform="translate(-78.791 -67.25)" fill="#4f4f4f"/>
          <path id="Path_191" data-name="Path 191" d="M109.528,111.026h-7.416l0,.006,3.707,6.247h.006l3.708-6.247Z" transform="translate(-79.835 -67.251)" fill="#636363"/>
          <path id="Path_192" data-name="Path 192" d="M99.441,117.278h7.417l-3.708-6.247Z" transform="translate(-80.875 -67.25)" fill="#717171"/>
          <path id="Path_193" data-name="Path 193" d="M96.766,111.026l0,.006,0-.006Z" transform="translate(-81.919 -67.251)" fill="#919191"/>
          <path id="Path_194" data-name="Path 194" d="M104.189,111.026H96.773l0,.006,3.707,6.247h.006l3.708-6.247Z" transform="translate(-81.918 -67.251)" fill="#909090"/>
          <path id="Path_195" data-name="Path 195" d="M91.433,112.78h7.414l-3.707-6.246Z" transform="translate(-84.001 -69.005)" fill="#717171"/>
          <path id="Path_196" data-name="Path 196" d="M114.867,111.026H107.45l0,.006,3.708,6.247h0l3.708-6.249Z" transform="translate(-77.751 -67.251)" fill="#3a3a3a"/>
          <path id="Path_197" data-name="Path 197" d="M118.126,111.031l0,0h-.006Z" transform="translate(-73.584 -67.251)" fill="#919191"/>
          <path id="Path_198" data-name="Path 198" d="M110.119,117.278h7.418l-3.71-6.249Z" transform="translate(-76.707 -67.25)" fill="#2f2f2f"/>
          <path id="Path_199" data-name="Path 199" d="M112.79,111.026l0,0,3.71,6.249h0l3.708-6.249,0,0Z" transform="translate(-75.666 -67.251)" fill="#1c1c1c"/>
        </g>
      </g>
      <g id="Group_9" data-name="Group 9" transform="translate(0 0)" style="mix-blend-mode: hard-light;isolation: isolate">
        <path id="Path_200" data-name="Path 200" d="M104.632,79.541,86.087,110.808l11.129,18.766,29.691,0,11.139-18.763h-29.69L115.779,98.3h29.69l-3.71-6.254h-29.7l-11.123,18.761,3.71,6.252,22.265.007-3.716,6.247H100.931L93.51,110.808l14.841-25,51.956-.01,3.708,6.257,3.712-6.252-3.712-6.25Z" transform="translate(-86.087 -79.541)" fill="url(#linear-gradient)"/>
        <path id="Path_201" data-name="Path 201" d="M135.628,88.54l3.71,6.255-18.55,31.262h22.268L154.181,107.3,143.052,88.542ZM146.759,107.3l-7.415,12.5h-7.424l11.133-18.76Z" transform="translate(-72.543 -76.028)" fill="url(#linear-gradient-2)"/>
        <path id="Path_202" data-name="Path 202" d="M178.2,79.547l-22.268,0L144.809,98.3l14.844,25.011h-7.422l-3.708-6.252-3.713,6.252,3.713,6.25,22.265,0L159.653,110.81ZM155.942,104.56,152.233,98.3l7.415-12.5h7.424Z" transform="translate(-63.169 -79.539)" fill="url(#linear-gradient-3)"/>
      </g>
    </g>
  </g>
</svg>
