# Australia Post Shipping 使用示例

## 概述

本文檔提供了如何使用新增的 Australia Post 運送方式功能的具體示例。

## 後端 API 使用

### 獲取單個訂單

```bash
GET /v1/wc-orders/12345
```

**響應示例：**
```json
{
  "id": 12345,
  "order_number": "12345",
  "status": "processing",
  "total": 125.50,
  "shipping_method": "australia_post_express",
  "shipping_method_title": "Australia Post Shipping Express Post",
  "shipping_total": 15.50,
  "billing_first_name": "<PERSON>",
  "billing_last_name": "<PERSON><PERSON>",
  "shipping_first_name": "<PERSON>",
  "shipping_last_name": "<PERSON><PERSON>",
  "shipping_address_1": "123 Main St",
  "shipping_city": "Sydney",
  "shipping_state": "NSW",
  "shipping_postcode": "2000",
  "shipping_country": "AU",
  "line_items": [
    {
      "name": "Product A",
      "quantity": 2,
      "total": 110.00
    }
  ]
}
```

### 獲取訂單列表

```bash
GET /v1/wc-orders?page=1&page_size=20
```

**響應示例：**
```json
{
  "orders": [
    {
      "id": 12345,
      "date_created": "2024-01-15T10:30:00Z",
      "status": "processing",
      "total": 125.50,
      "payment_method_title": "Credit Card",
      "customer_name": "John Doe",
      "customer_email": "<EMAIL>",
      "shipping_method": "australia_post_express",
      "shipping_method_title": "Australia Post Shipping Express Post"
    },
    {
      "id": 12346,
      "date_created": "2024-01-15T11:15:00Z",
      "status": "completed",
      "total": 89.99,
      "payment_method_title": "PayPal",
      "customer_name": "Jane Smith",
      "customer_email": "<EMAIL>",
      "shipping_method": "australia_post_parcel",
      "shipping_method_title": "Australia Post Shipping Parcel Post"
    }
  ],
  "total": 150,
  "page": 1,
  "page_size": 20
}
```

## 前端使用

### 在 Vue 組件中使用

```vue
<template>
  <div>
    <h3>訂單運送資訊</h3>
    <p>運送方式: {{ getShippingMethodDisplay(order.shipping_method, order.shipping_method_title) }}</p>
    <p>運費: AU$ {{ formatNumber(order.shipping_total, 2) }}</p>
  </div>
</template>

<script setup lang="ts">
import { WCOrder } from '@/api/order';

const props = defineProps<{
  order: WCOrder;
}>();

const getShippingMethodDisplay = (method: string, title: string) => {
  switch (method) {
    case 'australia_post_express':
      return 'Australia Post Express Post';
    case 'australia_post_parcel':
      return 'Australia Post Parcel Post';
    case 'australia_post':
      return 'Australia Post';
    default:
      return title || method || 'Unknown';
  }
};
</script>
```

### 運送方式過濾

```typescript
// 過濾 Australia Post Express 訂單
const expressOrders = orders.filter(order => 
  order.shipping_method === 'australia_post_express'
);

// 過濾所有 Australia Post 訂單
const australiaPostOrders = orders.filter(order => 
  order.shipping_method?.startsWith('australia_post')
);

// 統計各種運送方式的使用次數
const shippingMethodStats = orders.reduce((stats, order) => {
  const method = order.shipping_method || 'unknown';
  stats[method] = (stats[method] || 0) + 1;
  return stats;
}, {} as Record<string, number>);
```

## 資料庫查詢示例

### 直接 SQL 查詢

```sql
-- 查詢所有 Australia Post Express 訂單
SELECT o.id, o.total_amount, si.order_item_name as shipping_title
FROM wp_wc_orders o
LEFT JOIN wp_woocommerce_order_items si ON o.id = si.order_id AND si.order_item_type = 'shipping'
LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'
WHERE sim.meta_value LIKE '%australia_post%' 
  AND (sim.meta_value LIKE '%express%' OR si.order_item_name LIKE '%express%')
ORDER BY o.date_created_gmt DESC;

-- 統計各種 Australia Post 運送方式的使用情況
SELECT 
  CASE 
    WHEN sim.meta_value LIKE '%express%' OR si.order_item_name LIKE '%express%' THEN 'Australia Post Express'
    WHEN sim.meta_value LIKE '%parcel%' OR si.order_item_name LIKE '%parcel%' THEN 'Australia Post Parcel'
    ELSE 'Australia Post Other'
  END as shipping_type,
  COUNT(*) as order_count,
  AVG(o.total_amount) as avg_order_value
FROM wp_wc_orders o
LEFT JOIN wp_woocommerce_order_items si ON o.id = si.order_id AND si.order_item_type = 'shipping'
LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'
WHERE sim.meta_value LIKE '%australia_post%'
GROUP BY shipping_type
ORDER BY order_count DESC;
```

## 常見問題

### Q: 如何添加新的運送方式解析？

A: 在 `parseAustraliaPostShippingMethod` 函數中添加新的條件判斷：

```go
func (r *wcOrderRepository) parseAustraliaPostShippingMethod(methodID, methodTitle string) string {
    if strings.Contains(strings.ToLower(methodID), "australia_post") {
        titleLower := strings.ToLower(methodTitle)
        
        // 添加新的運送方式
        if strings.Contains(titleLower, "priority") {
            return "australia_post_priority"
        }
        // ... 其他現有條件
    }
    return methodID
}
```

### Q: 前端如何顯示自定義的運送方式圖示？

A: 可以在 `getShippingMethodDisplay` 函數中返回包含圖示的 HTML：

```typescript
const getShippingMethodIcon = (method: string) => {
  switch (method) {
    case 'australia_post_express':
      return '🚀'; // 或使用 q-icon
    case 'australia_post_parcel':
      return '📦';
    default:
      return '🚚';
  }
};
```

### Q: 如何處理沒有運送方式資訊的訂單？

A: 系統會自動處理這種情況，顯示 "Unknown" 或原始的方法名稱。可以在前端添加額外的處理邏輯：

```typescript
const getShippingMethodDisplay = (method?: string, title?: string) => {
  if (!method && !title) {
    return t('noShippingMethod'); // "無運送方式"
  }
  // ... 其他邏輯
};
```
