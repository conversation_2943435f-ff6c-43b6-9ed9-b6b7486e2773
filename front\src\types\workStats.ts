import { ClockPair } from '@/api/attendance';

// 工作統計介面
export interface WorkStatistics {
  workDays: number; // 工作天數
  totalWorkHours: number; // 總工作時數（小時）
  averageWorkHours: number; // 平均每日工作時數
  validPairs: ClockPair[]; // 有效的打卡對
  invalidPairs: ClockPair[]; // 無效的打卡對（缺少打卡記錄）
}

// 計算兩個時間之間的小時差
function calculateHoursDifference(startTime: Date, endTime: Date): number {
  const diffInMs = endTime.getTime() - startTime.getTime();
  return diffInMs / (1000 * 60 * 60); // 轉換為小時
}

// 計算工作統計
export function calculateWorkStatistics(
  clockPairs: ClockPair[]
): WorkStatistics {
  let totalWorkHours = 0;
  const validPairs: ClockPair[] = [];
  const invalidPairs: ClockPair[] = [];

  // 處理每個打卡對
  clockPairs.forEach((pair) => {
    // 檢查是否有完整的打卡記錄（上班和下班都要有）
    if (pair.clock_in && pair.clock_out) {
      const clockInTime = new Date(pair.clock_in.clock_time);
      const clockOutTime = new Date(pair.clock_out.clock_time);

      // 確保下班時間晚於上班時間
      if (clockOutTime > clockInTime) {
        const dailyHours = calculateHoursDifference(clockInTime, clockOutTime);
        totalWorkHours += dailyHours;
        validPairs.push(pair);
      } else {
        // 時間順序錯誤的記錄
        invalidPairs.push(pair);
      }
    } else {
      // 缺少打卡記錄
      invalidPairs.push(pair);
    }
  });

  const workDays = validPairs.length;
  const averageWorkHours = workDays > 0 ? totalWorkHours / workDays : 0;

  return {
    workDays,
    totalWorkHours: Math.round(totalWorkHours * 100) / 100, // 保留兩位小數
    averageWorkHours: Math.round(averageWorkHours * 100) / 100,
    validPairs,
    invalidPairs,
  };
}
