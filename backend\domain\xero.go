package domain

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"time"
)

// XeroDate 自定義時間類型，用於處理 Xero API 的日期格式
type XeroDate struct {
	time.Time
}

// UnmarshalJSON 自定義 JSON 解析，處理 Xero 的 \/Date(timestamp+timezone)\/ 格式
func (xd *XeroDate) UnmarshalJSON(data []byte) error {
	// 移除引號
	str := string(data)
	if str == "null" || str == `""` {
		return nil
	}

	// 移除外層引號
	if len(str) >= 2 && str[0] == '"' && str[len(str)-1] == '"' {
		str = str[1 : len(str)-1]
	}

	// 解析 \/Date(timestamp+timezone)\/ 格式
	re := regexp.MustCompile(`^\\?/Date\((\d+)([\+\-]\d{4})?\)\\?/$`)
	matches := re.FindStringSubmatch(str)

	if len(matches) >= 2 {
		// 解析時間戳
		timestamp, err := strconv.ParseInt(matches[1], 10, 64)
		if err != nil {
			return fmt.Errorf("failed to parse timestamp: %v", err)
		}

		// 轉換為 time.Time (毫秒轉秒)
		xd.Time = time.Unix(timestamp/1000, (timestamp%1000)*1000000).UTC()
		return nil
	}

	// 如果不是 Xero 格式，嘗試標準 ISO 8601 格式
	layouts := []string{
		time.RFC3339,
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05",
		"2006-01-02",
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, str); err == nil {
			xd.Time = t
			return nil
		}
	}

	return fmt.Errorf("unable to parse date: %s", str)
}

// MarshalJSON 自定義 JSON 序列化，輸出標準 ISO 8601 格式
func (xd XeroDate) MarshalJSON() ([]byte, error) {
	if xd.Time.IsZero() {
		return []byte("null"), nil
	}
	return json.Marshal(xd.Time.Format(time.RFC3339))
}

// XeroConfig Xero設定模型
type XeroConfig struct {
	ID           int64     `json:"-" gorm:"primarykey"`
	UUID         string    `json:"uuid"`
	ClientID     string    `json:"client_id"`
	ClientSecret string    `json:"client_secret"`
	RedirectURI  string    `json:"redirect_uri" `
	Scopes       string    `json:"scopes"`
	DefaultEmail string    `json:"default_email"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// XeroToken Xero Token模型
type XeroToken struct {
	ID           int64      `json:"-" gorm:"primarykey"`
	UUID         string     `json:"uuid"`
	ConfigID     int64      `json:"config_id" `
	AccessToken  string     `json:"access_token"`
	RefreshToken string     `json:"refresh_token" `
	TokenType    string     `json:"token_type" gorm:"default:Bearer"`
	ExpiresAt    *time.Time `json:"expires_at"`
	TenantID     string     `json:"tenant_id"`
	TenantName   string     `json:"tenant_name"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`

	// 關聯
	Config XeroConfig `json:"config,omitempty" gorm:"foreignKey:ConfigID"`
}

// XeroAuthRequest OAuth2認證請求結構
type XeroAuthRequest struct {
	ClientID     string `json:"client_id" binding:"required"`
	ClientSecret string `json:"client_secret" binding:"required"`
	RedirectURI  string `json:"redirect_uri" binding:"required"`
	Scopes       string `json:"scopes"`
	DefaultEmail string `json:"default_email"`
}

// XeroCallbackRequest OAuth2回調結構
type XeroCallbackRequest struct {
	Code  string `json:"code" binding:"required"`
	State string `json:"state" binding:"required"`
}

// XeroTokenResponse Token回應結構
type XeroTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
}

// XeroConnectionResponse 連接狀態回應
type XeroConnectionResponse struct {
	Connections []XeroConnection `json:"connections"`
}

type XeroConnection struct {
	ID             string `json:"id"`
	TenantID       string `json:"tenantId"`
	TenantType     string `json:"tenantType"`
	TenantName     string `json:"tenantName"`
	CreatedDateUTC string `json:"createdDateUtc"`
	UpdatedDateUTC string `json:"updatedDateUtc"`
}

// XeroInvoice Invoice 結構
type XeroInvoice struct {
	InvoiceID       string         `json:"InvoiceID,omitempty"`
	InvoiceNumber   string         `json:"InvoiceNumber,omitempty"`
	Type            string         `json:"Type"` // ACCREC, ACCPAY
	Contact         XeroContact    `json:"Contact"`
	Date            XeroDate       `json:"Date"`
	DueDate         XeroDate       `json:"DueDate,omitempty"`
	Status          string         `json:"Status,omitempty"`          // DRAFT, SUBMITTED, AUTHORISED, PAID, VOIDED
	LineAmountTypes string         `json:"LineAmountTypes,omitempty"` // Exclusive, Inclusive, NoTax
	SubTotal        float64        `json:"SubTotal,omitempty"`
	TotalTax        float64        `json:"TotalTax,omitempty"`
	Total           float64        `json:"Total,omitempty"`
	CurrencyCode    string         `json:"CurrencyCode,omitempty"`
	UpdatedDateUTC  XeroDate       `json:"UpdatedDateUTC,omitempty"`
	LineItems       []XeroLineItem `json:"LineItems"`
	Reference       string         `json:"Reference,omitempty"`
}

// XeroContact 聯絡人結構
type XeroContact struct {
	ContactID    string `json:"ContactID,omitempty"`
	Name         string `json:"Name"`
	EmailAddress string `json:"EmailAddress,omitempty"`
}

// XeroLineItem 發票項目結構
type XeroLineItem struct {
	LineItemID  string  `json:"LineItemID,omitempty"`
	Description string  `json:"Description"`
	Quantity    float64 `json:"Quantity"`
	UnitAmount  float64 `json:"UnitAmount"`
	LineAmount  float64 `json:"LineAmount,omitempty"`
	TaxAmount   float64 `json:"TaxAmount,omitempty"`
	AccountCode string  `json:"AccountCode,omitempty"`
	TaxType     string  `json:"TaxType,omitempty"`
}

// XeroInvoiceParams 查詢參數
type XeroInvoiceParams struct {
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
	Status    string `json:"status,omitempty"`
	ContactID string `json:"contact_id,omitempty"`
	DateFrom  string `json:"date_from,omitempty"`
	DateTo    string `json:"date_to,omitempty"`
}

// XeroInvoiceRequest 建立/更新發票請求
type XeroInvoiceRequest struct {
	Invoices []XeroInvoice `json:"Invoices"`
}

// XeroInvoiceResponse 單一發票回應
type XeroInvoiceResponse struct {
	ID       string        `json:"Id,omitempty"`
	Status   string        `json:"Status,omitempty"`
	Invoices []XeroInvoice `json:"Invoices,omitempty"`
}

// XeroInvoicesResponse 發票列表回應
type XeroInvoicesResponse struct {
	ID       string        `json:"Id,omitempty"`
	Status   string        `json:"Status,omitempty"`
	Invoices []XeroInvoice `json:"Invoices,omitempty"`
}

// XeroPayment 付款結構
type XeroPayment struct {
	PaymentID string   `json:"PaymentID,omitempty"`
	Date      XeroDate `json:"Date"`
	Amount    float64  `json:"Amount"`
	Reference string   `json:"Reference,omitempty"`
	Account   struct {
		Code string `json:"Code,omitempty"`
		Name string `json:"Name,omitempty"`
	} `json:"Account,omitempty"`
	Invoice struct {
		InvoiceID string `json:"InvoiceID"`
	} `json:"Invoice"`
}

// XeroPaymentRequest 付款請求
type XeroPaymentRequest struct {
	Payments []XeroPayment `json:"Payments"`
}

// XeroPaymentResponse 付款回應
type XeroPaymentResponse struct {
	ID       string        `json:"Id,omitempty"`
	Status   string        `json:"Status,omitempty"`
	Payments []XeroPayment `json:"Payments,omitempty"`
}

// XeroAccount 帳戶結構
type XeroAccount struct {
	AccountID               string   `json:"AccountID,omitempty"`
	Code                    string   `json:"Code"`
	Name                    string   `json:"Name"`
	Type                    string   `json:"Type,omitempty"`    // BANK, CURRENT, EQUITY, EXPENSE, FIXED, INVENTORY, LIABILITY, PREPAYMENT, REVENUE, SALES, OVERHEADS, DEPRECIATN, OTHERINCOME, DIRECTCOSTS, PAYGLIABILITY, SUPERANNUATIONEXPENSE, SUPERANNUATIONLIABILITY, TERMLIABILITY, WAGEPAYABLELIABILITY, WAGESEXPENSE
	TaxType                 string   `json:"TaxType,omitempty"` // 稅務類型
	Description             string   `json:"Description,omitempty"`
	Class                   string   `json:"Class,omitempty"`                   // ASSET, EQUITY, EXPENSE, LIABILITY, REVENUE
	SystemAccount           string   `json:"SystemAccount,omitempty"`           // 系統帳戶類型
	EnablePaymentsToAccount bool     `json:"EnablePaymentsToAccount,omitempty"` // 是否可用於付款
	ShowInExpenseClaims     bool     `json:"ShowInExpenseClaims,omitempty"`     // 是否顯示在費用申請中
	BankAccountNumber       string   `json:"BankAccountNumber,omitempty"`
	BankAccountType         string   `json:"BankAccountType,omitempty"`
	CurrencyCode            string   `json:"CurrencyCode,omitempty"`
	ReportingCode           string   `json:"ReportingCode,omitempty"`
	ReportingCodeName       string   `json:"ReportingCodeName,omitempty"`
	HasAttachments          bool     `json:"HasAttachments,omitempty"`
	UpdatedDateUTC          XeroDate `json:"UpdatedDateUTC,omitempty"`
	AddToWatchlist          bool     `json:"AddToWatchlist,omitempty"`
}

// XeroAccountsRequest 帳戶請求
type XeroAccountsRequest struct {
	Accounts []XeroAccount `json:"Accounts"`
}

// XeroAccountsResponse 帳戶回應
type XeroAccountsResponse struct {
	ID       string        `json:"Id,omitempty"`
	Status   string        `json:"Status,omitempty"`
	Accounts []XeroAccount `json:"Accounts,omitempty"`
}
