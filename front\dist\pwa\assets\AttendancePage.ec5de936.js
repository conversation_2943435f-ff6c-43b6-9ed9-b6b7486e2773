import{Q as c,a as h}from"./QToolbar.b89be0c0.js";import{d as j,r as u,c as _,w,o as H,z as O,p as S,v as G,m as l,j as o,G as R,B as z,A as E,C as m,Q,q as P,t as v,K as V,y as k,H as K}from"./index.09f89dc4.js";import{Q as M}from"./QDate.f6067d5f.js";import{Q as T}from"./QPopupProxy.f63a65d9.js";import{Q as L}from"./QSelect.958fa87e.js";import{Q as J}from"./QTable.5ba31f17.js";import{Q as W}from"./QPage.ce1b4cb5.js";import{C as U}from"./ClosePopup.712518f2.js";import{u as X}from"./vue-i18n.1783a0cb.js";import{A as Z}from"./attendance.6666db33.js";import{f as D}from"./date.6d29930c.js";import{U as $}from"./user.c6f09a36.js";import"./format.054b8074.js";import"./QMenu.531c6599.js";import"./selection.2acb415c.js";import"./QItemSection.3e7b5a38.js";import"./QItemLabel.88180eb1.js";import"./QList.5d1c2d4f.js";import"./use-fullscreen.2a1ec9b4.js";const ee={class:"row items-center justify-end q-gutter-sm"},te={class:"row items-center justify-end q-gutter-sm"},we=j({__name:"AttendancePage",setup(ae){const{t:s}=X(),g=u(),y=u(),C=()=>{setTimeout(()=>{g.value.hide()},50)},A=()=>{setTimeout(()=>{y.value.hide()},50)},x=_(()=>[{name:"name",label:s("name"),field:t=>{var e;return(e=t.user)==null?void 0:e.name},align:"center"},{name:"type",label:s("clockType"),field:t=>s(t.type),align:"center"},{name:"clock_time",label:s("clockTime"),field:t=>D(t.clock_time,"YYYY/MM/DD HH:mm"),align:"center"}]),a=u({from:"",to:""}),p=u(""),B=_(()=>[{label:s("user.all"),value:""},...Y.value]),q=t=>{if(!a.value.to)return!0;const e=new Date(t),r=new Date(a.value.to);return e<=r},F=t=>{if(!a.value.from)return!0;const e=new Date(t),r=new Date(a.value.from);return e>=r};w(()=>a.value.from,t=>{if(t&&a.value.to){const e=new Date(t),r=new Date(a.value.to);e>r&&(a.value.to=t)}}),w(()=>a.value.to,t=>{if(t&&a.value.from){const e=new Date(a.value.from);new Date(t)<e&&(a.value.from=t)}});const I=()=>{const t=new Date,e=new Date(t.getFullYear(),t.getMonth(),1),r=new Date(t.getFullYear(),t.getMonth()+1,0);a.value={from:D(e,"YYYY/MM/DD"),to:D(r,"YYYY/MM/DD")}},b=u([]),i=u({sortBy:"clock_time",descending:!0,page:1,rowsPerPage:20,rowsNumber:0}),d=u(!1),f=async()=>{try{d.value=!0;const t=await Z.fetch({filter:{user_uuid:p.value,start_date:a.value.from,end_date:a.value.to},pagination:i.value});b.value=t.result.data,i.value=t.result.pagination}finally{d.value=!1}},Y=u([]),N=async()=>{try{d.value=!0;const t=await $.fetch();Y.value=t.result.map(e=>({label:e.name,value:e.uuid}))}finally{d.value=!1}};return H(()=>{I(),f(),N()}),(t,e)=>{const r=O("TablePagination");return S(),G(W,null,{default:l(()=>[o(K,{flat:"",square:"",class:"bg-cream"},{default:l(()=>[o(R,null,{default:l(()=>[o(J,{"virtual-scroll":"",rows:b.value,columns:x.value,pagination:i.value,"onUpdate:pagination":e[5]||(e[5]=n=>i.value=n),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",loading:d.value},{top:l(()=>[o(c,null,{default:l(()=>[o(h,null,{default:l(()=>[z(E(m(s)("attendance.history")),1)]),_:1})]),_:1}),o(c,null,{default:l(()=>[o(Q,{modelValue:a.value.from,"onUpdate:modelValue":e[1]||(e[1]=n=>a.value.from=n),label:m(s)("datePicker.from"),mask:"date",rules:["date"],readonly:""},{prepend:l(()=>[o(P,{name:"event",class:"cursor-pointer"},{default:l(()=>[o(T,{ref_key:"fromDatePopup",ref:g,cover:"","transition-show":"scale","transition-hide":"scale"},{default:l(()=>[o(M,{modelValue:a.value.from,"onUpdate:modelValue":[e[0]||(e[0]=n=>a.value.from=n),C],options:q,mask:"YYYY/MM/DD","today-btn":""},{default:l(()=>[v("div",ee,[V(o(k,{label:m(s)("close"),color:"primary",flat:""},null,8,["label"]),[[U]])])]),_:1},8,["modelValue"])]),_:1},512)]),_:1})]),_:1},8,["modelValue","label"]),e[7]||(e[7]=v("span",{class:"q-mx-md"},"\uFF5E",-1)),o(Q,{modelValue:a.value.to,"onUpdate:modelValue":e[3]||(e[3]=n=>a.value.to=n),label:m(s)("datePicker.to"),mask:"date",rules:["date"],readonly:""},{prepend:l(()=>[o(P,{name:"event",class:"cursor-pointer"},{default:l(()=>[o(T,{ref_key:"toDatePopup",ref:y,cover:"","transition-show":"scale","transition-hide":"scale"},{default:l(()=>[o(M,{modelValue:a.value.to,"onUpdate:modelValue":[e[2]||(e[2]=n=>a.value.to=n),A],options:F,mask:"YYYY/MM/DD","today-btn":""},{default:l(()=>[v("div",te,[V(o(k,{label:m(s)("close"),color:"primary",flat:""},null,8,["label"]),[[U]])])]),_:1},8,["modelValue"])]),_:1},512)]),_:1})]),_:1},8,["modelValue","label"])]),_:1}),o(c,null,{default:l(()=>[o(L,{modelValue:p.value,"onUpdate:modelValue":[e[4]||(e[4]=n=>p.value=n),f],options:B.value,"emit-value":"","map-options":"",label:m(s)("user.label")},null,8,["modelValue","options","label"])]),_:1})]),_:1},8,["rows","columns","pagination","loading"]),o(r,{modelValue:i.value,"onUpdate:modelValue":e[6]||(e[6]=n=>i.value=n),onGetData:f},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})}}});export{we as default};
