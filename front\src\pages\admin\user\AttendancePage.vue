<template>
  <q-page>
    <q-card flat square class="bg-cream">
      <q-card-section>
        <q-table
          virtual-scroll
          :rows="items"
          :columns="columns"
          v-model:pagination="pagination"
          hide-pagination
          binary-state-sort
          table-header-class="bg-grey-3"
          :loading="isLoading"
        >
          <template v-slot:top>
            <!-- Title -->
            <q-toolbar>
              <q-toolbar-title>{{ t('attendance.history') }}</q-toolbar-title>
            </q-toolbar>

            <!-- Date Range Selector -->
            <q-toolbar>
              <!-- 開始日期 -->
              <q-input
                v-model="dateRange.from"
                :label="t('datePicker.from')"
                mask="date"
                :rules="['date']"
                readonly
              >
                <template v-slot:prepend>
                  <q-icon name="event" class="cursor-pointer">
                    <q-popup-proxy
                      ref="fromDatePopup"
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-date
                        v-model="dateRange.from"
                        :options="fromDateOptions"
                        mask="YYYY/MM/DD"
                        @update:model-value="closeFromDatePopup"
                        today-btn
                      >
                        <div class="row items-center justify-end q-gutter-sm">
                          <q-btn
                            :label="t('close')"
                            color="primary"
                            flat
                            v-close-popup
                          />
                        </div>
                      </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>

              <span class="q-mx-md">～</span>

              <!-- 結束日期 -->
              <q-input
                v-model="dateRange.to"
                :label="t('datePicker.to')"
                mask="date"
                :rules="['date']"
                readonly
              >
                <template v-slot:prepend>
                  <q-icon name="event" class="cursor-pointer">
                    <q-popup-proxy
                      ref="toDatePopup"
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-date
                        v-model="dateRange.to"
                        :options="toDateOptions"
                        mask="YYYY/MM/DD"
                        @update:model-value="closeToDatePopup"
                        today-btn
                      >
                        <div class="row items-center justify-end q-gutter-sm">
                          <q-btn
                            :label="t('close')"
                            color="primary"
                            flat
                            v-close-popup
                          />
                        </div>
                      </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </q-toolbar>

            <q-toolbar>
              <!-- User Select -->
              <q-select
                v-model="userUUID"
                :options="userOptions"
                emit-value
                map-options
                :label="t('user.label')"
                @update:model-value="getAttendances"
              />
              <!-- Export button -->
              <!-- <q-btn
                flat
                :label="t('export')"
                icon="file_download"
                color="primary"
                @click="exportAttendance"
              /> -->
            </q-toolbar>
          </template>
        </q-table>

        <TablePagination v-model="pagination" @getData="getAttendances" />
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { AttendanceApi, Clock } from '@/api/attendance';
import { Pagination } from '@/types';
import { formatDate } from '@/utils';
import { UserApi } from '@/api/user';

const { t } = useI18n();

const fromDatePopup = ref();
const toDatePopup = ref();

const closeFromDatePopup = () => {
  setTimeout(() => {
    fromDatePopup.value.hide();
  }, 50);
};

const closeToDatePopup = () => {
  setTimeout(() => {
    toDatePopup.value.hide();
  }, 50);
};

const columns = computed(() => [
  {
    name: 'name',
    label: t('name'),
    field: (row: Clock) => row.user?.name,
    align: 'center' as const,
  },
  {
    name: 'type',
    label: t('clockType'),
    field: (row: Clock) => t(row.type),
    align: 'center' as const,
  },
  {
    name: 'clock_time',
    label: t('clockTime'),
    field: (row: Clock) => formatDate(row.clock_time, 'YYYY/MM/DD HH:mm'),
    align: 'center' as const,
  },
]);

interface DateRange {
  from: string;
  to: string;
}

const dateRange = ref<DateRange>({
  from: '',
  to: '',
});

const userUUID = ref('');
const userOptions = computed(() => [
  {
    label: t('user.all'),
    value: '',
  },
  ...users.value,
]);

// 計算日期限制函數
const fromDateOptions = (date: string) => {
  // 確保開始日期不能選擇比結束日期更晚的日期
  if (!dateRange.value.to) return true;
  const selectedDate = new Date(date);
  const endDate = new Date(dateRange.value.to);
  return selectedDate <= endDate;
};

const toDateOptions = (date: string) => {
  // 確保結束日期不能選擇比開始日期更早的日期
  if (!dateRange.value.from) return true;
  const selectedDate = new Date(date);
  const startDate = new Date(dateRange.value.from);
  return selectedDate >= startDate;
};

// 監聽日期變化，自動調整不合理的日期
watch(
  () => dateRange.value.from,
  (newValue) => {
    if (newValue && dateRange.value.to) {
      const fromDate = new Date(newValue);
      const toDate = new Date(dateRange.value.to);
      if (fromDate > toDate) {
        dateRange.value.to = newValue;
      }
    }
  }
);

watch(
  () => dateRange.value.to,
  (newValue) => {
    if (newValue && dateRange.value.from) {
      const fromDate = new Date(dateRange.value.from);
      const toDate = new Date(newValue);
      if (toDate < fromDate) {
        dateRange.value.from = newValue;
      }
    }
  }
);
// 初始化日期範圍為當前月份
const initDateRange = () => {
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  dateRange.value = {
    from: formatDate(firstDay, 'YYYY/MM/DD'),
    to: formatDate(lastDay, 'YYYY/MM/DD'),
  };
};

const items = ref<Clock[]>([]);

const pagination = ref<Pagination>({
  sortBy: 'clock_time',
  descending: true,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

const isLoading = ref(false);
const getAttendances = async () => {
  try {
    isLoading.value = true;

    const response = await AttendanceApi.fetch({
      filter: {
        user_uuid: userUUID.value,
        start_date: dateRange.value.from,
        end_date: dateRange.value.to,
      },
      pagination: pagination.value,
    });

    items.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const users = ref<
  {
    label: string;
    value: string;
  }[]
>([]);
const getUsers = async () => {
  try {
    isLoading.value = true;

    const response = await UserApi.fetch();

    users.value = response.result.map((user) => ({
      label: user.name,
      value: user.uuid,
    }));
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  initDateRange();
  getAttendances();
  getUsers();
});

const exportAttendance = async () => {
  console.log('Exporting attendance data...');
};
</script>
