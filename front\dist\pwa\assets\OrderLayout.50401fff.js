import{d as ae,r as i,o as ke,w as Fe,z as we,p as r,k as U,j as e,t as c,B as h,A as m,C as n,y as F,aV as W,v as b,m as t,q as Q,x as E,F as ne,u as Ie,aJ as Pe,c as G,G as Y,H as te,E as me,Q as ce,K as B,n as Ce,aK as Ye,b2 as Ue,D as Ve,l as Ae,b3 as Me,aI as Le,I as Te,b4 as ie}from"./index.09f89dc4.js";import{a as x,Q as S}from"./QItemSection.3e7b5a38.js";import{Q as T}from"./QItemLabel.88180eb1.js";import{Q as pe}from"./QList.5d1c2d4f.js";import{Q as Qe}from"./QMenu.531c6599.js";import{Q as Be}from"./QDrawer.e9e5d86a.js";import{Q as Oe,a as Re}from"./QLayout.3422dc25.js";import{C as K}from"./ClosePopup.712518f2.js";import{u as J}from"./vue-i18n.1783a0cb.js";import{Q as xe,a as fe,b as Se,c as ve}from"./QTabPanels.d6390bc2.js";import{Q as qe}from"./QSpace.2ea7fb32.js";import{Q as $e}from"./QScrollArea.e7fd209f.js";import{A as de}from"./attendance.6666db33.js";import{f as N}from"./date.6d29930c.js";import{_ as He}from"./plugin-vue_export-helper.21dcd24c.js";import{Q as ee}from"./QTd.00e5e315.js";import{Q as he}from"./QTable.5ba31f17.js";import{Q as je}from"./QDate.f6067d5f.js";import{Q as ze}from"./QPopupProxy.f63a65d9.js";import{Q as ge}from"./QSelect.958fa87e.js";import{Q as Ne}from"./QTooltip.6fa09534.js";import{Q as Ee,f as Ge}from"./QTr.2cbfa351.js";import{C as Ke}from"./customer.e2880270.js";import{P as We}from"./product.f0d93c26.js";import{_ as Je}from"./BarcodeScannerWrapper.6b8ce7f3.js";import{P as Xe,R as Ze,S as et,a as tt,I as De}from"./inventory.655e9b3c.js";import{u as at}from"./dialog.27403fe4.js";import"./selection.2acb415c.js";import"./TouchPan.818d9316.js";import"./format.054b8074.js";import"./QScrollObserver.942d75c7.js";import"./use-fullscreen.2a1ec9b4.js";import"./use-quasar.3b603a60.js";const lt={key:0,class:"punch-form column full-height q-px-md q-pt-lg"},ot={class:"col-1"},nt={class:"row"},st={class:"col text-h6"},ut={class:"col-11"},rt={class:"col-1"},it={class:"row"},ct={class:"col text-h6 q-py-md"},dt={class:"col-6 text-center q-pt-lg"},mt={class:"row"},pt={class:"col"},ft={class:"d-block"},vt={class:"text-subtitle1"},_t={class:"col"},yt={class:"d-block"},bt={class:"text-subtitle1"},gt=ae({__name:"PunchForm",props:{userInfo:{}},emits:["change-user"],setup(H,{emit:A}){const{t:l}=J(),s=H,_=A,g=i(!1),k=async()=>{try{g.value=!0,await de.punch({user_uuid:s.userInfo.uuid,type:"clock_in"}),C()}finally{g.value=!1}},d=async()=>{try{g.value=!0,await de.punch({user_uuid:s.userInfo.uuid,type:"clock_out"}),C()}finally{g.value=!1}},y=i(),C=async()=>{try{g.value=!0;const D=await de.getLatest(s.userInfo.uuid);y.value=D.result}finally{g.value=!1}};ke(()=>{C()}),Fe(()=>s.userInfo,()=>{C()});const w=i(!1),q=()=>{w.value=!0},$=D=>{_("change-user",D)};return(D,P)=>{var M;const O=we("UserVerifyForm");return r(),U(ne,null,[e(O,{modelValue:w.value,"onUpdate:modelValue":P[0]||(P[0]=V=>w.value=V),onLogin:$},null,8,["modelValue"]),w.value?E("",!0):(r(),U("div",lt,[c("div",ot,[c("div",nt,[c("div",st,[h(m(D.userInfo.name)+" ",1),e(F,{type:"button",onClick:q,label:n(l)("changeUser"),icon:"switch_account",color:"primary",dense:"","no-caps":"",class:"q-ml-md"},null,8,["label"]),e(W,{class:"q-mt-md q-mb-lg"})])])]),c("div",ut,[c("div",rt,[c("div",it,[c("div",ct,[(M=y.value)!=null&&M.clock_time?(r(),b(S,{key:0},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(n(l)("latestPunch"))+"\uFF1A ",1)]),_:1})]),_:1}),e(x,null,{default:t(()=>[h(m(n(N)(y.value.clock_time,"YYYY-MM-DD HH:mm:ss")),1)]),_:1}),e(x,{side:""},{default:t(()=>[y.value.type==="clock_in"?(r(),b(Q,{key:0,name:"arrow_upward",color:"positive"})):(r(),b(Q,{key:1,name:"arrow_downward",color:"negative"}))]),_:1})]),_:1})):(r(),b(S,{key:1},{default:t(()=>[e(x,null,{default:t(()=>[h(m(n(l)("error.noData")),1)]),_:1})]),_:1}))])])]),c("div",dt,[c("div",mt,[c("div",pt,[e(F,{round:"",onClick:k,class:"punch",color:"green",loading:g.value},{default:t(()=>[c("div",ft,[e(Q,{size:"xl",name:"arrow_upward"}),c("div",vt,m(n(l)("clockIn")),1)])]),_:1},8,["loading"])]),c("div",_t,[e(F,{round:"",onClick:d,class:"punch",color:"red",loading:g.value},{default:t(()=>[c("div",yt,[e(Q,{size:"xl",name:"arrow_downward"}),c("div",bt,m(n(l)("clockOut")),1)])]),_:1},8,["loading"])])])])])]))],64)}}});var ht=He(gt,[["__scopeId","data-v-ed6cb724"]]);const kt={class:"punch-history-list q-py-md"},wt=ae({__name:"PunchHistoryList",props:{userInfo:{}},setup(H){const{t:A}=J(),l=H,s=i(!1),_=i([]),g=async()=>{try{s.value=!0;const k=await de.fetch({filter:{user_uuid:l.userInfo.uuid,start_date:N(new Date().setDate(new Date().getDate()-7),"YYYY-MM-DD")}});_.value=k.result.data}finally{s.value=!1}};return ke(()=>{g()}),(k,d)=>(r(),U("div",kt,[e(pe,{bordered:"",separator:""},{default:t(()=>[_.value.length===0?(r(),b(S,{key:0},{default:t(()=>[e(x,null,{default:t(()=>[h(m(n(A)("error.noData")),1)]),_:1})]),_:1})):E("",!0),(r(!0),U(ne,null,Ie(_.value,y=>(r(),b(S,{key:y.uuid},{default:t(()=>[e(x,null,{default:t(()=>[h(m(y.user.name),1)]),_:2},1024),e(x,null,{default:t(()=>[h(m(n(N)(y.clock_time,"YYYY-MM-DD HH:mm")),1)]),_:2},1024),e(x,{side:""},{default:t(()=>[y.type==="clock_in"?(r(),b(Q,{key:0,name:"arrow_upward",color:"positive"})):(r(),b(Q,{key:1,name:"arrow_downward",color:"negative"}))]),_:2},1024)]),_:2},1024))),128))]),_:1})]))}}),qt={class:"row q-mb-sm"},Vt=ae({__name:"PunchDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(H,{emit:A}){const{t:l}=J(),s=H,_=A,g=Pe(),k=i(g.getUserInfo),d=G({get:()=>s.modelValue,set:w=>_("update:modelValue",w)}),y=i("punch"),C=w=>{k.value=w};return(w,q)=>(r(),b(me,{modelValue:d.value,"onUpdate:modelValue":q[3]||(q[3]=$=>d.value=$),persistent:"","no-refocus":"",class:"card-dialog"},{default:t(()=>[e(te,{class:"column"},{default:t(()=>[e(Y,{class:"col-1 q-py-sm"},{default:t(()=>[c("div",qt,[e(xe,{modelValue:y.value,"onUpdate:modelValue":q[0]||(q[0]=$=>y.value=$),dense:"",class:"text-grey","active-color":"primary","indicator-color":"primary",align:"justify","narrow-indicator":""},{default:t(()=>[e(fe,{name:"punch",label:n(l)("punch")},null,8,["label"]),e(fe,{name:"history",label:n(l)("history")},null,8,["label"])]),_:1},8,["modelValue"]),e(qe),e(F,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:q[1]||(q[1]=$=>_("update:modelValue",!1))})])]),_:1}),e(Y,{class:"col-11"},{default:t(()=>[e(Se,{modelValue:y.value,"onUpdate:modelValue":q[2]||(q[2]=$=>y.value=$),class:"full-height"},{default:t(()=>[e(ve,{name:"punch"},{default:t(()=>[e(ht,{userInfo:k.value,onChangeUser:C},null,8,["userInfo"])]),_:1}),e(ve,{name:"history"},{default:t(()=>[e($e,{class:"full-height q-px-md"},{default:t(()=>[e(wt,{userInfo:k.value},null,8,["userInfo"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}}),Dt={class:"row"},Pt={class:"text-h6"},Ct={class:"row q-col-gutter-md"},Qt={class:"col-12 col-md-3"},xt={class:"row items-center justify-end"},St={key:0,class:"col-12 col-md-3"},$t={key:1,class:"col-12 col-md-3"},Ft={class:"col-12"},It={class:"row items-center justify-between q-mb-md"},Yt={class:"text-h6"},Ut={class:"row"},At={class:"summary-section q-mt-md"},Mt={class:"row justify-end"},Lt={class:"col-12 col-md-4"},Tt={class:"summary-card"},Bt={class:"row q-mb-sm"},Ot={class:"col-6 text-right q-pr-md"},Rt={class:"col-6 text-right total-quantity"},Ht={class:"row justify-end"},jt={class:"text-h6"},zt=ae({__name:"BaseInventoryForm",props:{modelValue:{},columns:{},onSubmit:{type:Function}},emits:["update:modelValue"],setup(H,{expose:A,emit:l}){const{t:s}=J(),_=H,g=i(!1),k=l,d=G({get:()=>_.modelValue,set:u=>k("update:modelValue",u)}),y=G(()=>_.columns),C=G(()=>[{value:"customer_return",name:s("inventory.returnType.customer_return")},{value:"supplier_return",name:s("inventory.returnType.supplier_return")}]),w=i([]),q=i([]),$=async()=>{try{const u=await Ke.fetch({filter:{is_supplier:d.value.is_supplier}});w.value=u.result.data,q.value=u.result.data}catch(u){Ve(u)}},D=(u,p)=>{if(u===""){p(()=>{q.value=[...w.value]});return}p(()=>{const a=u.toLowerCase();q.value=w.value.filter(o=>o.name.toLowerCase().indexOf(a)>-1)})},P=i([]),O=i([]),M=async()=>{const u=await We.listProducts();P.value=u.result.data,O.value=u.result.data},V=i({product:null,quantity:1}),X=i(!1),se=()=>{X.value=!0,ue()},j=()=>{X.value=!1,z.value=!1,ue()},z=i(!1),_e=u=>{se(),z.value=!0,V.value={product:u.product,quantity:u.quantity}},R=u=>{V.value={product:u,quantity:1},Z()},Z=()=>{if(!V.value.product)return;const u=d.value.items.find(p=>{var a;return!!p.product&&p.product.uuid===((a=V.value.product)==null?void 0:a.uuid)});u?z.value?u.quantity=V.value.quantity:u.quantity+=V.value.quantity:d.value.items.push(V.value),j()},ue=()=>{V.value={product:null,quantity:1}},I=(u,p)=>{if(u===""){p(()=>{O.value=[...P.value]});return}p(()=>{const a=u.toLowerCase();O.value=P.value.filter(o=>o.name.toLowerCase().indexOf(a)>-1)})},re=u=>{const p=d.value.items.indexOf(u);p>-1&&d.value.items.splice(p,1)},ye=()=>{g.value=!0,$(),M()},L=()=>{g.value=!1},le=i(!1),oe=async()=>{try{le.value=!0,await _.onSubmit(d.value),Ue.create({type:"positive",message:s("success"),position:"top"})}catch(u){Ve(u)}finally{le.value=!1,L()}},be=G(()=>()=>["stock_in","return"].includes(_.modelValue.type));return A({openDialog:ye}),(u,p)=>(r(),b(me,{modelValue:g.value,"onUpdate:modelValue":p[8]||(p[8]=a=>g.value=a),class:"full-dialog","no-refocus":"",persistent:""},{default:t(()=>[e(te,{class:"column"},{default:t(()=>[e(Y,{class:"col-1"},{default:t(()=>[c("div",Dt,[c("div",Pt,m(d.value.title),1),e(qe),e(F,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:L})])]),_:1}),e(Y,{class:"col-10"},{default:t(()=>[e($e,{class:"full-height"},{default:t(()=>[e(te,{bordered:"",class:"q-mb-md bg-grey-3"},{default:t(()=>[e(Y,null,{default:t(()=>[c("div",Ct,[c("div",Qt,[e(ce,{outlined:"",dense:"",modelValue:d.value.form_date,"onUpdate:modelValue":p[1]||(p[1]=a=>d.value.form_date=a),mask:"date",label:n(s)("date"),"stack-label":"",class:"bg-white"},{append:t(()=>[e(Q,{name:"event",class:"cursor-pointer"},{default:t(()=>[e(ze,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:t(()=>[e(je,{modelValue:d.value.form_date,"onUpdate:modelValue":p[0]||(p[0]=a=>d.value.form_date=a),mask:"YYYY-MM-DD"},{default:t(()=>[c("div",xt,[B(e(F,{label:n(s)("close"),"no-caps":"",flat:""},null,8,["label"]),[[K]])])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue","label"])]),u.modelValue.type==="return"?(r(),U("div",St,[e(ge,{outlined:"",dense:"",modelValue:d.value.return_type,"onUpdate:modelValue":p[2]||(p[2]=a=>d.value.return_type=a),options:C.value,"option-label":"name","option-value":"value",label:n(s)("inventory.returnType.label"),"map-options":"","emit-value":"","stack-label":"",class:"bg-white"},null,8,["modelValue","options","label"])])):E("",!0),be.value()?(r(),U("div",$t,[e(ge,{outlined:"",dense:"",modelValue:d.value.customer,"onUpdate:modelValue":p[3]||(p[3]=a=>d.value.customer=a),options:q.value,"option-label":"name","option-value":"uuid",label:`${n(s)("supplier")}/${n(s)("customer.label")}`,"stack-label":"",placeholder:`(${n(s)("optional")})`,clearable:"","use-input":"","input-debounce":"0",onFilter:D,class:"bg-white"},null,8,["modelValue","options","label","placeholder"])])):E("",!0),c("div",Ft,[e(ce,{outlined:"",dense:"",type:"textarea",modelValue:d.value.notes,"onUpdate:modelValue":p[4]||(p[4]=a=>d.value.notes=a),label:n(s)("note.label"),"stack-label":"",class:"bg-white"},null,8,["modelValue","label"])])])]),_:1})]),_:1}),e(te,{bordered:"",class:"bg-grey-3"},{default:t(()=>[e(Y,null,{default:t(()=>{var a;return[c("div",It,[c("div",Yt,m(n(s)("products")),1),c("div",Ut,[e(Je,{products:P.value,barcodeField:"barcode",onProductFound:R},null,8,["products"]),e(F,{color:"positive",icon:"add",class:"q-pa-sm",onClick:se})])]),e(he,{flat:"",bordered:"",rows:d.value.items,columns:y.value,"table-header-class":"bg-cream","rows-per-page-options":[0],"hide-pagination":""},{body:t(o=>[e(Ee,{clickable:"",props:o,onClick:f=>_e(o.row)},{default:t(()=>[e(ee,{props:o,key:"product_name"},{default:t(()=>[h(m(o.row.product.name),1)]),_:2},1032,["props"]),e(ee,{props:o,key:"barcode"},{default:t(()=>[h(m(o.row.product.barcode),1)]),_:2},1032,["props"]),e(ee,{props:o,key:"category"},{default:t(()=>[h(m(o.row.product.category.name),1)]),_:2},1032,["props"]),e(ee,{props:o,key:"quantity"},{default:t(()=>[h(m(o.row.quantity),1)]),_:2},1032,["props"]),e(ee,{props:o,key:"actions"},{default:t(()=>[e(F,{flat:"",round:"",color:"negative",icon:"delete",onClick:Ce(f=>re(o.row),["stop"])},{default:t(()=>[e(Ne,null,{default:t(()=>[h(m(n(s)("remove")),1)]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["props"])]),_:2},1032,["props","onClick"])]),_:1},8,["rows","columns"]),c("div",At,[c("div",Mt,[c("div",Lt,[c("div",Tt,[c("div",Bt,[c("div",Ot,m(n(s)("itemNum"))+"\uFF1A ",1),c("div",Rt,m(((a=d.value.items)==null?void 0:a.length)||0),1)])])])])])]}),_:1})]),_:1})]),_:1})]),_:1}),e(Y,{class:"col-1 bg-grey-2"},{default:t(()=>[c("div",Ht,[e(F,{color:"positive",label:n(s)("save"),onClick:oe,loading:le.value},null,8,["label","loading"])])]),_:1})]),_:1}),e(me,{modelValue:X.value,"onUpdate:modelValue":p[7]||(p[7]=a=>X.value=a),"no-refocus":""},{default:t(()=>[e(te,{style:{"min-width":"350px"}},{default:t(()=>[e(Y,null,{default:t(()=>[c("div",jt,[z.value?(r(),U(ne,{key:0},[h(m(n(s)("editProduct")),1)],64)):(r(),U(ne,{key:1},[h(m(n(s)("addProduct")),1)],64))])]),_:1}),e(Y,{class:"q-pt-none"},{default:t(()=>[e(ge,{outlined:"",dense:"",modelValue:V.value.product,"onUpdate:modelValue":p[5]||(p[5]=a=>V.value.product=a),options:O.value,"option-label":"name","option-value":"uuid",label:n(s)("product.label"),"use-input":"","hide-selected":"","fill-input":"","input-debounce":"0",onFilter:I,rules:[a=>!!a||n(s)("error.required")]},null,8,["modelValue","options","label","rules"]),e(ce,{outlined:"",dense:"",type:"number",modelValue:V.value.quantity,"onUpdate:modelValue":p[6]||(p[6]=a=>V.value.quantity=a),modelModifiers:{number:!0},label:n(s)("quantity"),class:"q-mt-md",rules:[a=>a>0||n(s)("error.minNumber",{min:1})]},null,8,["modelValue","label","rules"])]),_:1}),e(Ye,{align:"right"},{default:t(()=>[e(F,{dense:"",type:"button",label:n(s)("cancel"),color:"grey","no-caps":"",onClick:j},null,8,["label"]),e(F,{dense:"",label:z.value?n(s)("save"):n(s)("add"),"no-caps":"",color:"positive",onClick:Z},null,8,["label"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]))}}),Nt={class:"row q-pt-sm"},Et={class:"row q-gutter-sm q-mb-sm"},Gt={key:0,class:"text-positive text-h6"},Kt={key:1,class:"text-warning text-h6"},Wt={key:2,class:"text-negative text-h6"},Jt=ae({__name:"InventoryDialog",setup(H,{expose:A}){const{t:l}=J(),s=i(!1),_=i("inventory"),g=(a,o)=>{a==="inventory"?I():a==="history"&&u()},k=i(),d=i(a=>Promise.resolve()),y=i([]),C=i({title:"",type:"",uuid:"",no_number:"",form_date:"",order_no:"",customer:null,notes:"",status:"",items:[]}),w=()=>{C.value={title:l("inventory.stock_in.label"),type:"stock_in",uuid:"",no_number:"",form_date:N(new Date,"YYYY-MM-DD"),order_no:"",customer:null,status:"received",notes:"",items:[]},y.value=[{name:"product_name",label:l("product.name"),align:"left",field:"product_name",sortable:!0},{name:"barcode",label:l("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:l("category"),align:"left",field:"category",sortable:!0},{name:"quantity",label:l("quantity"),align:"left",field:"quantity"},{name:"actions",label:l("actions"),field:"actions",align:"center"}],d.value=async a=>{try{await Promise.all([M(a)])}finally{_.value==="inventory"?I():_.value==="history"&&u()}},k.value.openDialog()},q=()=>{C.value={title:l("inventory.return.label"),type:"return",uuid:"",no_number:"",form_date:N(new Date,"YYYY-MM-DD"),order_no:"",return_type:"customer_return",customer:null,status:"return",notes:"",items:[]},y.value=[{name:"product_name",label:l("product.name"),align:"left",field:"product_name",sortable:!0},{name:"barcode",label:l("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:l("category"),align:"left",field:"category",sortable:!0},{name:"quantity",label:l("quantity"),align:"left",field:"quantity"},{name:"actions",label:l("actions"),field:"actions",align:"center"}],d.value=async a=>{try{await Promise.all([V(a)])}finally{_.value==="inventory"?I():_.value==="history"&&u()}},k.value.openDialog()},$=()=>{C.value={title:l("inventory.scrap.label"),type:"scrap",uuid:"",no_number:"",form_date:N(new Date,"YYYY-MM-DD"),order_no:"",customer:null,status:"scrap",notes:"",items:[]},y.value=[{name:"product_name",label:l("product.name"),align:"left",field:"product_name",sortable:!0},{name:"barcode",label:l("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:l("category"),align:"left",field:"category",sortable:!0},{name:"quantity",label:l("quantity"),align:"left",field:"quantity"},{name:"actions",label:l("actions"),field:"actions",align:"center"}],d.value=async a=>{try{await Promise.all([X(a)])}finally{_.value==="inventory"?I():_.value==="history"&&u()}},k.value.openDialog()},D=()=>{C.value={title:l("inventory.stocktaking.label"),type:"stocktaking",uuid:"",no_number:"",form_date:N(new Date,"YYYY-MM-DD"),order_no:"",customer:null,status:"stocktaking",notes:"",items:[]},y.value=[{name:"product_name",label:l("product.name"),align:"left",field:"product_name",sortable:!0},{name:"barcode",label:l("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:l("category"),align:"left",field:"category",sortable:!0},{name:"quantity",label:l("quantity"),align:"left",field:"quantity"},{name:"actions",label:l("actions"),field:"actions",align:"center"}],d.value=async a=>{try{await Promise.all([se(a)])}finally{_.value==="inventory"?I():_.value==="history"&&u()}},k.value.openDialog()},P=()=>{s.value=!0,I()},O=()=>{s.value=!1},M=async a=>{if(a.items.length===0)return Promise.reject();const o=i({po_number:a.no_number,customer:a.customer,order_date:a.form_date,status:"received",notes:a.notes,items:[]});for(const f of a.items){if(!f.product)continue;let v=parseFloat(Ge(f.product.price*f.quantity,2));o.value.items.push({product:f.product,quantity_ordered:f.quantity,quantity_received:f.quantity,unit_price:f.product.price,total_price:v})}return Xe.createPurchaseOrder(o.value)},V=async a=>{if(a.items.length===0||!a.return_type)return Promise.reject();const o=i({uuid:"",return_type:a.return_type,return_number:a.no_number,order_no:a.order_no,customer:a.customer,return_date:a.form_date,notes:a.notes,items:[]});for(const f of a.items)!f.product||o.value.items.push({uuid:"",product:f.product,quantity:f.quantity,notes:""});return Ze.createReturnOrder(o.value)},X=async a=>{if(a.items.length===0)return Promise.reject();const o=i({uuid:"",scrap_date:a.form_date,notes:a.notes,items:[]});for(const f of a.items)!f.product||o.value.items.push({uuid:"",product:f.product,quantity:f.quantity,notes:""});return et.createScrapStock(o.value)},se=async a=>{if(a.items.length===0)return Promise.reject();const o=i({uuid:"",count_number:a.no_number,count_date:a.form_date,notes:a.notes,items:[]});for(const f of a.items)!f.product||o.value.items.push({uuid:"",product:f.product,actual_quantity:f.quantity,notes:""});return tt.createStocktaking(o.value)},j=i({search:""}),z=i([]),_e=G(()=>[{name:"name",label:l("product.name"),align:"left",field:"name",sortable:!0},{name:"barcode",label:l("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:l("category"),align:"left",field:"category",sortable:!0},{name:"stock_quantity",label:l("stockQuantity"),align:"left",field:"stock_qty",sortable:!0},{name:"unit",label:l("unit"),align:"left",field:"unit"}]),R=i({sortBy:"diff_stock_qty",descending:!1,page:1,rowsPerPage:20,rowsNumber:0}),Z=i(!1),ue=a=>{if(!a)return;const o=a,{sortBy:f,descending:v}=o.pagination;f!=""&&(R.value.sortBy=f,R.value.descending=v),I()},I=async()=>{try{Z.value=!0;const a=await De.listProductStocks({filter:j.value,pagination:R.value});z.value=a.result.data,R.value=a.result.pagination}finally{Z.value=!1}},re=i([]),ye=G(()=>[{name:"created_at",label:l("dateAt"),align:"left",field:a=>N(a.created_at,"YYYY-MM-DD HH:mm"),sortable:!0},{name:"product_name",label:l("product.name"),align:"left",field:a=>a.product.name,sortable:!0},{name:"barcode",label:l("barcode"),align:"left",field:a=>a.product.barcode,sortable:!0},{name:"category",label:l("category"),align:"left",field:a=>a.product.category.name,sortable:!0},{name:"before_quantity",label:l("inventory.beforeQuantity"),align:"left",field:a=>a.before_quantity,sortable:!0},{name:"after_quantity",label:l("inventory.afterQuantity"),align:"left",field:a=>a.after_quantity,sortable:!0},{name:"quantity",label:l("inventory.diffQuantity"),align:"left",field:a=>a.after_quantity-a.before_quantity,sortable:!0},{name:"unit",label:l("unit"),align:"left",field:a=>a.product.unit,sortable:!0},{name:"transaction_type",label:l("type"),align:"left",field:a=>l(`inventory.transaction.${a.transaction_type}`)}]),L=i({sortBy:"created_at",descending:!0,page:1,rowsPerPage:20,rowsNumber:0}),le=i(),oe=i(!1),be=a=>{if(!a)return;const o=a,{sortBy:f,descending:v}=o.pagination;f!=""&&(L.value.sortBy=f,L.value.descending=v),u()},u=async()=>{try{oe.value=!0;const a=await De.listTransactions({filter:le.value,pagination:L.value});re.value=a.result.data,L.value=a.result.pagination}finally{oe.value=!1}},p=()=>{j.value.search="",I()};return A({openDialog:P}),(a,o)=>{const f=we("TablePagination");return r(),b(me,{modelValue:s.value,"onUpdate:modelValue":o[8]||(o[8]=v=>s.value=v),class:"full-dialog","no-refocus":"",persistent:""},{default:t(()=>[e(te,{class:"column"},{default:t(()=>[e(Y,{class:"col-1 q-py-none"},{default:t(()=>[c("div",Nt,[e(xe,{modelValue:_.value,"onUpdate:modelValue":o[0]||(o[0]=v=>_.value=v),"active-color":"primary","narrow-indicator":""},{default:t(()=>[e(fe,{name:"inventory",label:n(l)("inventory.label"),"no-caps":""},null,8,["label"]),e(fe,{name:"history",label:n(l)("inventory.stockHistory"),"no-caps":""},null,8,["label"])]),_:1},8,["modelValue"]),e(qe),e(F,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:O})])]),_:1}),e(Se,{modelValue:_.value,"onUpdate:modelValue":o[6]||(o[6]=v=>_.value=v),class:"col-11",onBeforeTransition:g},{default:t(()=>[e(ve,{name:"inventory"},{default:t(()=>[e(Y,{class:"q-pa-none full-height"},{default:t(()=>[e(he,{rows:z.value,columns:_e.value,"row-key":"uuid","virtual-scroll":"",pagination:R.value,"onUpdate:pagination":o[3]||(o[3]=v=>R.value=v),"hide-pagination":"","binary-state-sort":"",class:"q-pa-sm full-height","table-header-class":"bg-grey-2",onRequest:ue,loading:Z.value},{top:t(()=>[c("div",Et,[e(F,{dense:"",icon:"add",class:"bg-positive text-white q-pa-sm q-mr-md"},{default:t(()=>[e(Qe,{anchor:"bottom right",self:"top right",offset:[0,10]},{default:t(()=>[e(pe,{style:{"min-width":"200px"}},{default:t(()=>[B((r(),b(S,{clickable:"",onClick:w},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(n(l)("inventory.stock_in.label")),1)]),_:1}),e(T,{caption:""},{default:t(()=>[h(m(n(l)("inventory.stock_in.caption")),1)]),_:1})]),_:1})]),_:1})),[[K]]),e(W),B((r(),b(S,{clickable:"",onClick:q},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(n(l)("inventory.return.label")),1)]),_:1}),e(T,{caption:""},{default:t(()=>[h(m(n(l)("inventory.return.caption")),1)]),_:1})]),_:1})]),_:1})),[[K]]),e(W),B((r(),b(S,{clickable:"",onClick:$},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(n(l)("inventory.scrap.label")),1)]),_:1}),e(T,{caption:""},{default:t(()=>[h(m(n(l)("inventory.scrap.caption")),1)]),_:1})]),_:1})]),_:1})),[[K]]),e(W),B((r(),b(S,{clickable:"",onClick:D},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(n(l)("inventory.stocktaking.label")),1)]),_:1}),e(T,{caption:""},{default:t(()=>[h(m(n(l)("inventory.stocktaking.caption")),1)]),_:1})]),_:1})]),_:1})),[[K]])]),_:1})]),_:1})]),_:1}),e(ce,{filled:"",dense:"",modelValue:j.value.search,"onUpdate:modelValue":[o[1]||(o[1]=v=>j.value.search=v),I],debounce:300,label:"Search",class:"q-pb-md-sm"},Ae({prepend:t(()=>[e(Q,{name:"search",class:"cursor-pointer"})]),_:2},[j.value.search?{name:"append",fn:t(()=>[e(Q,{name:"close",class:"cursor-pointer",onClick:Ce(p,["stop"])})]),key:"0"}:void 0]),1032,["modelValue"])])]),bottom:t(()=>[e(f,{modelValue:R.value,"onUpdate:modelValue":o[2]||(o[2]=v=>R.value=v),onGetData:I},null,8,["modelValue"])]),"body-cell-stock_quantity":t(v=>[e(ee,{props:v},{default:t(()=>[v.row.diff_stock_qty>5?(r(),U("span",Gt,m(v.row.stock_qty),1)):v.row.diff_stock_qty>0?(r(),U("span",Kt,m(v.row.stock_qty),1)):(r(),U("span",Wt,m(v.row.stock_qty),1))]),_:2},1032,["props"])]),_:1},8,["rows","columns","pagination","loading"])]),_:1})]),_:1}),e(ve,{name:"history"},{default:t(()=>[e(Y,{class:"q-pa-none full-height"},{default:t(()=>[e(he,{rows:re.value,columns:ye.value,"row-key":"uuid","virtual-scroll":"",pagination:L.value,"onUpdate:pagination":o[5]||(o[5]=v=>L.value=v),"hide-pagination":"","binary-state-sort":"",class:"q-pa-sm full-height","table-header-class":"bg-grey-2",loading:oe.value,onRequest:be},{bottom:t(()=>[e(f,{modelValue:L.value,"onUpdate:modelValue":o[4]||(o[4]=v=>L.value=v),onGetData:u},null,8,["modelValue"])]),_:1},8,["rows","columns","pagination","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(zt,{ref_key:"inventoryFormRef",ref:k,modelValue:C.value,"onUpdate:modelValue":o[7]||(o[7]=v=>C.value=v),columns:y.value,onSubmit:d.value},null,8,["modelValue","columns","onSubmit"])]),_:1},8,["modelValue"])}}});const $a=ae({__name:"OrderLayout",setup(H){const A=Me(),l=Le(),s=at(),{t:_}=J(),{locale:g}=J(),k=D=>{g.value=D,localStorage.setItem("user-locale",D)};ke(()=>{const D=localStorage.getItem("user-locale");D&&(g.value=D)});const d=Pe(),y=i(),C=G(()=>!A.params.orderID),w=i(!1),q=()=>{w.value=!0},$=()=>{s.showMessage({message:_("logoutConfirm"),timeout:0,ok:async()=>{await Te.logout(),d.logout(),l.push("/login")}})};return(D,P)=>{const O=we("router-view");return r(),b(Re,{view:"hHh LpR fFf"},{default:t(()=>[C.value?(r(),b(Be,{key:0,"show-if-above":"",width:60,breakpoint:300,"no-swipe-backdrop":"","no-swipe-open":"","no-swipe-close":"",bordered:"",style:{"background-color":"#789dbc",color:"#f8fafc"}},{default:t(()=>[e(pe,{class:"q-list-bottom"},{default:t(()=>{var M;return[B((r(),b(S,{clickable:"",onClick:q},{default:t(()=>[e(Q,{name:"punch_clock",size:"sm"})]),_:1})),[[ie]]),B((r(),b(S,{clickable:"",onClick:(M=y.value)==null?void 0:M.openDialog},{default:t(()=>[e(Q,{name:"inventory",size:"sm"})]),_:1},8,["onClick"])),[[ie]]),e(W),B((r(),b(S,{clickable:""},{default:t(()=>[e(Q,{name:"language",size:"sm"}),e(Qe,{anchor:"top right"},{default:t(()=>[e(pe,{style:{"min-width":"100px"}},{default:t(()=>[B((r(),b(S,{clickable:"",onClick:P[0]||(P[0]=V=>k("en-US"))},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(D.$t("app.english")),1)]),_:1})]),_:1}),n(g)==="en-US"?(r(),b(x,{key:0,avatar:""},{default:t(()=>[e(Q,{name:"check",color:"primary"})]),_:1})):E("",!0)]),_:1})),[[K]]),B((r(),b(S,{clickable:"",onClick:P[1]||(P[1]=V=>k("zh-TW"))},{default:t(()=>[e(x,null,{default:t(()=>[e(T,null,{default:t(()=>[h(m(D.$t("app.chinese")),1)]),_:1})]),_:1}),n(g)==="zh-TW"?(r(),b(x,{key:0,avatar:""},{default:t(()=>[e(Q,{name:"check",color:"primary"})]),_:1})):E("",!0)]),_:1})),[[K]])]),_:1})]),_:1})]),_:1})),[[ie]]),e(W),n(d).isAdmin()?(r(),U(ne,{key:0},[e(S,{clickable:"",onClick:P[2]||(P[2]=V=>n(l).push("/admin/dashboard"))},{default:t(()=>[e(Q,{name:"manage_accounts",size:"sm"})]),_:1}),e(W)],64)):E("",!0),B((r(),b(S,{clickable:"",onClick:$},{default:t(()=>[e(Q,{name:"logout",size:"sm"})]),_:1})),[[ie]])]}),_:1})]),_:1})):E("",!0),e(Oe,null,{default:t(()=>[e(O),e(Vt,{modelValue:w.value,"onUpdate:modelValue":P[3]||(P[3]=M=>w.value=M)},null,8,["modelValue"]),e(Jt,{ref_key:"inventoryDialog",ref:y},null,512)]),_:1})]),_:1})}}});export{$a as default};
