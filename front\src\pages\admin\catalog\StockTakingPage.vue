<template>
  <div class="stock-taking-page">
    <div class="page-header">
      <h1 class="text-h4 q-mb-md">庫存盤點</h1>

      <!-- 基本資訊卡片 -->
      <q-card class="basic-info-card">
        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- 盤點單號 -->
            <div class="col-12 col-md-3">
              <q-input
                v-model="takingInfo.takingNumber"
                label="盤點單號"
                readonly
                outlined
                dense
              />
            </div>

            <!-- 盤點日期 -->
            <div class="col-12 col-md-3">
              <q-input
                v-model="takingInfo.date"
                label="盤點日期"
                type="date"
                outlined
                dense
              />
            </div>

            <!-- 盤點人員 -->
            <div class="col-12 col-md-3">
              <q-input
                v-model="takingInfo.counter"
                label="盤點人員"
                outlined
                dense
              />
            </div>

            <!-- 盤點狀態 -->
            <div class="col-12 col-md-3">
              <q-select
                v-model="takingInfo.status"
                :options="statusOptions"
                label="盤點狀態"
                outlined
                dense
              />
            </div>

            <!-- 備註 -->
            <div class="col-12">
              <q-input
                v-model="takingInfo.notes"
                label="備註說明"
                type="textarea"
                outlined
                dense
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 商品列表卡片 -->
      <q-card class="products-card">
        <q-card-section>
          <div class="row items-center justify-between q-mb-md">
            <div class="text-h6">盤點商品</div>
            <div class="action-buttons">
              <q-btn
                color="primary"
                icon="add"
                label="新增商品"
                @click="openAddProductDialog"
              />
              <q-btn
                color="secondary"
                icon="qr_code_scanner"
                label="掃描條碼"
                @click="scanBarcode"
              />
            </div>
          </div>

          <!-- 商品表格 -->
          <q-table
            :rows="takingItems"
            :columns="columns"
            row-key="id"
            class="count-table"
            flat
            bordered
          >
            <template #body-cell-quantity="props">
              <q-td :props="props" class="quantity-cell">
                <div class="system-quantity">
                  系統：{{ props.row.systemQuantity }}
                </div>
                <div class="actual-quantity">
                  <q-input
                    v-model.number="props.row.actualQuantity"
                    type="number"
                    dense
                    outlined
                    style="width: 100px"
                    @update:model-value="updateDifference(props.row)"
                  />
                </div>
                <div
                  :class="[
                    'difference',
                    props.row.difference > 0
                      ? 'positive'
                      : props.row.difference < 0
                      ? 'negative'
                      : '',
                  ]"
                >
                  差異：{{ props.row.difference }}
                </div>
              </q-td>
            </template>
            <template #body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  flat
                  round
                  color="negative"
                  icon="delete"
                  @click="removeItem(props.row.id)"
                >
                  <q-tooltip>移除</q-tooltip>
                </q-btn>
              </q-td>
            </template>
          </q-table>

          <!-- 總計區域 -->
          <div class="summary-section q-mt-md">
            <div class="row justify-end">
              <div class="col-12 col-md-4">
                <div class="summary-card">
                  <div class="row q-mb-sm">
                    <div class="col-6 text-right q-pr-md">盤點總數：</div>
                    <div class="col-6 text-right total-items">
                      {{ totalItems }} 項
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-6 text-right q-pr-md">差異項目：</div>
                    <div class="col-6 text-right discrepancy-items">
                      {{ discrepancyItems }} 項
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 提交按鈕 -->
      <div class="row justify-end q-mt-md">
        <q-btn color="primary" label="提交盤點單" @click="submitTaking" />
      </div>
    </div>

    <!-- 新增商品對話框 -->
    <q-dialog v-model="addProductDialog" no-refocus>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">新增盤點商品</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-select
            v-model="newProduct"
            :options="productOptions"
            label="選擇商品"
            @update:model-value="onProductSelected"
            outlined
            dense
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="確定" color="primary" @click="addProduct" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, reactive } from 'vue';
import { useQuasar } from 'quasar';

interface TakingInfo {
  takingNumber: string;
  date: string;
  counter: string;
  status: string;
  notes: string;
}

interface TakingItem {
  id: string;
  productId: string;
  name: string;
  barcode: string;
  unit: string;
  systemQuantity: number;
  actualQuantity: number;
  difference: number;
}

interface Product {
  id: string;
  name: string;
  barcode: string;
  unit: string;
  currentStock: number;
}

interface SelectOption {
  label: string;
  value: Product;
}

export default defineComponent({
  name: 'StockTakingPage',
  setup() {
    const $q = useQuasar();

    // 盤點單基本資訊
    const takingInfo = reactive<TakingInfo>({
      takingNumber: `STK-${new Date().getTime().toString().slice(-6)}`,
      date: new Date().toISOString().slice(0, 10),
      counter: '',
      status: '草稿',
      notes: '',
    });

    // 盤點狀態選項
    const statusOptions = ['草稿', '進行中', '已完成', '已取消'];

    // 盤點商品列表
    const takingItems = ref<TakingItem[]>([]);

    // 新增商品對話框
    const addProductDialog = ref(false);
    const newProduct = ref<SelectOption | null>(null);

    // 商品選項（模擬數據）
    const productOptions = [
      {
        label: '商品A',
        value: {
          id: 'prod-001',
          name: '商品A',
          barcode: '4710001',
          unit: '個',
          currentStock: 100,
        },
      },
      {
        label: '商品B',
        value: {
          id: 'prod-002',
          name: '商品B',
          barcode: '4710002',
          unit: '個',
          currentStock: 50,
        },
      },
    ] as SelectOption[];

    // 表格列定義
    const columns = [
      {
        name: 'name',
        label: '商品名稱',
        field: 'name',
        align: 'left' as const,
      },
      {
        name: 'barcode',
        label: '條碼',
        field: 'barcode',
        align: 'left' as const,
      },
      { name: 'unit', label: '單位', field: 'unit', align: 'center' as const },
      {
        name: 'quantity',
        label: '數量',
        field: 'quantity',
        align: 'center' as const,
      },
      {
        name: 'actions',
        label: '操作',
        field: 'actions',
        align: 'center' as const,
      },
    ];

    // 計算總項目數
    const totalItems = computed(() => takingItems.value.length);

    // 計算差異項目數
    const discrepancyItems = computed(() => {
      return takingItems.value.filter((item) => item.difference !== 0).length;
    });

    // 打開新增商品對話框
    function openAddProductDialog() {
      newProduct.value = null;
      addProductDialog.value = true;
    }

    // 選擇商品時的處理
    function onProductSelected(selected: SelectOption | null) {
      newProduct.value = selected;
    }

    // 更新差異數量
    function updateDifference(item: TakingItem) {
      item.difference = item.actualQuantity - item.systemQuantity;
    }

    // 新增商品
    function addProduct() {
      const selected = newProduct.value;
      if (!selected) {
        $q.notify({
          type: 'negative',
          message: '請選擇商品',
          icon: 'warning',
        });
        return;
      }

      const newItem: TakingItem = {
        id: `item-${Date.now()}`,
        productId: selected.value.id,
        name: selected.value.name,
        barcode: selected.value.barcode,
        unit: selected.value.unit,
        systemQuantity: selected.value.currentStock,
        actualQuantity: selected.value.currentStock,
        difference: 0,
      };

      takingItems.value.push(newItem);
      addProductDialog.value = false;

      $q.notify({
        type: 'positive',
        message: '商品已新增',
        icon: 'check',
      });
    }

    // 移除商品
    function removeItem(itemId: string) {
      const index = takingItems.value.findIndex((item) => item.id === itemId);
      if (index !== -1) {
        takingItems.value.splice(index, 1);

        $q.notify({
          type: 'info',
          message: '商品已移除',
          icon: 'delete',
        });
      }
    }

    // 掃描條碼
    function scanBarcode() {
      $q.notify({
        type: 'info',
        message: '條碼掃描功能尚未實作',
        icon: 'qr_code_scanner',
      });
    }

    // 提交盤點單
    function submitTaking() {
      if (takingItems.value.length === 0) {
        $q.notify({
          type: 'negative',
          message: '請至少新增一項盤點商品',
          icon: 'warning',
        });
        return;
      }

      if (!takingInfo.counter) {
        $q.notify({
          type: 'negative',
          message: '請填寫盤點人員',
          icon: 'warning',
        });
        return;
      }

      console.log('提交盤點單', {
        info: takingInfo,
        items: takingItems.value,
        summary: {
          totalItems: totalItems.value,
          discrepancyItems: discrepancyItems.value,
        },
      });

      $q.notify({
        type: 'positive',
        message: '盤點單已提交',
        icon: 'check_circle',
      });
    }

    return {
      takingInfo,
      statusOptions,
      columns,
      takingItems,
      addProductDialog,
      newProduct,
      productOptions,
      totalItems,
      discrepancyItems,
      openAddProductDialog,
      onProductSelected,
      updateDifference,
      addProduct,
      removeItem,
      scanBarcode,
      submitTaking,
    };
  },
});
</script>

<style lang="scss">
@import '@/css/stock-taking.scss';
</style>
