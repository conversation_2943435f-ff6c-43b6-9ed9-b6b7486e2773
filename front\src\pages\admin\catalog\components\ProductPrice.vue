<template>
  <span class="product-price">
    <!-- 彈性金額產品 -->
    <div v-if="isFlexiblePrice" class="price-container q-mx-auto">
      <div class="flexible-price text-primary text-weight-bold">
        {{ $t('flexiblePrice') }}
      </div>
    </div>

    <!-- 當有特價時 -->
    <div v-else-if="hasDiscount" class="price-container q-mx-auto">
      <div>
        <!-- 原價（刪除線） -->
        <div class="original-price text-strike text-grey-6">
          AU$ {{ formatNumber(price, 2) }}
        </div>
        <!-- 特價 -->
        <div class="sale-price text-red text-weight-bold">
          AU$ {{ formatNumber(sale_price, 2) }}
        </div>
      </div>
      <!-- 折扣標籤（可選） -->
      <q-chip
        v-if="showDiscountPercent"
        color="red"
        text-color="white"
        size="sm"
        class="discount-chip"
      >
        -{{ discountPercent }}%
      </q-chip>
    </div>

    <!-- 當沒有特價時，只顯示原價 -->
    <div v-else class="price-container q-mx-auto">
      <div class="regular-price">AU$ {{ formatNumber(price, 2) }}</div>
    </div>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { formatNumber } from '@/utils';

const props = defineProps({
  price: {
    type: Number,
    required: true,
  },
  sale_price: {
    type: Number,
    default: 0,
  },
  showDiscountPercent: {
    type: Boolean,
    default: false,
  },
  isFlexiblePrice: {
    type: Boolean,
    default: false,
  },
});

// 計算是否有折扣
const hasDiscount = computed(() => {
  return props.sale_price > 0 && props.sale_price < props.price;
});

// 計算折扣百分比
const discountPercent = computed(() => {
  if (!hasDiscount.value) return 0;
  return Math.round(100 - (props.sale_price / props.price) * 100);
});
</script>

<style scoped>
.product-price {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.discount-chip {
  margin-left: 8px;
}

/* 響應式設計 */
@media (max-width: 600px) {
  .price-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .discount-chip {
    margin-left: 0;
    margin-top: 4px;
  }
}
</style>
