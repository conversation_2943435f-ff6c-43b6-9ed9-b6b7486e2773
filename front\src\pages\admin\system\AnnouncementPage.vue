<template>
  <q-page class="q-pt-md">
    <q-card
      flat
      square
      bordered
      class="q-mx-auto"
      style="width: 800px; max-width: 100%"
    >
      <q-form @submit.prevent="onSubmit" greedy>
        <q-card-section>
          <div class="row q-mb-md">
            <div class="col-12 text-h6 text-bold text-black">
              {{ t('announcement.title') }}
              <q-separator color="black" size="2px" class="q-mb-sm" />
            </div>
          </div>

          <!-- 公告內容 -->
          <div class="row q-mb-md">
            <div class="col-12 text-subtitle1">
              {{ t('announcement.content') }}
            </div>
            <div class="col-12">
              <q-editor
                v-model="announcement.content"
                :toolbar="toolbar"
                min-height="15rem"
                :placeholder="t('announcement.placeholder')"
              />
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-px-lg q-py-md">
          <q-btn
            type="submit"
            :label="t('save')"
            color="positive"
            size="md"
            :loading="isLoading"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Notify, useQuasar } from 'quasar';
import { AnnouncementApi, AnnouncementConfig } from '@/api/announcement';

const { t } = useI18n();
const $q = useQuasar();

const announcement = ref<AnnouncementConfig>({
  content: '',
});

// 只包含文字相關功能的 toolbar
const toolbar = [
  [
    {
      label: $q.lang.editor.formatting,
      list: 'no-icons',
      options: ['p', 'h3', 'h4', 'h5', 'h6'],
    },
  ],
  ['left', 'center', 'right', 'justify'],
  ['bold', 'italic', 'underline', 'strike'],
  ['unordered', 'ordered'],
];

const isLoading = ref(false);

const getData = async () => {
  try {
    isLoading.value = true;
    const response = await AnnouncementApi.get();
    announcement.value = response.result;
  } catch (error) {
    console.error('Failed to load announcement:', error);
  } finally {
    isLoading.value = false;
  }
};

const onSubmit = async () => {
  try {
    isLoading.value = true;
    await AnnouncementApi.update(announcement.value);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } catch (error) {
    console.error('Failed to save announcement:', error);
    Notify.create({
      type: 'negative',
      message: t('failed'),
      position: 'top',
    });
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  getData();
});
</script>
