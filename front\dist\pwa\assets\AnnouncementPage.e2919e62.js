import{bk as d,d as y,r as u,o as x,p as Q,v as q,m as n,j as e,n as w,G as V,t as s,B as k,A as m,C as r,aV as C,aK as A,y as B,H as S,b2 as p}from"./index.09f89dc4.js";import{Q as N}from"./QEditor.dddd89de.js";import{Q as F}from"./QForm.c51f3f04.js";import{Q as j}from"./QPage.ce1b4cb5.js";import{u as z}from"./vue-i18n.1783a0cb.js";import{u as D}from"./use-quasar.3b603a60.js";import"./QMenu.531c6599.js";import"./selection.2acb415c.js";import"./QTooltip.6fa09534.js";import"./QItemSection.3e7b5a38.js";import"./use-fullscreen.2a1ec9b4.js";const f={get:()=>d.get("v1/announcements"),update:i=>d.put("v1/announcements",i)},M={class:"row q-mb-md"},P={class:"col-12 text-h6 text-bold text-black"},E={class:"row q-mb-md"},G={class:"col-12 text-subtitle1"},H={class:"col-12"},Y=y({__name:"AnnouncementPage",setup(i){const{t}=z(),v=D(),l=u({content:""}),h=[[{label:v.lang.editor.formatting,list:"no-icons",options:["p","h3","h4","h5","h6"]}],["left","center","right","justify"],["bold","italic","underline","strike"],["unordered","ordered"]],o=u(!1),g=async()=>{try{o.value=!0;const a=await f.get();l.value=a.result}catch(a){console.error("Failed to load announcement:",a)}finally{o.value=!1}},_=async()=>{try{o.value=!0,await f.update(l.value),p.create({type:"positive",message:t("success"),position:"top"})}catch(a){console.error("Failed to save announcement:",a),p.create({type:"negative",message:t("failed"),position:"top"})}finally{o.value=!1}};return x(()=>{g()}),(a,c)=>(Q(),q(j,{class:"q-pt-md"},{default:n(()=>[e(S,{flat:"",square:"",bordered:"",class:"q-mx-auto",style:{width:"800px","max-width":"100%"}},{default:n(()=>[e(F,{onSubmit:w(_,["prevent"]),greedy:""},{default:n(()=>[e(V,null,{default:n(()=>[s("div",M,[s("div",P,[k(m(r(t)("announcement.title"))+" ",1),e(C,{color:"black",size:"2px",class:"q-mb-sm"})])]),s("div",E,[s("div",G,m(r(t)("announcement.content")),1),s("div",H,[e(N,{modelValue:l.value.content,"onUpdate:modelValue":c[0]||(c[0]=b=>l.value.content=b),toolbar:h,"min-height":"15rem",placeholder:r(t)("announcement.placeholder")},null,8,["modelValue","placeholder"])])])]),_:1}),e(A,{align:"right",class:"q-px-lg q-py-md"},{default:n(()=>[e(B,{type:"submit",label:r(t)("save"),color:"positive",size:"md",loading:o.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1})]),_:1}))}});export{Y as default};
