<template>
  <div class="full-height q-px-md-xl">
    <!-- data table -->
    <q-table
      virtual-scroll
      class="full-height"
      row-key="uuid"
      :rows="products"
      :columns="columns"
      v-model:pagination="pagination"
      hide-pagination
      binary-state-sort
      table-header-class="bg-grey-3"
      @request="onRequest"
      :loading="isLoading"
    >
      <template v-slot:top>
        <!-- add btn -->
        <q-btn
          type="button"
          @click="handleCreate"
          color="create"
          :size="$q.screen.lt.md ? 'sm' : 'md'"
          class="q-pa-sm"
          v-if="categoryID !== 0"
        >
          <q-icon name="add" />
          <template v-if="$q.screen.gt.sm">
            {{ t('product.label') }}
          </template>
        </q-btn>
        <!-- search -->
        <q-input
          v-model="search"
          outlined
          dense
          :placeholder="t('search.product')"
          class="q-ml-md"
          clearable
          clear-icon="close"
          @keyup.enter.prevent="getProducts"
          @update:model-value="getProducts"
          input-debounce="300"
          style="width: 300px"
        >
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:body="props">
        <q-tr clickable @click="handleEdit(props.row)">
          <q-td :props="props" key="is_active">
            <q-toggle
              v-model="props.row.is_active"
              color="positive"
              @update:modelValue="onChangeStatus(props.row)"
            />
          </q-td>
          <q-td :props="props" key="name">
            {{ props.row.name }}
          </q-td>
          <q-td :props="props" key="barcode">
            {{ props.row.barcode }}
          </q-td>
          <q-td :props="props" key="price">
            <ProductPrice
              :price="props.row.price"
              :sale_price="props.row.sale_price"
              :showDiscountPercent="true"
              :isFlexiblePrice="props.row.is_flexible_price"
            />
          </q-td>
          <q-td :props="props" key="cost"> AU$ {{ props.row.cost }} </q-td>
          <q-td :props="props" key="stock_quantity">
            {{ props.row.stock_quantity }}
          </q-td>
        </q-tr>
      </template>
    </q-table>

    <TablePagination v-model="pagination" @getData="getProducts" />
  </div>

  <ProductProfile
    v-model="showProduct"
    :productUUID="productUUID"
    :categoryID="categoryID"
    @refreshData="refreshData"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Notify } from 'quasar';
import { useI18n } from 'vue-i18n';
import { ProductApi, Product } from '@/api/product';
import { TableRequestProps } from '@/types';
import ProductPrice from './ProductPrice.vue';
import ProductProfile from '@/components/ProductProfile.vue';

const props = withDefaults(
  defineProps<{
    categoryID: number;
  }>(),
  {
    categoryID: 0,
  }
);

defineEmits(['close']);

const { t } = useI18n();

const search = ref('');
const isLoading = ref(false);
const products = ref<Product[]>([]);
const productUUID = ref('');
const showProduct = ref(false);
const pagination = ref({
  sortBy: 'name',
  descending: false,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

const columns = computed(() => [
  {
    name: 'is_active',
    label: '',
    field: 'is_active',
    headerStyle: 'width: 50px',
  },
  {
    name: 'name',
    required: true,
    label: t('name'),
    align: 'center' as const,
    field: 'name',
    sortable: true,
  },
  {
    name: 'barcode',
    required: true,
    label: t('barcode'),
    align: 'center' as const,
    field: 'barcode',
    sortable: true,
  },
  {
    name: 'price',
    required: true,
    label: t('price'),
    align: 'center' as const,
    field: 'price',
    sortable: true,
  },
  {
    name: 'cost',
    required: true,
    label: t('cost'),
    align: 'center' as const,
    field: 'cost',
    sortable: true,
  },
  {
    name: 'stock_quantity',
    required: true,
    label: t('stockQuantity'),
    align: 'center' as const,
    field: 'stock_quantity',
    sortable: true,
  },
]);

const handleCreate = () => {
  productUUID.value = '';
  showProduct.value = true;
};

const handleEdit = (product: Product) => {
  productUUID.value = product.uuid;
  showProduct.value = true;
};

const getProducts = async () => {
  try {
    isLoading.value = true;
    const response = await ProductApi.listProducts({
      filter: {
        search: search.value,
        category_id: props.categoryID,
      },
      pagination: pagination.value,
    });

    products.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const onRequest = async (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  getProducts();
};

const onChangeStatus = async (product: Product) => {
  try {
    isLoading.value = true;
    await ProductApi.updateStatus(product.uuid, product.is_active);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } finally {
    getProducts();
  }
};

const refreshData = () => {
  showProduct.value = false;
  getProducts();
};

onMounted(() => {
  showProduct.value = false;
  getProducts();
});

watch(
  () => props.categoryID,
  () => {
    showProduct.value = false;
    getProducts();
  }
);
</script>
