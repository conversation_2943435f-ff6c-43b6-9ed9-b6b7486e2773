<template>
  <div class="barcode-scanner-wrapper">
    <BarcodeScanner
      :products="products"
      :barcode-field="barcodeField"
      @barcode-detected="onBarcodeDetected"
      @product-found="onProductFound"
      @product-not-found="onProductNotFound"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import BarcodeScanner from './BarcodeScanner.vue';
import { Product } from '@/api/product';

const { t } = useI18n();
const $q = useQuasar();

const props = defineProps({
  products: {
    type: Array as () => Product[],
    required: true,
  },
  barcodeField: {
    type: String,
    default: 'barcode',
  },
  autoNotify: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits([
  'barcode-scanned',
  'product-found',
  'product-not-found',
]);

const lastBarcode = ref('');
const onBarcodeDetected = (barcode: string) => {
  lastBarcode.value = barcode;

  emit('barcode-scanned', barcode);
};

const onProductFound = (product: Product) => {
  if (props.autoNotify) {
    $q.notify({
      message: `${product.name} ${t('barcodeScanner.scanCompleted')}`,
      color: 'positive',
      position: 'top',
      timeout: 1500,
    });
  }

  emit('product-found', product);
};

const onProductNotFound = (barcode: string) => {
  if (props.autoNotify) {
    $q.notify({
      message: t('barcodeScanner.notFound', { barcode }),
      color: 'warning',
      position: 'top',
      timeout: 1500,
    });
  }

  emit('product-not-found', barcode);
};
</script>
