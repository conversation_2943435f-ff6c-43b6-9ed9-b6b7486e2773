function p(t,r="YYYY-MM-DD"){if(!t)return"";const e=new Date(t),a=e.getFullYear(),o=e.getMonth()+1,s=e.getDate(),c=e.getHours(),Y=e.getMinutes(),d=e.getSeconds(),n=u=>u.toString().padStart(2,"0");return r.replace("YYYY",a.toString()).replace("MM",n(o)).replace("DD",n(s)).replace("HH",n(c)).replace("mm",n(Y)).replace("ss",n(d))}const D=[t=>!t||t===""?!0:/^(\d{4})\-(\d{1,2})\-(\d{1,2})$/.test(t)||"YYYY-MM-DD"];export{D as d,p as f};
