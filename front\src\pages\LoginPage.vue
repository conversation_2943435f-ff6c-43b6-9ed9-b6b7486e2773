<template>
  <q-page>
    <q-form @submit="onLogin" class="q-py-lg">
      <q-card
        class="q-mx-auto q-py-lg q-px-md"
        style="max-width: min(100%, 28rem)"
      >
        <q-card-section class="q-gutter-md">
          <div class="text-h6">
            {{ t('login') }}
          </div>
          <!-- <q-input
            type="text"
            v-model="storeID"
            label="Store ID"
            outlined
            required
          /> -->
          <q-input
            type="text"
            v-model="username"
            :label="t('account')"
            outlined
            :rules="[(val) => !!val || t('error.required')]"
            lazy-rules
          />
          <q-input
            :type="isPwd ? 'password' : 'text'"
            v-model="password"
            :label="t('password')"
            outlined
            :rules="[(val) => !!val || t('error.required')]"
            lazy-rules
          >
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
              />
            </template>
          </q-input>
        </q-card-section>

        <q-card-actions align="between">
          <q-toggle
            v-model="rememberMe"
            @update="onRememberMe"
            :label="t('rememberMe')"
            size="lg"
            color="toggle"
            dense
            keep-color
          />

          <!-- 忘記密碼 -->
          <!-- <q-btn
            to="/forget-password"
            :label="t('forgetPassword')"
            color="primary"
            dense
            flat
          /> -->
        </q-card-actions>

        <q-card-actions>
          <q-btn
            type="submit"
            rounded
            :loading="isLogin"
            :label="t('login')"
            class="full-width q-mt-sm"
            color="login"
            text-color="login"
            size="lg"
            :ripple="{ center: true }"
          />
        </q-card-actions>
      </q-card>
    </q-form>
  </q-page>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { AuthApi } from '@/api/auth';
import { useAuthStore } from '@/stores/auth-store';
import { handleError } from '@/utils/error-handler';

const { t } = useI18n();

defineOptions({
  name: 'LoginPage',
});

// const storeID = ref('');
const username = ref('');
const password = ref('');
const isPwd = ref(true);
const rememberMe = ref(false);
const isLogin = ref(false);

const router = useRouter();
const authStore = useAuthStore();

// 如果有記住帳號，自動填入
const rememberedAccount = localStorage.getItem('remembered_account');
if (rememberedAccount) {
  username.value = rememberedAccount;
  rememberMe.value = true;
}

const onLogin = async () => {
  isLogin.value = true;

  try {
    const response = await AuthApi.login({
      username: username.value,
      password: password.value,
    });

    // 如果記住帳號，保存到 localStorage
    if (rememberMe.value) {
      localStorage.setItem('remembered_account', username.value);
    } else {
      localStorage.removeItem('remembered_account');
    }

    authStore.login(response.result);
    if (authStore.isAdmin()) {
      router.push('/admin/dashboard/user');
    } else {
      router.push('/order');
    }
  } catch (error) {
    handleError(error);
  } finally {
    isLogin.value = false;
  }
};

const onRememberMe = () => {
  console.log('rememberMe:', rememberMe.value);
};
</script>

<style lang="scss">
.bg-login {
  background-color: #ff9d3d;
}

.text-login {
  color: #fbfbfb;
}
</style>
