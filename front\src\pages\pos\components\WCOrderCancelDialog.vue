<template>
  <q-dialog v-model="visible" persistent no-refocus>
    <q-card style="min-width: 350px">
      <q-card-section>
        <div class="text-h6">{{ $t('wcOrder.cancel.title') }}</div>
      </q-card-section>

      <q-card-section>
        <q-option-group
          v-model="cancelReason"
          :options="cancelReasons"
          type="radio"
        />

        <q-input
          v-if="cancelReason === 'other'"
          v-model.trim="otherReasonText"
          type="textarea"
          :label="$t('wcOrder.cancel.reasonLabel')"
          :placeholder="$t('wcOrder.cancel.reasonPlaceholder')"
          class="q-mt-md"
          outlined
          :rules="[
            (val) => val.length > 0 || $t('wcOrder.cancel.reasonRequired'),
          ]"
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          :label="$t('cancel')"
          color="primary"
          @click="closeDialog"
        />
        <q-btn 
          :label="$t('confirm')" 
          color="negative" 
          @click="confirmCancel"
          :loading="isLoading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Notify } from 'quasar';

const { t } = useI18n();

const props = defineProps({
  orderId: {
    type: Number,
    required: true,
  },
  otherReason: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['order-cancelled']);

const visible = ref(false);
const cancelReason = ref('');
const otherReasonText = ref('');
const isLoading = ref(false);

// 取消原因選項
const cancelReasons = computed(() => [
  {
    label: t('wcOrder.cancel.reasons.outOfStock'),
    value: t('wcOrder.cancel.reasons.outOfStock'),
  },
  {
    label: t('wcOrder.cancel.reasons.customerRequest'),
    value: t('wcOrder.cancel.reasons.customerRequest'),
  },
  {
    label: t('wcOrder.cancel.reasons.paymentIssue'),
    value: t('wcOrder.cancel.reasons.paymentIssue'),
  },
  {
    label: t('wcOrder.cancel.reasons.duplicateOrder'),
    value: t('wcOrder.cancel.reasons.duplicateOrder'),
  },
  {
    label: t('wcOrder.cancel.reasons.incorrectInfo'),
    value: t('wcOrder.cancel.reasons.incorrectInfo'),
  },
  { 
    label: t('wcOrder.cancel.reasons.other'), 
    value: 'other' 
  },
]);

const openDialog = () => {
  visible.value = true;
  cancelReason.value = '';
  otherReasonText.value = props.otherReason || '';

  if (props.otherReason) {
    // 如果有傳入其他原因，則自動選擇"其他"選項
    cancelReason.value = 'other';
  } else {
    // 預設選擇第一個取消原因
    cancelReason.value = cancelReasons.value[0].value;
  }
};

const closeDialog = () => {
  visible.value = false;
  isLoading.value = false;
};

const confirmCancel = () => {
  // 檢查是否選擇了取消原因
  if (!cancelReason.value) {
    Notify.create({
      type: 'warning',
      position: 'top',
      message: t('wcOrder.cancel.reasonRequired'),
    });
    return;
  }

  // 如果選擇了"其他"，檢查是否填寫說明
  if (cancelReason.value === 'other' && otherReasonText.value === '') {
    Notify.create({
      type: 'warning',
      position: 'top',
      message: t('wcOrder.cancel.reasonRequired'),
    });
    return;
  }

  // 準備取消原因資料
  const cancelData = {
    orderId: props.orderId,
    reason: cancelReason.value,
    otherReason: cancelReason.value === 'other' ? otherReasonText.value : null,
  };

  // 觸發取消訂單事件
  emit('order-cancelled', cancelData);

  // 關閉對話框
  closeDialog();
};

// 對外暴露方法
defineExpose({
  openDialog,
});
</script>
