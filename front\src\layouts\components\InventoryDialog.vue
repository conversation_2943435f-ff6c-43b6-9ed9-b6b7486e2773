<template>
  <q-dialog v-model="visible" class="full-dialog" no-refocus persistent>
    <q-card class="column">
      <!-- header -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-pt-sm">
          <!-- title -->
          <!-- <div class="text-h5 text-bold">
            {{ $t('inventory.label') }}
          </div> -->
          <q-tabs v-model="tab" active-color="primary" narrow-indicator>
            <q-tab name="inventory" :label="t('inventory.label')" no-caps />
            <q-tab
              name="history"
              :label="t('inventory.stockHistory')"
              no-caps
            />
          </q-tabs>

          <q-space />

          <!-- close -->
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="closeDialog"
          />
        </div>
      </q-card-section>

      <!-- body -->
      <q-tab-panels
        v-model="tab"
        class="col-11"
        @before-transition="onPanelTransition"
      >
        <!-- inventory -->
        <q-tab-panel name="inventory">
          <q-card-section class="q-pa-none full-height">
            <q-table
              :rows="rows"
              :columns="columns"
              row-key="uuid"
              virtual-scroll
              v-model:pagination="pagination"
              hide-pagination
              binary-state-sort
              class="q-pa-sm full-height"
              table-header-class="bg-grey-2"
              @request="onRequest"
              :loading="isLoading"
            >
              <!-- actions -->
              <template v-slot:top>
                <div class="row q-gutter-sm q-mb-sm">
                  <!-- actions -->
                  <q-btn
                    dense
                    icon="add"
                    class="bg-positive text-white q-pa-sm q-mr-md"
                  >
                    <q-menu
                      anchor="bottom right"
                      self="top right"
                      :offset="[0, 10]"
                    >
                      <q-list style="min-width: 200px">
                        <!-- 進貨 -->
                        <q-item clickable v-close-popup @click="handleStockIn">
                          <q-item-section>
                            <q-item-label>
                              {{ t('inventory.stock_in.label') }}
                            </q-item-label>
                            <q-item-label caption>
                              {{ t('inventory.stock_in.caption') }}
                            </q-item-label>
                          </q-item-section>
                        </q-item>

                        <q-separator />
                        <!-- 退貨 -->
                        <q-item
                          clickable
                          v-close-popup
                          @click="handleStockReturn"
                        >
                          <q-item-section>
                            <q-item-label>
                              {{ t('inventory.return.label') }}
                            </q-item-label>
                            <q-item-label caption>
                              {{ t('inventory.return.caption') }}
                            </q-item-label>
                          </q-item-section>
                        </q-item>

                        <q-separator />

                        <!-- 報廢 -->
                        <q-item
                          clickable
                          v-close-popup
                          @click="handleStockScrap"
                        >
                          <q-item-section>
                            <q-item-label>
                              {{ t('inventory.scrap.label') }}
                            </q-item-label>
                            <q-item-label caption>
                              {{ t('inventory.scrap.caption') }}
                            </q-item-label>
                          </q-item-section>
                        </q-item>

                        <q-separator />

                        <!-- 盤點 -->
                        <q-item
                          clickable
                          v-close-popup
                          @click="handleStocktaking"
                        >
                          <q-item-section>
                            <q-item-label>
                              {{ t('inventory.stocktaking.label') }}
                            </q-item-label>
                            <q-item-label caption>
                              {{ t('inventory.stocktaking.caption') }}
                            </q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>

                  <!-- search -->
                  <q-input
                    filled
                    dense
                    v-model="filter.search"
                    :debounce="300"
                    @update:model-value="getProductStocks"
                    label="Search"
                    class="q-pb-md-sm"
                  >
                    <template v-slot:prepend>
                      <q-icon name="search" class="cursor-pointer" />
                    </template>
                    <!-- clear -->
                    <template v-slot:append v-if="filter.search">
                      <q-icon
                        name="close"
                        class="cursor-pointer"
                        @click.stop="clearSearch"
                      />
                    </template>
                  </q-input>
                </div>
              </template>

              <!-- pagination -->
              <template v-slot:bottom>
                <TablePagination
                  v-model="pagination"
                  @getData="getProductStocks"
                />
              </template>

              <!-- cell -->
              <template v-slot:body-cell-stock_quantity="props">
                <q-td :props="props">
                  <!-- 安全庫存 -->
                  <span
                    class="text-positive text-h6"
                    v-if="props.row.diff_stock_qty > 5"
                  >
                    {{ props.row.stock_qty }}
                  </span>
                  <!-- 接近低庫存 -->
                  <span
                    class="text-warning text-h6"
                    v-else-if="props.row.diff_stock_qty > 0"
                  >
                    {{ props.row.stock_qty }}
                  </span>
                  <!-- 低庫存 -->
                  <span class="text-negative text-h6" v-else>
                    {{ props.row.stock_qty }}
                  </span>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-tab-panel>
        <!-- history -->
        <q-tab-panel name="history">
          <q-card-section class="q-pa-none full-height">
            <q-table
              :rows="historyRows"
              :columns="historyColumns"
              row-key="uuid"
              virtual-scroll
              v-model:pagination="historyPagination"
              hide-pagination
              binary-state-sort
              class="q-pa-sm full-height"
              table-header-class="bg-grey-2"
              :loading="isHistoryLoading"
              @request="onHistoryRequest"
            >
              <!-- pagination -->
              <template v-slot:bottom>
                <TablePagination
                  v-model="historyPagination"
                  @getData="getTransactions"
                />
              </template>
            </q-table>
          </q-card-section>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>

    <BaseInventoryForm
      ref="inventoryFormRef"
      v-model="inventoryForm"
      :columns="inventoryColumns"
      :onSubmit="handleInventoryForm"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import BaseInventoryForm from '@/components/BaseInventoryForm.vue';
import {
  InventoryApi,
  PurchaseOrderApi,
  PurchaseOrder,
  ProductStock,
  ProductStockFilter,
  InventoryTransactionFilter,
  InventoryTransaction,
  ReturnOrder,
  ReturnOrderApi,
  ScrapStockApi,
  ScrapStock,
  Stocktaking,
  StocktakingApi,
} from '@/api/inventory';
import { Pagination, TableRequestProps } from '@/types';
import { InventoryForm } from '@/components/models/inventory';
import { formatDate, formatNumber, handleError } from '@/utils';

const { t } = useI18n();
const visible = ref(false);
const tab = ref('inventory');

const onPanelTransition = (to: string | number, from: string | number) => {
  if (to === 'inventory') {
    getProductStocks();
  } else if (to === 'history') {
    getTransactions();
  }
};

const inventoryFormRef = ref();
const handleInventoryForm = ref<(form: InventoryForm) => void>((form) =>
  Promise.resolve()
);
const inventoryColumns = ref<
  {
    name: string;
    label: string;
    align?: 'left' | 'right' | 'center';
    field: string | ((row: unknown) => unknown);
    sortable?: boolean;
    required?: boolean;
  }[]
>([]);
const inventoryForm = ref<InventoryForm>({
  title: '',
  type: '',
  uuid: '',
  no_number: '',
  form_date: '',
  order_no: '',
  customer: null,
  notes: '',
  status: '',
  items: [],
});

const handleStockIn = () => {
  inventoryForm.value = {
    title: t('inventory.stock_in.label'),
    type: 'stock_in',
    uuid: '',
    no_number: '',
    form_date: formatDate(new Date(), 'YYYY-MM-DD'),
    order_no: '',
    customer: null,
    status: 'received',
    notes: '',
    items: [],
  };

  inventoryColumns.value = [
    {
      name: 'product_name',
      label: t('product.name'),
      align: 'left' as const,
      field: 'product_name',
      sortable: true,
    },
    {
      name: 'barcode',
      label: t('barcode'),
      align: 'left' as const,
      field: 'barcode',
      sortable: true,
    },
    {
      name: 'category',
      label: t('category'),
      align: 'left' as const,
      field: 'category',
      sortable: true,
    },
    {
      name: 'quantity',
      label: t('quantity'),
      align: 'left' as const,
      field: 'quantity',
    },
    {
      name: 'actions',
      label: t('actions'),
      field: 'actions',
      align: 'center' as const,
    },
  ];

  handleInventoryForm.value = async (form: InventoryForm) => {
    try {
      await Promise.all([submitStockInForm(form)]);
    } finally {
      if (tab.value === 'inventory') {
        getProductStocks();
      } else if (tab.value === 'history') {
        getTransactions();
      }
    }
  };

  inventoryFormRef.value.openDialog();
};

const handleStockReturn = () => {
  inventoryForm.value = {
    title: t('inventory.return.label'),
    type: 'return',
    uuid: '',
    no_number: '',
    form_date: formatDate(new Date(), 'YYYY-MM-DD'),
    order_no: '',
    return_type: 'customer_return',
    customer: null,
    status: 'return',
    notes: '',
    items: [],
  };

  inventoryColumns.value = [
    {
      name: 'product_name',
      label: t('product.name'),
      align: 'left' as const,
      field: 'product_name',
      sortable: true,
    },
    {
      name: 'barcode',
      label: t('barcode'),
      align: 'left' as const,
      field: 'barcode',
      sortable: true,
    },
    {
      name: 'category',
      label: t('category'),
      align: 'left' as const,
      field: 'category',
      sortable: true,
    },
    {
      name: 'quantity',
      label: t('quantity'),
      align: 'left' as const,
      field: 'quantity',
    },
    {
      name: 'actions',
      label: t('actions'),
      field: 'actions',
      align: 'center' as const,
    },
  ];

  handleInventoryForm.value = async (form: InventoryForm) => {
    try {
      await Promise.all([submitReturnForm(form)]);
    } finally {
      if (tab.value === 'inventory') {
        getProductStocks();
      } else if (tab.value === 'history') {
        getTransactions();
      }
    }
  };

  inventoryFormRef.value.openDialog();
};

// 報廢
const handleStockScrap = () => {
  inventoryForm.value = {
    title: t('inventory.scrap.label'),
    type: 'scrap',
    uuid: '',
    no_number: '',
    form_date: formatDate(new Date(), 'YYYY-MM-DD'),
    order_no: '',
    customer: null,
    status: 'scrap',
    notes: '',
    items: [],
  };

  inventoryColumns.value = [
    {
      name: 'product_name',
      label: t('product.name'),
      align: 'left' as const,
      field: 'product_name',
      sortable: true,
    },
    {
      name: 'barcode',
      label: t('barcode'),
      align: 'left' as const,
      field: 'barcode',
      sortable: true,
    },
    {
      name: 'category',
      label: t('category'),
      align: 'left' as const,
      field: 'category',
      sortable: true,
    },
    {
      name: 'quantity',
      label: t('quantity'),
      align: 'left' as const,
      field: 'quantity',
    },
    {
      name: 'actions',
      label: t('actions'),
      field: 'actions',
      align: 'center' as const,
    },
  ];

  handleInventoryForm.value = async (form: InventoryForm) => {
    try {
      await Promise.all([submitScrapForm(form)]);
    } finally {
      if (tab.value === 'inventory') {
        getProductStocks();
      } else if (tab.value === 'history') {
        getTransactions();
      }
    }
  };

  inventoryFormRef.value.openDialog();
};

// 盤點
const handleStocktaking = () => {
  inventoryForm.value = {
    title: t('inventory.stocktaking.label'),
    type: 'stocktaking',
    uuid: '',
    no_number: '',
    form_date: formatDate(new Date(), 'YYYY-MM-DD'),
    order_no: '',
    customer: null,
    status: 'stocktaking',
    notes: '',
    items: [],
  };

  inventoryColumns.value = [
    {
      name: 'product_name',
      label: t('product.name'),
      align: 'left' as const,
      field: 'product_name',
      sortable: true,
    },
    {
      name: 'barcode',
      label: t('barcode'),
      align: 'left' as const,
      field: 'barcode',
      sortable: true,
    },
    {
      name: 'category',
      label: t('category'),
      align: 'left' as const,
      field: 'category',
      sortable: true,
    },
    {
      name: 'quantity',
      label: t('quantity'),
      align: 'left' as const,
      field: 'quantity',
    },
    {
      name: 'actions',
      label: t('actions'),
      field: 'actions',
      align: 'center' as const,
    },
  ];

  handleInventoryForm.value = async (form: InventoryForm) => {
    try {
      await Promise.all([submitStocktakingForm(form)]);
    } finally {
      if (tab.value === 'inventory') {
        getProductStocks();
      } else if (tab.value === 'history') {
        getTransactions();
      }
    }
  };

  inventoryFormRef.value.openDialog();
};

const openDialog = () => {
  visible.value = true;
  getProductStocks();
};

const closeDialog = () => {
  visible.value = false;
};

const submitStockInForm = async (form: InventoryForm) => {
  if (form.items.length === 0) {
    return Promise.reject();
  }

  const purchaseOrder = ref<PurchaseOrder>({
    po_number: form.no_number,
    customer: form.customer,
    order_date: form.form_date,
    status: 'received',
    notes: form.notes,
    items: [],
  });

  for (const item of form.items) {
    if (!item.product) continue;

    let totalPrice = parseFloat(
      formatNumber(item.product.price * item.quantity, 2)
    );

    purchaseOrder.value.items.push({
      product: item.product,
      quantity_ordered: item.quantity,
      quantity_received: item.quantity,
      unit_price: item.product.price,
      total_price: totalPrice,
    });
  }

  return PurchaseOrderApi.createPurchaseOrder(purchaseOrder.value);
};

const submitReturnForm = async (form: InventoryForm) => {
  if (form.items.length === 0) {
    return Promise.reject();
  }

  if (!form.return_type) {
    return Promise.reject();
  }

  const returnOrder = ref<ReturnOrder>({
    uuid: '',
    return_type: form.return_type,
    return_number: form.no_number,
    order_no: form.order_no,
    customer: form.customer,
    return_date: form.form_date,
    notes: form.notes,
    items: [],
  });

  for (const item of form.items) {
    if (!item.product) continue;

    returnOrder.value.items.push({
      uuid: '',
      product: item.product,
      quantity: item.quantity,
      notes: '',
    });
  }

  return ReturnOrderApi.createReturnOrder(returnOrder.value);
};

const submitScrapForm = async (form: InventoryForm) => {
  if (form.items.length === 0) {
    return Promise.reject();
  }

  const scrapStock = ref<ScrapStock>({
    uuid: '',
    scrap_date: form.form_date,
    notes: form.notes,
    items: [],
  });

  for (const item of form.items) {
    if (!item.product) continue;

    scrapStock.value.items.push({
      uuid: '',
      product: item.product,
      quantity: item.quantity,
      notes: '',
    });
  }

  return ScrapStockApi.createScrapStock(scrapStock.value);
};

const submitStocktakingForm = async (form: InventoryForm) => {
  if (form.items.length === 0) {
    return Promise.reject();
  }

  const stocktaking = ref<Stocktaking>({
    uuid: '',
    count_number: form.no_number,
    count_date: form.form_date,
    notes: form.notes,
    items: [],
  });

  for (const item of form.items) {
    if (!item.product) continue;

    stocktaking.value.items.push({
      uuid: '',
      product: item.product,
      actual_quantity: item.quantity,
      notes: '',
    });
  }

  return StocktakingApi.createStocktaking(stocktaking.value);
};

const filter = ref<ProductStockFilter>({
  search: '',
});

const rows = ref<ProductStock[]>([]);
const columns = computed(() => [
  {
    name: 'name',
    label: t('product.name'),
    align: 'left' as const,
    field: 'name',
    sortable: true,
  },
  {
    name: 'barcode',
    label: t('barcode'),
    align: 'left' as const,
    field: 'barcode',
    sortable: true,
  },
  {
    name: 'category',
    label: t('category'),
    align: 'left' as const,
    field: 'category',
    sortable: true,
  },
  {
    name: 'stock_quantity',
    label: t('stockQuantity'),
    align: 'left' as const,
    field: 'stock_qty',
    sortable: true,
  },
  {
    name: 'unit',
    label: t('unit'),
    align: 'left' as const,
    field: 'unit',
  },
]);

const pagination = ref<Pagination>({
  sortBy: 'diff_stock_qty',
  descending: false,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

const isLoading = ref(false);

const onRequest = (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  if (sortBy != '') {
    pagination.value.sortBy = sortBy;
    pagination.value.descending = descending;
  }

  getProductStocks();
};

const getProductStocks = async () => {
  try {
    isLoading.value = true;
    const response = await InventoryApi.listProductStocks({
      filter: filter.value,
      pagination: pagination.value,
    });

    rows.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const historyRows = ref<InventoryTransaction[]>([]);
const historyColumns = computed(() => [
  {
    name: 'created_at',
    label: t('dateAt'),
    align: 'left' as const,
    field: (row: InventoryTransaction) =>
      formatDate(row.created_at, 'YYYY-MM-DD HH:mm'),
    sortable: true,
  },
  {
    name: 'product_name',
    label: t('product.name'),
    align: 'left' as const,
    field: (row: InventoryTransaction) => row.product.name,
    sortable: true,
  },
  {
    name: 'barcode',
    label: t('barcode'),
    align: 'left' as const,
    field: (row: InventoryTransaction) => row.product.barcode,
    sortable: true,
  },
  {
    name: 'category',
    label: t('category'),
    align: 'left' as const,
    field: (row: InventoryTransaction) => row.product.category.name,
    sortable: true,
  },
  {
    name: 'before_quantity',
    label: t('inventory.beforeQuantity'),
    align: 'left' as const,
    field: (row: InventoryTransaction) => row.before_quantity,
    sortable: true,
  },
  {
    name: 'after_quantity',
    label: t('inventory.afterQuantity'),
    align: 'left' as const,
    field: (row: InventoryTransaction) => row.after_quantity,
    sortable: true,
  },
  {
    name: 'quantity',
    label: t('inventory.diffQuantity'),
    align: 'left' as const,
    field: (row: InventoryTransaction) =>
      row.after_quantity - row.before_quantity,
    sortable: true,
  },
  {
    name: 'unit',
    label: t('unit'),
    align: 'left' as const,
    field: (row: InventoryTransaction) => row.product.unit,
    sortable: true,
  },
  {
    name: 'transaction_type',
    label: t('type'),
    align: 'left' as const,
    field: (row: InventoryTransaction) =>
      t(`inventory.transaction.${row.transaction_type}`),
  },
]);

const historyPagination = ref<Pagination>({
  sortBy: 'created_at',
  descending: true,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});
const historyFilter = ref<InventoryTransactionFilter>();

const isHistoryLoading = ref(false);

const onHistoryRequest = (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  if (sortBy != '') {
    historyPagination.value.sortBy = sortBy;
    historyPagination.value.descending = descending;
  }

  getTransactions();
};

const getTransactions = async () => {
  try {
    isHistoryLoading.value = true;
    const response = await InventoryApi.listTransactions({
      filter: historyFilter.value,
      pagination: historyPagination.value,
    });

    historyRows.value = response.result.data;
    historyPagination.value = response.result.pagination;
  } finally {
    isHistoryLoading.value = false;
  }
};

const clearSearch = () => {
  filter.value.search = '';
  getProductStocks();
};

// 對外暴露方法
defineExpose({
  openDialog,
});
</script>
