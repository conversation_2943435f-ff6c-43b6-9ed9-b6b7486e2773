// 頁面容器樣式
.stock-page {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;

  .content-container {
    margin-top: 30px;
  }

  // animation: pageTransition 0.4s ease-out;

  // 新增底線動畫
  // .page-title::after {
  //   animation: underlineExpand 0.6s 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  // }
}

@keyframes pageTransition {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes underlineExpand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 60px;
    opacity: 1;
  }
}

// 內容容器樣式
.content-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

// 頁面標題樣式
.page-title {
  font-size: 2.3rem;
  font-weight: 700;
  margin: 0 auto 25px;
  display: block;
  width: fit-content;
  position: relative;
  padding-bottom: 15px;
  text-align: center;
  opacity: 0;

  // 深藍到青藍漸層
  background: linear-gradient(135deg, #2d3436 0%, #0984e3 100%);

  /* 標準屬性在前 */
  background-clip: text;
  -webkit-background-clip: text; /* 瀏覽器前綴在後 */

  /* 文字顏色透明 */
  color: transparent;
  -webkit-text-fill-color: transparent; /* 兼容webkit瀏覽器 */

  &::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    width: 60px;
    height: 3px;
    background: #0984e3;
    border-radius: 2px;
    transition: all 0.3s ease;
  }

  &:hover::after {
    width: 80px;
    background: linear-gradient(90deg, #0984e3 0%, #00cec9 100%);
  }

  // animation: titleFade 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes titleFade {
  0% {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 搜尋區域樣式
.search-section {
  max-width: 300px;
}

// 搜尋輸入框樣式
.search-input {
  width: 100%;
}

// 功能按鈕區域樣式
.action-buttons {
  display: flex;
  gap: 10px;
}

// 表格容器樣式
.stock-table-container {
  margin-top: 1rem;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

// 表格樣式
.stock-table {
  border-radius: 4px;
}

// 對話框內容樣式
.dialog-content {
  .q-input {
    margin-bottom: 1rem;
  }
}

// 掃描器樣式
.scanner-container {
  width: 100%;
  height: 300px;
  overflow: hidden;
  position: relative;
  background: #000;
}

.scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 600px) {
  .page-title {
    font-size: 1.8rem;

    &::after {
      width: 40px;
      height: 2px;
    }

    &:hover::after {
      width: 50px;
    }
  }
}
