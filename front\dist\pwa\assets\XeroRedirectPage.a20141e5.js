import{h as o,L as P,bx as R,by as O,d as V,aI as z,b3 as D,r as u,o as L,p as m,v as b,m as v,t as c,x as g,k as S,A as p,j as l,q as $,y as k,G as X,H as F}from"./index.09f89dc4.js";import{Q as j}from"./QExpansionItem.ea6d0330.js";import{Q as G}from"./QPage.ce1b4cb5.js";import{u as H}from"./use-quasar.3b603a60.js";import{u as J}from"./vue-i18n.1783a0cb.js";import{X as Y}from"./xero.34271ea4.js";import"./QItemSection.3e7b5a38.js";import"./QItemLabel.88180eb1.js";const K=[o("g",{transform:"translate(20 50)"},[o("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.6"},[o("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),o("g",{transform:"translate(50 50)"},[o("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.8"},[o("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.1s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),o("g",{transform:"translate(80 50)"},[o("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.9"},[o("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.2s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])])];var U=P({name:"QSpinnerFacebook",props:R,setup(r){const{cSize:n,classes:a}=O(r);return()=>o("svg",{class:a.value,width:n.value,height:n.value,viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},K)}});const f=r=>{if(r!=null)return Array.isArray(r)?r[0]||void 0:r},A=r=>{const n=f(r);return typeof n=="string"&&n.length>0},W={class:"text-center"},Z={key:1,class:"text-h6 q-mt-md"},ee={key:2,class:"text-center"},te={class:"text-h6 q-mt-md text-positive"},re={class:"q-mt-md"},oe={key:3,class:"text-center"},se={class:"text-h6 q-mt-md text-negative"},ae={class:"q-mt-md text-body2"},ne={class:"text-caption"},ie={class:"q-mt-md q-gutter-sm"},fe=V({__name:"XeroRedirectPage",setup(r){const n=z(),a=D(),w=H(),{t:d}=J(),y=u(!0),C=u(!1),q=u(!1),Q=u(""),I=u(""),h=u(""),M=async()=>{var e;try{const t=f(a.query.code),T=f(a.query.state),_=f(a.query.error);if(console.log("OAuth Callback Debug:",{code:t?"present":"missing",state:T?"present":"missing",error_param:_,savedState:localStorage.getItem("xero_oauth_state")?"present":"missing"}),_)throw new Error(d("xero.redirect.error.messages.authError",{error:_}));if(!A(a.query.code)||!A(a.query.state))throw new Error(d("xero.redirect.error.messages.missingParams"));const E=t,i=T,s=localStorage.getItem("xero_oauth_state");console.log("State Validation:",{receivedState:i,savedState:s,match:s===i,receivedStateLength:i.length,savedStateLength:s==null?void 0:s.length}),s?s!==i?(console.error("State mismatch detected:",{expected:s,received:i,expectedType:typeof s,receivedType:typeof i}),console.warn("State mismatch, but continuing with backend validation")):console.log("State validation passed successfully"):console.warn("No saved state found in localStorage, attempting backend validation");const N=await Y.callback(E,i);console.log("Callback Response:",N),localStorage.removeItem("xero_oauth_state"),I.value=((e=N.result)==null?void 0:e.tenant_name)||d("xero.redirect.error.messages.unknownOrg"),C.value=!0,w.notify({position:"top",type:"positive",message:d("xero.redirect.success.notification")})}catch(t){console.error("OAuth Callback Error:",t),q.value=!0,Q.value=(t==null?void 0:t.message)||d("xero.redirect.error.title"),h.value=JSON.stringify({url:window.location.href,query:a.query,savedState:localStorage.getItem("xero_oauth_state"),error:t==null?void 0:t.message,timestamp:new Date().toISOString()},null,2)}finally{y.value=!1}},x=()=>{n.push("/admin/dashboard/xero/setup")},B=()=>{localStorage.removeItem("xero_oauth_state"),w.notify({position:"top",type:"info",message:d("xero.redirect.debug.stateCleared")}),x()};return L(()=>{M()}),(e,t)=>(m(),b(G,{class:"flex flex-center"},{default:v(()=>[c("div",W,[y.value?(m(),b(U,{key:0,color:"primary",size:"50px"})):g("",!0),y.value?(m(),S("div",Z,p(e.$t("xero.redirect.processing")),1)):g("",!0),C.value?(m(),S("div",ee,[l($,{name:"check_circle",color:"positive",size:"50px"}),c("div",te,p(e.$t("xero.redirect.success.title")),1),c("div",re,p(e.$t("xero.redirect.success.connectedTo",{tenantName:I.value})),1),l(k,{color:"primary",label:e.$t("xero.redirect.success.backToSettings"),class:"q-mt-md",onClick:x},null,8,["label"])])):g("",!0),q.value?(m(),S("div",oe,[l($,{name:"error",color:"negative",size:"50px"}),c("div",se,p(e.$t("xero.redirect.error.title")),1),c("div",ae,p(Q.value),1),h.value?(m(),b(j,{key:0,icon:"bug_report",label:e.$t("xero.redirect.debug.title"),class:"q-mt-md"},{default:v(()=>[l(F,{class:"q-mt-sm"},{default:v(()=>[l(X,{class:"text-left"},{default:v(()=>[c("pre",ne,p(h.value),1)]),_:1})]),_:1})]),_:1},8,["label"])):g("",!0),c("div",ie,[l(k,{color:"primary",label:e.$t("xero.redirect.error.retry"),onClick:x},null,8,["label"]),l(k,{color:"secondary",label:e.$t("xero.redirect.error.clearState"),onClick:B},null,8,["label"])])])):g("",!0)])]),_:1}))}});export{fe as default};
