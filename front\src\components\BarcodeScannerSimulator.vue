<template>
  <div class="barcode-scanner-simulator">
    <q-input
      v-model="barcodeInput"
      label="輸入條碼"
      outlined
      dense
      @keyup.enter="simulateScan"
    />

    <div class="q-mt-md">
      <q-btn
        v-for="(code, index) in sampleBarcodes"
        :key="index"
        :label="`掃描: ${code}`"
        color="primary"
        class="q-ma-xs"
        @click="simulateScan(code)"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { Product } from '@/api/product';

const $q = useQuasar();

// 定義props接收商品列表，以便檢查掃描結果
const props = defineProps({
  products: {
    type: Array as () => Product[],
    default: () => [],
  },
  barcodeField: {
    type: String,
    default: 'barcode',
  },
});

const barcodeInput = ref('');

// 從商品列表中提取一些條碼作為示例
const sampleBarcodes = computed(() => {
  // 從商品中提取幾個條碼作為快速掃描按鈕
  return props.products
    .filter((product) => product.barcode == props.barcodeField)
    .slice(0, 3)
    .map((product) => product.barcode);
});

const simulateScan = (barcode: string) => {
  // 如果接收到的是事件對象，則從輸入框中獲取條碼
  const code = typeof barcode === 'string' ? barcode : barcodeInput.value;

  if (!code) return;

  // 創建與真實條碼掃描器相同的事件
  const barcodeEvent = new CustomEvent('barcode-scanned', {
    detail: { barcode: code },
  });

  // 檢查商品是否存在
  const productExists = props.products.some(
    (product) => product.barcode === code
  );

  // 發送事件
  window.dispatchEvent(barcodeEvent);

  // 清空輸入框
  barcodeInput.value = '';

  // 顯示掃描通知
  $q.notify({
    message: `已模擬掃描條碼: ${code}`,
    color: productExists ? 'positive' : 'warning',
    position: 'top',
    timeout: 1500,
  });
};

// 導出方法供父組件使用
defineExpose({
  simulateScan,
});
</script>

<style lang="scss" scoped>
.barcode-scanner-simulator {
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f8f8;
}
</style>
