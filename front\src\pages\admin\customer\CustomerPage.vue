<template>
  <q-page>
    <q-card flat square bordered class="bg-cream">
      <q-card-section>
        <q-table
          virtual-scroll
          :rows="customers"
          :columns="columns"
          row-key="uuid"
          v-model:pagination="pagination"
          hide-pagination
          binary-state-sort
          table-header-class="bg-grey-3"
          @request="onRequest"
          :loading="isLoading"
        >
          <template v-slot:top>
            <q-toolbar>
              <q-toolbar-title>{{ t('customers') }}</q-toolbar-title>
            </q-toolbar>
            <!-- add customer btn -->
            <q-btn
              type="button"
              @click="handleCreate"
              color="create"
              class="q-pa-sm"
              :size="$q.screen.lt.md ? 'sm' : 'md'"
            >
              <q-icon name="add" />
              <template v-if="$q.screen.gt.sm">
                {{ t('customer.label') }}
              </template>
            </q-btn>
            <!-- search name -->
            <q-input
              v-model="search"
              outlined
              dense
              :placeholder="t('search.customer')"
              class="q-ml-md"
              clearable
              clear-icon="close"
              @keyup.enter.prevent="getCustomers"
              @update:model-value="getCustomers"
              input-debounce="500"
              style="width: 300px"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>
          </template>

          <template v-slot:body="props">
            <q-tr clickable @click="handleEdit(props.row)">
              <q-td :props="props" key="name">
                {{ props.row.name }}
              </q-td>
              <q-td :props="props" key="phone">
                {{ props.row.phone }}
              </q-td>
              <q-td :props="props" key="email">
                {{ props.row.email }}
              </q-td>
              <q-td :props="props" key="license_plate">
                {{ props.row.license_plate }}
              </q-td>
              <q-td :props="props" key="tax_id">
                {{ props.row.tax_id }}
              </q-td>
              <q-td :props="props" key="is_supplier">
                {{ props.row.is_supplier ? 'Y' : 'N' }}
              </q-td>
              <q-td :props="props" key="created_at">
                {{ formatDate(props.row.created_at, 'YYYY-MM-DD HH:mm') }}
              </q-td>
            </q-tr>
          </template>
        </q-table>

        <TablePagination v-model="pagination" @getData="getCustomers" />
      </q-card-section>
    </q-card>

    <CustomerProfile
      v-model="showCustomerDialog"
      :customerUUID="customerData.uuid"
      @refreshData="getCustomers"
    />
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { CustomerApi, Customer } from '@/api/customer';
import { useCustomer } from '@/composables/useCustomer';
import { TableRequestProps } from '@/types';
import { formatDate } from '@/utils';
import CustomerProfile from '@/components/CustomerProfile.vue';

const { t } = useI18n();
const isLoading = ref(false);

const columns = computed(() => [
  {
    name: 'name',
    field: 'name',
    label: t('name'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'phone',
    field: 'phone',
    label: t('phone'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'email',
    field: 'email',
    label: t('email.label'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'license_plate',
    field: 'license_plate',
    label: t('licensePlate'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'tax_id',
    field: 'tax_id',
    label: t('taxID'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'is_supplier',
    field: 'is_supplier',
    label: t('supplier'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'created_at',
    field: 'created_at',
    label: t('createdAt'),
    align: 'left' as const,
    sortable: true,
  },
]);

const search = ref('');
const customers = ref<Customer[]>([]);
const pagination = ref({
  sortBy: 'created_at',
  descending: true,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

onMounted(() => {
  getCustomers();
});

const customerFac = useCustomer();
const customerData = ref<Customer>(<Customer>{});
const showCustomerDialog = ref(false);

const handleCreate = () => {
  customerData.value = customerFac.newCustomer();
  showCustomerDialog.value = true;
};

const handleEdit = (customer: Customer) => {
  customerData.value = customer;
  showCustomerDialog.value = true;
};

const getCustomers = async () => {
  try {
    isLoading.value = true;
    const response = await CustomerApi.fetch({
      filter: {
        search: search.value,
      },
      pagination: pagination.value,
    });

    customers.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const onRequest = async (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  getCustomers();
};
</script>

<style lang="scss" scoped>
.q-table {
  background-color: $light;
}
</style>
