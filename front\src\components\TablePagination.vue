<template>
  <div class="row col-12 justify-center q-mt-sm">
    <q-pagination
      v-model="localPage"
      color="grey-8"
      :max="pagesNumber"
      size="sm"
      @update:model-value="updatePagination"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Pagination } from '@/types';

const props = defineProps<{
  modelValue: Pagination;
}>();

const emit = defineEmits(['update:modelValue', 'getData']);

const localPage = ref(1);

const pagesNumber = computed(() =>
  Math.max(
    1,
    Math.ceil(props.modelValue.rowsNumber / props.modelValue.rowsPerPage)
  )
);

const updatePagination = (newPage: number) => {
  emit('update:modelValue', {
    ...props.modelValue,
    page: newPage,
  });
  emit('getData');
};

watch(
  () => props.modelValue.page,
  (newPage) => {
    localPage.value = newPage;
  },
  { immediate: true } // 確保初始化時同步值
);
</script>
