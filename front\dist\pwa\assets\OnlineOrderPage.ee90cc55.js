import{Q,a as T}from"./QToolbar.b89be0c0.js";import{Q as V}from"./QSelect.958fa87e.js";import{Q as p}from"./QTd.00e5e315.js";import{f as C,Q as H}from"./QTr.2cbfa351.js";import{Q as I}from"./QTable.5ba31f17.js";import{d as N,r,c as g,o as q,p as v,v as $,m as s,j as l,G as E,B as m,A as n,C as u,k as x,F as O,H as F}from"./index.09f89dc4.js";import{Q as G}from"./QPage.ce1b4cb5.js";import{u as L}from"./vue-i18n.1783a0cb.js";import{_ as R}from"./WCOrderDetailDialog.f8698d20.js";import{_ as j}from"./TablePagination.8f5d653e.js";import{C as W}from"./customer.e2880270.js";import{O as z}from"./order.7fd0b308.js";import{o as J,c as K}from"./order.46055623.js";import{f as h}from"./date.6d29930c.js";import"./QItemSection.3e7b5a38.js";import"./QItemLabel.88180eb1.js";import"./QMenu.531c6599.js";import"./selection.2acb415c.js";import"./format.054b8074.js";import"./QList.5d1c2d4f.js";import"./use-fullscreen.2a1ec9b4.js";import"./QSpace.2ea7fb32.js";import"./QScrollArea.e7fd209f.js";import"./QScrollObserver.942d75c7.js";import"./TouchPan.818d9316.js";import"./dialog.27403fe4.js";import"./i18n.fac3fce5.js";const xe=N({__name:"OnlineOrderPage",setup(X){const{t:o}=L(),P=(e,t)=>{if(!e&&!t)return o("unknown");switch(e){case"australia_post_express":return"Australia Post Express";case"australia_post_parcel":return"Australia Post Parcel";case"australia_post":return"Australia Post";default:return t||e||o("unknown")}},_=r([]),S=g(()=>[{name:"id",label:o("orderNo"),field:"id",align:"center"},{name:"date_created",label:o("dateAt"),field:e=>h(e.date_created,"YYYY-MM-DD HH:mm"),align:"center"},{name:"customer",label:o("customer.label"),field:e=>e.customer_name||o("unknown.customer"),align:"center"},{name:"total",label:o("total"),field:e=>C(e.total,2),align:"center"},{name:"status",label:o("status"),field:e=>o(e.status),align:"center"},{name:"shipping_method",label:o("shippingMethod"),field:e=>P(e.shipping_method,e.shipping_method_title),align:"center"}]),i=r({sortBy:"order_at",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),c=r(!1),b=r(""),y=r(""),A=g(()=>[{label:o("allStatus"),value:""},...J.value]),d=async()=>{try{c.value=!0;const e=await z.listWCHistory();_.value=e.result.data,i.value=e.result.pagination}finally{c.value=!1}},w=r([]),U=g(()=>[{uuid:"",name:o("customer.all")},...w.value]),Y=async()=>{const e=await W.fetch();for(let t of e.result.data)w.value.push({uuid:t.uuid,name:t.name})};q(()=>{d(),Y()});const f=r(!1),k=r(0),B=e=>{k.value=e,f.value=!0},M=e=>{if(!e)return;const t=e,{sortBy:a,descending:D}=t.pagination;i.value.sortBy=a,i.value.descending=D,d()};return(e,t)=>(v(),$(G,null,{default:s(()=>[l(F,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:s(()=>[l(E,null,{default:s(()=>[l(I,{"virtual-scroll":"",rows:_.value,columns:S.value,pagination:i.value,"onUpdate:pagination":t[2]||(t[2]=a=>i.value=a),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",onRequest:M,loading:c.value},{top:s(()=>[l(Q,null,{default:s(()=>[l(T,null,{default:s(()=>[m(n(u(o)("orders"))+" - "+n(u(o)("online")),1)]),_:1})]),_:1}),l(Q,{style:{display:"none"}},{default:s(()=>[l(V,{modelValue:b.value,"onUpdate:modelValue":[t[0]||(t[0]=a=>b.value=a),d],options:U.value,"option-label":"name","option-value":"uuid",label:u(o)("customer.label"),"emit-value":"","map-options":""},null,8,["modelValue","options","label"]),l(V,{modelValue:y.value,"onUpdate:modelValue":t[1]||(t[1]=a=>y.value=a),options:A.value,"option-label":"label","option-value":"value",label:u(o)("orderStatus.label"),"emit-value":"","map-options":"",class:"q-mx-md",style:{width:"120px"}},null,8,["modelValue","options","label"])]),_:1})]),body:s(a=>[l(H,{props:a,onClick:D=>B(a.row.id)},{default:s(()=>[l(p,{props:a,key:"id"},{default:s(()=>[m(n(a.row.id),1)]),_:2},1032,["props"]),l(p,{props:a,key:"date_created"},{default:s(()=>[m(n(u(h)(a.row.date_created,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"]),l(p,{props:a,key:"customer"},{default:s(()=>[a.row.customer_name?(v(),x(O,{key:0},[m(n(a.row.customer_name),1)],64)):(v(),x(O,{key:1},[m(n(u(o)("unknown.customer")),1)],64))]),_:2},1032,["props"]),l(p,{props:a,key:"total",class:"text-bold"},{default:s(()=>[m(" AU$ "+n(u(C)(a.row.total,2)),1)]),_:2},1032,["props"]),l(p,{props:a,key:"status",class:"text-bold"},{default:s(()=>[m(n(u(K)(a.row.status)),1)]),_:2},1032,["props"])]),_:2},1032,["props","onClick"])]),_:1},8,["rows","columns","pagination","loading"]),l(j,{modelValue:i.value,"onUpdate:modelValue":t[3]||(t[3]=a=>i.value=a),onGetData:d},null,8,["modelValue"])]),_:1})]),_:1}),l(R,{modelValue:f.value,"onUpdate:modelValue":t[4]||(t[4]=a=>f.value=a),orderID:k.value,onRefresh:d},null,8,["modelValue","orderID"])]),_:1}))}});export{xe as default};
