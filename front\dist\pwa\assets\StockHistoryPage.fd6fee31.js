import{d as k,r as u,c as q,o as h,z as w,p as d,v as Q,m as t,j as s,G as x,t as P,Q as S,l as V,q as y,n as B,k as p,A as f,H as C}from"./index.09f89dc4.js";import{Q as I}from"./QTd.00e5e315.js";import{Q as T}from"./QTable.5ba31f17.js";import{Q as H}from"./QPage.ce1b4cb5.js";import{u as N}from"./vue-i18n.1783a0cb.js";import{I as U}from"./inventory.655e9b3c.js";import"./QList.5d1c2d4f.js";import"./QSelect.958fa87e.js";import"./QItemSection.3e7b5a38.js";import"./QItemLabel.88180eb1.js";import"./QMenu.531c6599.js";import"./selection.2acb415c.js";import"./format.054b8074.js";import"./use-fullscreen.2a1ec9b4.js";const A={class:"row q-gutter-sm q-mb-sm"},D={key:0,class:"text-positive text-h6"},G={key:1,class:"text-warning text-h6"},M={key:2,class:"text-negative text-h6"},te=k({__name:"StockHistoryPage",setup(j){const{t:n}=N(),g=u([]),_=q(()=>[{name:"name",label:n("product.name"),align:"left",field:"name",sortable:!0},{name:"barcode",label:n("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:n("category"),align:"left",field:"category",sortable:!0},{name:"stock_quantity",label:n("stockQuantity"),align:"left",field:"stock_qty",sortable:!0},{name:"unit",label:n("unit"),align:"left",field:"unit"}]),a=u({sortBy:"diff_stock_qty",descending:!1,page:1,rowsPerPage:20,rowsNumber:0}),b=l=>{if(!l)return;const o=l,{sortBy:c,descending:e}=o.pagination;c!=""&&(a.value.sortBy=c,a.value.descending=e),i()},r=u({search:""}),m=u(!1),i=async()=>{try{m.value=!0;const l=await U.listProductStocks({filter:r.value,pagination:a.value});g.value=l.result.data,a.value=l.result.pagination}finally{m.value=!1}},v=()=>{r.value.search="",i()};return h(()=>{i()}),(l,o)=>{const c=w("TablePagination");return d(),Q(H,null,{default:t(()=>[s(C,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:t(()=>[s(x,null,{default:t(()=>[s(T,{rows:g.value,columns:_.value,"row-key":"uuid","virtual-scroll":"",pagination:a.value,"onUpdate:pagination":o[2]||(o[2]=e=>a.value=e),"hide-pagination":"","binary-state-sort":"",class:"q-pa-sm full-height","table-header-class":"bg-grey-2",onRequest:b,loading:m.value},{top:t(()=>[P("div",A,[s(S,{filled:"",dense:"",modelValue:r.value.search,"onUpdate:modelValue":[o[0]||(o[0]=e=>r.value.search=e),i],debounce:300,label:"Search",class:"q-pb-md-sm"},V({prepend:t(()=>[s(y,{name:"search",class:"cursor-pointer"})]),_:2},[r.value.search?{name:"append",fn:t(()=>[s(y,{name:"close",class:"cursor-pointer",onClick:B(v,["stop"])})]),key:"0"}:void 0]),1032,["modelValue"])])]),bottom:t(()=>[s(c,{modelValue:a.value,"onUpdate:modelValue":o[1]||(o[1]=e=>a.value=e),onGetData:i},null,8,["modelValue"])]),"body-cell-stock_quantity":t(e=>[s(I,{props:e},{default:t(()=>[e.row.diff_stock_qty>5?(d(),p("span",D,f(e.row.stock_qty),1)):e.row.diff_stock_qty>0?(d(),p("span",G,f(e.row.stock_qty),1)):(d(),p("span",M,f(e.row.stock_qty),1))]),_:2},1032,["props"])]),_:1},8,["rows","columns","pagination","loading"])]),_:1})]),_:1})]),_:1})}}});export{te as default};
