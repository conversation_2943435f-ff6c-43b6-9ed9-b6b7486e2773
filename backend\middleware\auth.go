package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"cx/service"
	"cx/utils"
)

// JWT 中間件，用於驗證用戶的 Bearer Token，並將 Token 中的 Claims 附加到Context中
func TokenAuth(authService service.AuthService, userService service.UserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 從授權標頭獲取 Token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			clearAuth(c)
			// utils.HandleError(c, http.StatusUnauthorized, nil, "empty authorization")
			// c.Abort()
			return
		}

		// 驗證 Bearer Token 格式
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			clearAuth(c)
			// utils.HandleError(c, http.StatusUnauthorized, nil, "wrong to parse Bearer")
			// c.Abort()
			return
		}

		tokenString := parts[1]

		// 解析 Token
		claims, isExpired, err := authService.ParseToken(tokenString)
		if err != nil {
			clearAuth(c)
			// utils.HandleError(c, http.StatusUnauthorized, err, "wrong to parse token")
			// c.Abort()
			return
		}

		if isExpired {
			clearAuth(c)
			// utils.HandleError(c, http.StatusUnauthorized, err, "token is expired")
			// c.Abort()
			return
		} else {
			c.Set("claims", claims)
			c.Set("user_uuid", claims.UserUUID)

			if user, _ := userService.GetByUUID(c, claims.UserUUID); user.ID != 0 {
				c.Set("user_id", user.ID)
			}
		}

		c.Next()
	}
}

func clearAuth(c *gin.Context) {
	c.Set("claims", nil)
	c.Set("user_uuid", "")
	c.Set("user_id", 0)
}

// 驗證令牌是否有效，包括令牌是否過期、用戶是否存在、用戶是否被禁用、令牌與用戶是否匹配
func AuthRequired(authService service.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get("claims")
		if !exists {
			utils.HandleError(c, http.StatusUnauthorized, nil, "Invalid token")
			c.Abort()
			return
		}

		valid, err := authService.ValidateToken(c, claims.(*service.TokenClaims))
		if !valid {
			utils.HandleError(c, http.StatusUnauthorized, err, "Invalid or expired token")
			c.Abort()
			return
		}

		c.Next()
	}
}

func AdminOnly(userService service.UserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get("claims")
		if !exists {
			utils.HandleError(c, http.StatusUnauthorized, nil, "Invalid token")
			c.Abort()
			return
		}

		user, err := userService.GetByUUID(c, claims.(*service.TokenClaims).UserUUID)
		if err != nil || !user.IsAdmin {
			utils.HandleError(c, http.StatusUnauthorized, err, "Invalid user")
		}

		c.Next()
	}
}
