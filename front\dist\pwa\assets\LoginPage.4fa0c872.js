import{d as Q,r as n,aI as x,aJ as V,p as w,v as q,m as t,j as l,H as I,G as S,t as A,A as C,C as s,Q as f,q as h,aK as b,aL as L,y as P,I as k,D as z}from"./index.09f89dc4.js";import{Q as B}from"./QForm.c51f3f04.js";import{Q as M}from"./QPage.ce1b4cb5.js";import{u as U}from"./vue-i18n.1783a0cb.js";const D={class:"text-h6"},G=Q({name:"LoginPage",__name:"LoginPage",setup(N){const{t:o}=U(),r=n(""),m=n(""),i=n(!0),u=n(!1),c=n(!1),p=x(),g=V(),v=localStorage.getItem("remembered_account");v&&(r.value=v,u.value=!0);const _=async()=>{c.value=!0;try{const d=await k.login({username:r.value,password:m.value});u.value?localStorage.setItem("remembered_account",r.value):localStorage.removeItem("remembered_account"),g.login(d.result),g.isAdmin()?p.push("/admin/dashboard/user"):p.push("/order")}catch(d){z(d)}finally{c.value=!1}},y=()=>{console.log("rememberMe:",u.value)};return(d,a)=>(w(),q(M,null,{default:t(()=>[l(B,{onSubmit:_,class:"q-py-lg"},{default:t(()=>[l(I,{class:"q-mx-auto q-py-lg q-px-md",style:{"max-width":"min(100%, 28rem)"}},{default:t(()=>[l(S,{class:"q-gutter-md"},{default:t(()=>[A("div",D,C(s(o)("login")),1),l(f,{type:"text",modelValue:r.value,"onUpdate:modelValue":a[0]||(a[0]=e=>r.value=e),label:s(o)("account"),outlined:"",rules:[e=>!!e||s(o)("error.required")],"lazy-rules":""},null,8,["modelValue","label","rules"]),l(f,{type:i.value?"password":"text",modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=e=>m.value=e),label:s(o)("password"),outlined:"",rules:[e=>!!e||s(o)("error.required")],"lazy-rules":""},{append:t(()=>[l(h,{name:i.value?"visibility_off":"visibility",class:"cursor-pointer",onClick:a[1]||(a[1]=e=>i.value=!i.value)},null,8,["name"])]),_:1},8,["type","modelValue","label","rules"])]),_:1}),l(b,{align:"between"},{default:t(()=>[l(L,{modelValue:u.value,"onUpdate:modelValue":a[3]||(a[3]=e=>u.value=e),onUpdate:y,label:s(o)("rememberMe"),size:"lg",color:"toggle",dense:"","keep-color":""},null,8,["modelValue","label"])]),_:1}),l(b,null,{default:t(()=>[l(P,{type:"submit",rounded:"",loading:c.value,label:s(o)("login"),class:"full-width q-mt-sm",color:"login","text-color":"login",size:"lg",ripple:{center:!0}},null,8,["loading","label"])]),_:1})]),_:1})]),_:1})]),_:1}))}});export{G as default};
