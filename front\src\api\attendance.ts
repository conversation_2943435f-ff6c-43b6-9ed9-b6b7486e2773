import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';
import { UserInfo } from './user';
import { Pagination } from '@/types';

export interface Clock {
  uuid: string;
  user: UserInfo;
  type: string;
  clock_time: Date;
}

export interface ClockFilter {
  user_uuid?: string;
  type?: string;
  start_date?: string;
  end_date?: string;
}
export interface ClockCreatePayload {
  user_uuid: string;
  type: string;
}

export interface ClockPair {
  uuid: string;
  date_at: string;
  user: UserInfo;
  clock_in?: Clock;
  clock_out?: Clock;
  status: string;
}

export interface ClockPairFilter {
  start_date?: string;
  end_date?: string;
  user_uuid?: string;
  status?: string[];
}

export const AttendanceApi = {
  fetch: ({
    filter,
    pagination,
  }: {
    filter?: ClockFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: Clock[];
      pagination: Pagination;
    }>('/v1/clocks', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  getLatest: (user_uuid: string) =>
    apiWrapper.get<Clock>('/v1/clocks/latest', { params: { user_uuid } }),
  punch: (payload: ClockCreatePayload) =>
    apiWrapper.post<CreateResponse>('/v1/clocks', payload),

  listClockPairs: ({
    filter,
    pagination,
  }: {
    filter?: ClockPairFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: ClockPair[];
      pagination: Pagination;
    }>('/v1/clocks/pairs', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
};
