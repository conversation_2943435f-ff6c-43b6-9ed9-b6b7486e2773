import{Q as f,a as q}from"./QToolbar.b89be0c0.js";import{d as A,r as s,c as y,o as H,p as w,v as N,m as l,j as t,G as P,B as d,A as m,C as i,Q as $,bz as K,n as R,q as F,k as C,F as O,H as G}from"./index.09f89dc4.js";import{Q as U}from"./QSelect.958fa87e.js";import{Q as p}from"./QTd.00e5e315.js";import{f as L,Q as j}from"./QTr.2cbfa351.js";import{Q as z}from"./QTable.5ba31f17.js";import{Q as E}from"./QPage.ce1b4cb5.js";import{u as J}from"./vue-i18n.1783a0cb.js";import{C as W}from"./customer.e2880270.js";import{O as X}from"./order.7fd0b308.js";import{o as Z,c as ee}from"./order.46055623.js";import{f as S}from"./date.6d29930c.js";import{_ as ae}from"./DateRangePicker.2cfa5e9c.js";import{_ as te}from"./OrderDetailDialog.ab6e7c3b.js";import{_ as oe}from"./TablePagination.8f5d653e.js";import"./QItemSection.3e7b5a38.js";import"./QItemLabel.88180eb1.js";import"./QMenu.531c6599.js";import"./selection.2acb415c.js";import"./format.054b8074.js";import"./QList.5d1c2d4f.js";import"./use-fullscreen.2a1ec9b4.js";import"./i18n.fac3fce5.js";import"./QDate.f6067d5f.js";import"./QPopupProxy.f63a65d9.js";import"./ClosePopup.712518f2.js";import"./QSpace.2ea7fb32.js";import"./QScrollArea.e7fd209f.js";import"./QScrollObserver.942d75c7.js";import"./TouchPan.818d9316.js";import"./use-quasar.3b603a60.js";import"./usePrintInvoice.1b70c5b1.js";import"./plugin-vue_export-helper.21dcd24c.js";import"./xero.34271ea4.js";const Ne=A({__name:"OnsiteOrderPage",setup(le){const{t:r}=J(),V=s([]),c=s({from:"",to:""}),Y=y(()=>[{name:"order_no",label:r("orderNo"),field:"order_no",align:"center"},{name:"order_at",label:r("dateAt"),field:o=>S(o.order_at,"YYYY-MM-DD HH:mm"),align:"center",sortable:!0},{name:"customer",label:r("customer.label"),field:o=>{var a;return((a=o.customer)==null?void 0:a.name)||r("unknown.customer")},align:"center"},{name:"total",label:r("total"),field:o=>L(o.total,2),align:"center",sortable:!0},{name:"status",label:r("status"),field:o=>r(o.status),align:"center",sortable:!0}]),n=s({sortBy:"order_at",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),v=s(!1),b=s(""),g=s(""),Q=s(""),B=y(()=>[{label:r("allStatus"),value:""},...Z.value]),u=async()=>{try{v.value=!0;const o=await X.fetch({filter:{search:b.value,customer_uuid:g.value,start_date:c.value.from,end_date:c.value.to,exclude_status:["pending"]},pagination:n.value});V.value=o.result.data,n.value=o.result.pagination}finally{v.value=!1}},k=s([]),I=y(()=>[{uuid:"",name:r("customer.all")},...k.value]),T=async()=>{const o=await W.fetch();for(let a of o.result.data)k.value.push({uuid:a.uuid,name:a.name})};H(()=>{u(),T()});const _=s(!1),D=s(""),h=o=>{D.value=o,_.value=!0},M=o=>{if(!o)return;const a=o,{sortBy:e,descending:x}=a.pagination;n.value.sortBy=e,n.value.descending=x,u()};return(o,a)=>(w(),N(E,null,{default:l(()=>[t(G,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:l(()=>[t(P,null,{default:l(()=>[t(z,{"virtual-scroll":"",rows:V.value,columns:Y.value,pagination:n.value,"onUpdate:pagination":a[4]||(a[4]=e=>n.value=e),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",onRequest:M,loading:v.value},{top:l(()=>[t(f,null,{default:l(()=>[t(q,null,{default:l(()=>[d(m(i(r)("orders"))+" - "+m(i(r)("onsite")),1)]),_:1})]),_:1}),t(f,null,{default:l(()=>[t(ae,{modelValue:c.value,"onUpdate:modelValue":[a[0]||(a[0]=e=>c.value=e),u]},null,8,["modelValue"])]),_:1}),t(f,null,{default:l(()=>[t($,{modelValue:b.value,"onUpdate:modelValue":[a[1]||(a[1]=e=>b.value=e),u],outlined:"",dense:"",placeholder:i(r)("search.order"),clearable:"","clear-icon":"close",onKeyup:K(R(u,["prevent"]),["enter"]),"input-debounce":"500",style:{width:"300px"}},{prepend:l(()=>[t(F,{name:"search"})]),_:1},8,["modelValue","placeholder","onKeyup"])]),_:1}),t(f,null,{default:l(()=>[t(U,{modelValue:g.value,"onUpdate:modelValue":[a[2]||(a[2]=e=>g.value=e),u],options:I.value,"option-label":"name","option-value":"uuid",label:i(r)("customer.label"),"emit-value":"","map-options":""},null,8,["modelValue","options","label"]),t(U,{modelValue:Q.value,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.value=e),options:B.value,"option-label":"label","option-value":"value",label:i(r)("orderStatus.label"),"emit-value":"","map-options":"",class:"q-mx-md",style:{width:"120px"}},null,8,["modelValue","options","label"])]),_:1})]),body:l(e=>[t(j,{props:e,onClick:x=>h(e.row.uuid)},{default:l(()=>[t(p,{props:e,key:"order_no"},{default:l(()=>[d(m(e.row.order_no),1)]),_:2},1032,["props"]),t(p,{props:e,key:"order_at"},{default:l(()=>[d(m(i(S)(e.row.order_at,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"]),t(p,{props:e,key:"customer"},{default:l(()=>[e.row.customer.name?(w(),C(O,{key:0},[d(m(e.row.customer.name),1)],64)):(w(),C(O,{key:1},[d(m(i(r)("unknown.customer")),1)],64))]),_:2},1032,["props"]),t(p,{props:e,key:"total",class:"text-bold"},{default:l(()=>[d(" AU$ "+m(e.row.total),1)]),_:2},1032,["props"]),t(p,{props:e,key:"status",class:"text-bold"},{default:l(()=>[d(m(i(ee)(e.row.status)),1)]),_:2},1032,["props"])]),_:2},1032,["props","onClick"])]),_:1},8,["rows","columns","pagination","loading"]),t(oe,{modelValue:n.value,"onUpdate:modelValue":a[5]||(a[5]=e=>n.value=e),onGetData:u},null,8,["modelValue"])]),_:1})]),_:1}),t(te,{modelValue:_.value,"onUpdate:modelValue":a[6]||(a[6]=e=>_.value=e),orderID:D.value,onRefresh:u},null,8,["modelValue","orderID"])]),_:1}))}});export{Ne as default};
