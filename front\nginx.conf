server {
    listen 80;
    server_name localhost;

    # gzip 配置
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 路徑配置
    location /au-pos/ {
        alias /usr/share/nginx/html/au-pos/;
        index index.html;
        try_files $uri $uri/ @aupos_fallback;

        # 只對 HTML 檔案禁用快取
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # 對有 hash 的資源啟用快取
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }
    }

    location @aupos_fallback {
        rewrite ^/au-pos/(.*)$ /au-pos/index.html last;
    }

    # PWA manifest 和 service worker 配置
    location /au-pos/manifest.json {
        alias /usr/share/nginx/html/au-pos/manifest.json;
        add_header Cache-Control "public, max-age=0";
    }
}
