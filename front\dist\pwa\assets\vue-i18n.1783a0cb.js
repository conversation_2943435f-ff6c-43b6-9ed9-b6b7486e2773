import{d as dt,h as en,e as yn,c as be,w as st,F as tn,i as Rn,o as kn,a as Dn,f as Mn,g as nn,r as Fn,s as vn,j as Un,T as wn}from"./index.09f89dc4.js";/*!
  * shared v9.14.0
  * (c) 2024 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */const ke=typeof window!="undefined",le=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Wn=(e,t,n)=>Vn({l:e,k:t,s:n}),Vn=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),X=e=>typeof e=="number"&&isFinite(e),xn=e=>an(e)==="[object Date]",De=e=>an(e)==="[object RegExp]",we=e=>v(e)&&Object.keys(e).length===0,j=Object.assign;let Ot;const Me=()=>Ot||(Ot=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{});function bt(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const $n=Object.prototype.hasOwnProperty;function Fe(e,t){return $n.call(e,t)}const G=Array.isArray,$=e=>typeof e=="function",C=e=>typeof e=="string",x=e=>typeof e=="boolean",U=e=>e!==null&&typeof e=="object",Yn=e=>U(e)&&$(e.then)&&$(e.catch),rn=Object.prototype.toString,an=e=>rn.call(e),v=e=>{if(!U(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},Gn=e=>e==null?"":G(e)||v(e)&&e.toString===rn?JSON.stringify(e,null,2):String(e);function Xn(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}function We(e){let t=e;return()=>++t}function Kn(e,t){typeof console!="undefined"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ye=e=>!U(e)||G(e);function Re(e,t){if(ye(e)||ye(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:s}=n.pop();Object.keys(r).forEach(c=>{ye(r[c])||ye(s[c])?s[c]=r[c]:n.push({src:r[c],des:s[c]})})}}/*!
  * message-compiler v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Hn(e,t,n){return{line:e,column:t,offset:n}}function ve(e,t,n){const r={start:e,end:t};return n!=null&&(r.source=n),r}const jn=/\{([0-9a-zA-Z]+)\}/g;function sn(e,...t){return t.length===1&&Bn(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(jn,(n,r)=>t.hasOwnProperty(r)?t[r]:"")}const ln=Object.assign,ht=e=>typeof e=="string",Bn=e=>e!==null&&typeof e=="object";function cn(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}const Et={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},Jn={[Et.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function Qn(e,t,...n){const r=sn(Jn[e]||"",...n||[]),s={message:String(r),code:e};return t&&(s.location=t),s}const k={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},qn={[k.EXPECTED_TOKEN]:"Expected token: '{0}'",[k.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[k.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[k.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[k.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[k.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[k.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[k.EMPTY_PLACEHOLDER]:"Empty placeholder",[k.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[k.INVALID_LINKED_FORMAT]:"Invalid linked format",[k.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[k.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[k.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[k.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[k.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[k.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function Te(e,t,n={}){const{domain:r,messages:s,args:c}=n,o=sn((s||qn)[e]||"",...c||[]),u=new SyntaxError(String(o));return u.code=e,t&&(u.location=t),u.domain=r,u}function Zn(e){throw e}const re=" ",zn="\r",H=`
`,er=String.fromCharCode(8232),tr=String.fromCharCode(8233);function nr(e){const t=e;let n=0,r=1,s=1,c=0;const o=p=>t[p]===zn&&t[p+1]===H,u=p=>t[p]===H,i=p=>t[p]===tr,d=p=>t[p]===er,O=p=>o(p)||u(p)||i(p)||d(p),T=()=>n,L=()=>r,h=()=>s,R=()=>c,D=p=>o(p)||i(p)||d(p)?H:t[p],A=()=>D(n),y=()=>D(n+c);function F(){return c=0,O(n)&&(r++,s=0),o(n)&&n++,n++,s++,t[n]}function _(){return o(n+c)&&c++,c++,t[n+c]}function E(){n=0,r=1,s=1,c=0}function I(p=0){c=p}function N(){const p=n+c;for(;p!==n;)F();c=0}return{index:T,line:L,column:h,peekOffset:R,charAt:D,currentChar:A,currentPeek:y,next:F,peek:_,reset:E,resetPeek:I,skipToPeek:N}}const oe=void 0,rr=".",Ct="'",ar="tokenizer";function sr(e,t={}){const n=t.location!==!1,r=nr(e),s=()=>r.index(),c=()=>Hn(r.line(),r.column(),r.index()),o=c(),u=s(),i={currentType:14,offset:u,startLoc:o,endLoc:o,lastType:14,lastOffset:u,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},d=()=>i,{onError:O}=t;function T(a,l,m,...P){const V=d();if(l.column+=m,l.offset+=m,O){const M=n?ve(V.startLoc,l):null,f=Te(a,M,{domain:ar,args:P});O(f)}}function L(a,l,m){a.endLoc=c(),a.currentType=l;const P={type:l};return n&&(P.loc=ve(a.startLoc,a.endLoc)),m!=null&&(P.value=m),P}const h=a=>L(a,14);function R(a,l){return a.currentChar()===l?(a.next(),l):(T(k.EXPECTED_TOKEN,c(),0,l),"")}function D(a){let l="";for(;a.currentPeek()===re||a.currentPeek()===H;)l+=a.currentPeek(),a.peek();return l}function A(a){const l=D(a);return a.skipToPeek(),l}function y(a){if(a===oe)return!1;const l=a.charCodeAt(0);return l>=97&&l<=122||l>=65&&l<=90||l===95}function F(a){if(a===oe)return!1;const l=a.charCodeAt(0);return l>=48&&l<=57}function _(a,l){const{currentType:m}=l;if(m!==2)return!1;D(a);const P=y(a.currentPeek());return a.resetPeek(),P}function E(a,l){const{currentType:m}=l;if(m!==2)return!1;D(a);const P=a.currentPeek()==="-"?a.peek():a.currentPeek(),V=F(P);return a.resetPeek(),V}function I(a,l){const{currentType:m}=l;if(m!==2)return!1;D(a);const P=a.currentPeek()===Ct;return a.resetPeek(),P}function N(a,l){const{currentType:m}=l;if(m!==8)return!1;D(a);const P=a.currentPeek()===".";return a.resetPeek(),P}function p(a,l){const{currentType:m}=l;if(m!==9)return!1;D(a);const P=y(a.currentPeek());return a.resetPeek(),P}function S(a,l){const{currentType:m}=l;if(!(m===8||m===12))return!1;D(a);const P=a.currentPeek()===":";return a.resetPeek(),P}function b(a,l){const{currentType:m}=l;if(m!==10)return!1;const P=()=>{const M=a.currentPeek();return M==="{"?y(a.peek()):M==="@"||M==="%"||M==="|"||M===":"||M==="."||M===re||!M?!1:M===H?(a.peek(),P()):w(a,!1)},V=P();return a.resetPeek(),V}function K(a){D(a);const l=a.currentPeek()==="|";return a.resetPeek(),l}function ee(a){const l=D(a),m=a.currentPeek()==="%"&&a.peek()==="{";return a.resetPeek(),{isModulo:m,hasSpace:l.length>0}}function w(a,l=!0){const m=(V=!1,M="",f=!1)=>{const g=a.currentPeek();return g==="{"?M==="%"?!1:V:g==="@"||!g?M==="%"?!0:V:g==="%"?(a.peek(),m(V,"%",!0)):g==="|"?M==="%"||f?!0:!(M===re||M===H):g===re?(a.peek(),m(!0,re,f)):g===H?(a.peek(),m(!0,H,f)):!0},P=m();return l&&a.resetPeek(),P}function Q(a,l){const m=a.currentChar();return m===oe?oe:l(m)?(a.next(),m):null}function xe(a){const l=a.charCodeAt(0);return l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===95||l===36}function $e(a){return Q(a,xe)}function Ye(a){const l=a.charCodeAt(0);return l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===95||l===36||l===45}function Ge(a){return Q(a,Ye)}function Xe(a){const l=a.charCodeAt(0);return l>=48&&l<=57}function Ke(a){return Q(a,Xe)}function He(a){const l=a.charCodeAt(0);return l>=48&&l<=57||l>=65&&l<=70||l>=97&&l<=102}function ne(a){return Q(a,He)}function Ie(a){let l="",m="";for(;l=Ke(a);)m+=l;return m}function je(a){A(a);const l=a.currentChar();return l!=="%"&&T(k.EXPECTED_TOKEN,c(),0,l),a.next(),"%"}function Ae(a){let l="";for(;;){const m=a.currentChar();if(m==="{"||m==="}"||m==="@"||m==="|"||!m)break;if(m==="%")if(w(a))l+=m,a.next();else break;else if(m===re||m===H)if(w(a))l+=m,a.next();else{if(K(a))break;l+=m,a.next()}else l+=m,a.next()}return l}function Be(a){A(a);let l="",m="";for(;l=Ge(a);)m+=l;return a.currentChar()===oe&&T(k.UNTERMINATED_CLOSING_BRACE,c(),0),m}function Je(a){A(a);let l="";return a.currentChar()==="-"?(a.next(),l+=`-${Ie(a)}`):l+=Ie(a),a.currentChar()===oe&&T(k.UNTERMINATED_CLOSING_BRACE,c(),0),l}function pt(a){return a!==Ct&&a!==H}function Qe(a){A(a),R(a,"'");let l="",m="";for(;l=Q(a,pt);)l==="\\"?m+=qe(a):m+=l;const P=a.currentChar();return P===H||P===oe?(T(k.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,c(),0),P===H&&(a.next(),R(a,"'")),m):(R(a,"'"),m)}function qe(a){const l=a.currentChar();switch(l){case"\\":case"'":return a.next(),`\\${l}`;case"u":return Se(a,l,4);case"U":return Se(a,l,6);default:return T(k.UNKNOWN_ESCAPE_SEQUENCE,c(),0,l),""}}function Se(a,l,m){R(a,l);let P="";for(let V=0;V<m;V++){const M=ne(a);if(!M){T(k.INVALID_UNICODE_ESCAPE_SEQUENCE,c(),0,`\\${l}${P}${a.currentChar()}`);break}P+=M}return`\\${l}${P}`}function Ze(a){return a!=="{"&&a!=="}"&&a!==re&&a!==H}function ze(a){A(a);let l="",m="";for(;l=Q(a,Ze);)m+=l;return m}function et(a){let l="",m="";for(;l=$e(a);)m+=l;return m}function tt(a){const l=m=>{const P=a.currentChar();return P==="{"||P==="%"||P==="@"||P==="|"||P==="("||P===")"||!P||P===re?m:(m+=P,a.next(),l(m))};return l("")}function pe(a){A(a);const l=R(a,"|");return A(a),l}function _e(a,l){let m=null;switch(a.currentChar()){case"{":return l.braceNest>=1&&T(k.NOT_ALLOW_NEST_PLACEHOLDER,c(),0),a.next(),m=L(l,2,"{"),A(a),l.braceNest++,m;case"}":return l.braceNest>0&&l.currentType===2&&T(k.EMPTY_PLACEHOLDER,c(),0),a.next(),m=L(l,3,"}"),l.braceNest--,l.braceNest>0&&A(a),l.inLinked&&l.braceNest===0&&(l.inLinked=!1),m;case"@":return l.braceNest>0&&T(k.UNTERMINATED_CLOSING_BRACE,c(),0),m=me(a,l)||h(l),l.braceNest=0,m;default:{let V=!0,M=!0,f=!0;if(K(a))return l.braceNest>0&&T(k.UNTERMINATED_CLOSING_BRACE,c(),0),m=L(l,1,pe(a)),l.braceNest=0,l.inLinked=!1,m;if(l.braceNest>0&&(l.currentType===5||l.currentType===6||l.currentType===7))return T(k.UNTERMINATED_CLOSING_BRACE,c(),0),l.braceNest=0,Oe(a,l);if(V=_(a,l))return m=L(l,5,Be(a)),A(a),m;if(M=E(a,l))return m=L(l,6,Je(a)),A(a),m;if(f=I(a,l))return m=L(l,7,Qe(a)),A(a),m;if(!V&&!M&&!f)return m=L(l,13,ze(a)),T(k.INVALID_TOKEN_IN_PLACEHOLDER,c(),0,m.value),A(a),m;break}}return m}function me(a,l){const{currentType:m}=l;let P=null;const V=a.currentChar();switch((m===8||m===9||m===12||m===10)&&(V===H||V===re)&&T(k.INVALID_LINKED_FORMAT,c(),0),V){case"@":return a.next(),P=L(l,8,"@"),l.inLinked=!0,P;case".":return A(a),a.next(),L(l,9,".");case":":return A(a),a.next(),L(l,10,":");default:return K(a)?(P=L(l,1,pe(a)),l.braceNest=0,l.inLinked=!1,P):N(a,l)||S(a,l)?(A(a),me(a,l)):p(a,l)?(A(a),L(l,12,et(a))):b(a,l)?(A(a),V==="{"?_e(a,l)||P:L(l,11,tt(a))):(m===8&&T(k.INVALID_LINKED_FORMAT,c(),0),l.braceNest=0,l.inLinked=!1,Oe(a,l))}}function Oe(a,l){let m={type:14};if(l.braceNest>0)return _e(a,l)||h(l);if(l.inLinked)return me(a,l)||h(l);switch(a.currentChar()){case"{":return _e(a,l)||h(l);case"}":return T(k.UNBALANCED_CLOSING_BRACE,c(),0),a.next(),L(l,3,"}");case"@":return me(a,l)||h(l);default:{if(K(a))return m=L(l,1,pe(a)),l.braceNest=0,l.inLinked=!1,m;const{isModulo:V,hasSpace:M}=ee(a);if(V)return M?L(l,0,Ae(a)):L(l,4,je(a));if(w(a))return L(l,0,Ae(a));break}}return m}function nt(){const{currentType:a,offset:l,startLoc:m,endLoc:P}=i;return i.lastType=a,i.lastOffset=l,i.lastStartLoc=m,i.lastEndLoc=P,i.offset=s(),i.startLoc=c(),r.currentChar()===oe?L(i,14):Oe(r,i)}return{nextToken:nt,currentOffset:s,currentPosition:c,context:d}}const lr="parser",cr=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function or(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"\uFFFD"}}}function ir(e={}){const t=e.location!==!1,{onError:n,onWarn:r}=e;function s(_,E,I,N,...p){const S=_.currentPosition();if(S.offset+=N,S.column+=N,n){const b=t?ve(I,S):null,K=Te(E,b,{domain:lr,args:p});n(K)}}function c(_,E,I,N,...p){const S=_.currentPosition();if(S.offset+=N,S.column+=N,r){const b=t?ve(I,S):null;r(Qn(E,b,p))}}function o(_,E,I){const N={type:_};return t&&(N.start=E,N.end=E,N.loc={start:I,end:I}),N}function u(_,E,I,N){N&&(_.type=N),t&&(_.end=E,_.loc&&(_.loc.end=I))}function i(_,E){const I=_.context(),N=o(3,I.offset,I.startLoc);return N.value=E,u(N,_.currentOffset(),_.currentPosition()),N}function d(_,E){const I=_.context(),{lastOffset:N,lastStartLoc:p}=I,S=o(5,N,p);return S.index=parseInt(E,10),_.nextToken(),u(S,_.currentOffset(),_.currentPosition()),S}function O(_,E,I){const N=_.context(),{lastOffset:p,lastStartLoc:S}=N,b=o(4,p,S);return b.key=E,I===!0&&(b.modulo=!0),_.nextToken(),u(b,_.currentOffset(),_.currentPosition()),b}function T(_,E){const I=_.context(),{lastOffset:N,lastStartLoc:p}=I,S=o(9,N,p);return S.value=E.replace(cr,or),_.nextToken(),u(S,_.currentOffset(),_.currentPosition()),S}function L(_){const E=_.nextToken(),I=_.context(),{lastOffset:N,lastStartLoc:p}=I,S=o(8,N,p);return E.type!==12?(s(_,k.UNEXPECTED_EMPTY_LINKED_MODIFIER,I.lastStartLoc,0),S.value="",u(S,N,p),{nextConsumeToken:E,node:S}):(E.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,I.lastStartLoc,0,q(E)),S.value=E.value||"",u(S,_.currentOffset(),_.currentPosition()),{node:S})}function h(_,E){const I=_.context(),N=o(7,I.offset,I.startLoc);return N.value=E,u(N,_.currentOffset(),_.currentPosition()),N}function R(_){const E=_.context(),I=o(6,E.offset,E.startLoc);let N=_.nextToken();if(N.type===9){const p=L(_);I.modifier=p.node,N=p.nextConsumeToken||_.nextToken()}switch(N.type!==10&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(N)),N=_.nextToken(),N.type===2&&(N=_.nextToken()),N.type){case 11:N.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(N)),I.key=h(_,N.value||"");break;case 5:N.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(N)),I.key=O(_,N.value||"");break;case 6:N.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(N)),I.key=d(_,N.value||"");break;case 7:N.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(N)),I.key=T(_,N.value||"");break;default:{s(_,k.UNEXPECTED_EMPTY_LINKED_KEY,E.lastStartLoc,0);const p=_.context(),S=o(7,p.offset,p.startLoc);return S.value="",u(S,p.offset,p.startLoc),I.key=S,u(I,p.offset,p.startLoc),{nextConsumeToken:N,node:I}}}return u(I,_.currentOffset(),_.currentPosition()),{node:I}}function D(_){const E=_.context(),I=E.currentType===1?_.currentOffset():E.offset,N=E.currentType===1?E.endLoc:E.startLoc,p=o(2,I,N);p.items=[];let S=null,b=null;do{const w=S||_.nextToken();switch(S=null,w.type){case 0:w.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(w)),p.items.push(i(_,w.value||""));break;case 6:w.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(w)),p.items.push(d(_,w.value||""));break;case 4:b=!0;break;case 5:w.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(w)),p.items.push(O(_,w.value||"",!!b)),b&&(c(_,Et.USE_MODULO_SYNTAX,E.lastStartLoc,0,q(w)),b=null);break;case 7:w.value==null&&s(_,k.UNEXPECTED_LEXICAL_ANALYSIS,E.lastStartLoc,0,q(w)),p.items.push(T(_,w.value||""));break;case 8:{const Q=R(_);p.items.push(Q.node),S=Q.nextConsumeToken||null;break}}}while(E.currentType!==14&&E.currentType!==1);const K=E.currentType===1?E.lastOffset:_.currentOffset(),ee=E.currentType===1?E.lastEndLoc:_.currentPosition();return u(p,K,ee),p}function A(_,E,I,N){const p=_.context();let S=N.items.length===0;const b=o(1,E,I);b.cases=[],b.cases.push(N);do{const K=D(_);S||(S=K.items.length===0),b.cases.push(K)}while(p.currentType!==14);return S&&s(_,k.MUST_HAVE_MESSAGES_IN_PLURAL,I,0),u(b,_.currentOffset(),_.currentPosition()),b}function y(_){const E=_.context(),{offset:I,startLoc:N}=E,p=D(_);return E.currentType===14?p:A(_,I,N,p)}function F(_){const E=sr(_,ln({},e)),I=E.context(),N=o(0,I.offset,I.startLoc);return t&&N.loc&&(N.loc.source=_),N.body=y(E),e.onCacheKey&&(N.cacheKey=e.onCacheKey(_)),I.currentType!==14&&s(E,k.UNEXPECTED_LEXICAL_ANALYSIS,I.lastStartLoc,0,_[I.offset]||""),u(N,E.currentOffset(),E.currentPosition()),N}return{parse:F}}function q(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"\u2026":t}function ur(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:c=>(n.helpers.add(c),c)}}function At(e,t){for(let n=0;n<e.length;n++)Nt(e[n],t)}function Nt(e,t){switch(e.type){case 1:At(e.cases,t),t.helper("plural");break;case 2:At(e.items,t);break;case 6:{Nt(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function fr(e,t={}){const n=ur(e);n.helper("normalize"),e.body&&Nt(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function _r(e){const t=e.body;return t.type===2?St(t):t.cases.forEach(n=>St(n)),e}function St(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=cn(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}const mr="minifier";function Ee(e){switch(e.t=e.type,e.type){case 0:{const t=e;Ee(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)Ee(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)Ee(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Ee(t.key),t.k=t.key,delete t.key,t.modifier&&(Ee(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw Te(k.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:mr,args:[e.type]})}delete e.type}const dr="parser";function Er(e,t){const{sourceMap:n,filename:r,breakLineCode:s,needIndent:c}=t,o=t.location!==!1,u={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:c,indentLevel:0};o&&e.loc&&(u.source=e.loc.source);const i=()=>u;function d(A,y){u.code+=A}function O(A,y=!0){const F=y?s:"";d(c?F+"  ".repeat(A):F)}function T(A=!0){const y=++u.indentLevel;A&&O(y)}function L(A=!0){const y=--u.indentLevel;A&&O(y)}function h(){O(u.indentLevel)}return{context:i,push:d,indent:T,deindent:L,newline:h,helper:A=>`_${A}`,needIndent:()=>u.needIndent}}function Nr(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),ge(e,t.key),t.modifier?(e.push(", "),ge(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function gr(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const s=t.items.length;for(let c=0;c<s&&(ge(e,t.items[c]),c!==s-1);c++)e.push(", ");e.deindent(r()),e.push("])")}function Lr(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const s=t.cases.length;for(let c=0;c<s&&(ge(e,t.cases[c]),c!==s-1);c++)e.push(", ");e.deindent(r()),e.push("])")}}function Tr(e,t){t.body?ge(e,t.body):e.push("null")}function ge(e,t){const{helper:n}=e;switch(t.type){case 0:Tr(e,t);break;case 1:Lr(e,t);break;case 2:gr(e,t);break;case 6:Nr(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw Te(k.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:dr,args:[t.type]})}}const Ir=(e,t={})=>{const n=ht(t.mode)?t.mode:"normal",r=ht(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,c=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,o=t.needIndent?t.needIndent:n!=="arrow",u=e.helpers||[],i=Er(e,{mode:n,filename:r,sourceMap:s,breakLineCode:c,needIndent:o});i.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),i.indent(o),u.length>0&&(i.push(`const { ${cn(u.map(T=>`${T}: _${T}`),", ")} } = ctx`),i.newline()),i.push("return "),ge(i,e),i.deindent(o),i.push("}"),delete e.helpers;const{code:d,map:O}=i.context();return{ast:e,code:d,map:O?O.toJSON():void 0}};function pr(e,t={}){const n=ln({},t),r=!!n.jit,s=!!n.minify,c=n.optimize==null?!0:n.optimize,u=ir(n).parse(e);return r?(c&&_r(u),s&&Ee(u),{ast:u,code:""}):(fr(u,n),Ir(u,n))}/*!
  * core-base v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Or(){typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Me().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Me().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const ie=[];ie[0]={w:[0],i:[3,0],["["]:[4],o:[7]};ie[1]={w:[1],["."]:[2],["["]:[4],o:[7]};ie[2]={w:[2],i:[3,0],[0]:[3,0]};ie[3]={i:[3,0],[0]:[3,0],w:[1,1],["."]:[2,1],["["]:[4,1],o:[7,1]};ie[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],o:8,l:[4,0]};ie[5]={["'"]:[4,0],o:8,l:[5,0]};ie[6]={['"']:[4,0],o:8,l:[6,0]};const br=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function hr(e){return br.test(e)}function Cr(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function Ar(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Sr(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:hr(t)?Cr(t):"*"+t}function Pr(e){const t=[];let n=-1,r=0,s=0,c,o,u,i,d,O,T;const L=[];L[0]=()=>{o===void 0?o=u:o+=u},L[1]=()=>{o!==void 0&&(t.push(o),o=void 0)},L[2]=()=>{L[0](),s++},L[3]=()=>{if(s>0)s--,r=4,L[0]();else{if(s=0,o===void 0||(o=Sr(o),o===!1))return!1;L[1]()}};function h(){const R=e[n+1];if(r===5&&R==="'"||r===6&&R==='"')return n++,u="\\"+R,L[0](),!0}for(;r!==null;)if(n++,c=e[n],!(c==="\\"&&h())){if(i=Ar(c),T=ie[r],d=T[i]||T.l||8,d===8||(r=d[0],d[1]!==void 0&&(O=L[d[1]],O&&(u=c,O()===!1))))return;if(r===7)return t}}const Pt=new Map;function yr(e,t){return U(e)?e[t]:null}function Rr(e,t){if(!U(e))return null;let n=Pt.get(t);if(n||(n=Pr(t),n&&Pt.set(t,n)),!n)return null;const r=n.length;let s=e,c=0;for(;c<r;){const o=s[n[c]];if(o===void 0||$(s))return null;s=o,c++}return s}const kr=e=>e,Dr=e=>"",Mr="text",Fr=e=>e.length===0?"":Xn(e),vr=Gn;function yt(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function Ur(e){const t=X(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(X(e.named.count)||X(e.named.n))?X(e.named.count)?e.named.count:X(e.named.n)?e.named.n:t:t}function wr(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Wr(e={}){const t=e.locale,n=Ur(e),r=U(e.pluralRules)&&C(t)&&$(e.pluralRules[t])?e.pluralRules[t]:yt,s=U(e.pluralRules)&&C(t)&&$(e.pluralRules[t])?yt:void 0,c=y=>y[r(n,y.length,s)],o=e.list||[],u=y=>o[y],i=e.named||{};X(e.pluralIndex)&&wr(n,i);const d=y=>i[y];function O(y){const F=$(e.messages)?e.messages(y):U(e.messages)?e.messages[y]:!1;return F||(e.parent?e.parent.message(y):Dr)}const T=y=>e.modifiers?e.modifiers[y]:kr,L=v(e.processor)&&$(e.processor.normalize)?e.processor.normalize:Fr,h=v(e.processor)&&$(e.processor.interpolate)?e.processor.interpolate:vr,R=v(e.processor)&&C(e.processor.type)?e.processor.type:Mr,A={list:u,named:d,plural:c,linked:(y,...F)=>{const[_,E]=F;let I="text",N="";F.length===1?U(_)?(N=_.modifier||N,I=_.type||I):C(_)&&(N=_||N):F.length===2&&(C(_)&&(N=_||N),C(E)&&(I=E||I));const p=O(y)(A),S=I==="vnode"&&G(p)&&N?p[0]:p;return N?T(N)(S,I):S},message:O,type:R,interpolate:h,normalize:L,values:j({},o,i)};return A}const on=Et.__EXTEND_POINT__,ue=We(on),Vr={NOT_FOUND_KEY:on,FALLBACK_TO_TRANSLATE:ue(),CANNOT_FORMAT_NUMBER:ue(),FALLBACK_TO_NUMBER_FORMAT:ue(),CANNOT_FORMAT_DATE:ue(),FALLBACK_TO_DATE_FORMAT:ue(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:ue(),__EXTEND_POINT__:ue()},un=k.__EXTEND_POINT__,fe=We(un),Z={INVALID_ARGUMENT:un,INVALID_DATE_ARGUMENT:fe(),INVALID_ISO_DATE_ARGUMENT:fe(),NOT_SUPPORT_NON_STRING_MESSAGE:fe(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:fe(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:fe(),NOT_SUPPORT_LOCALE_TYPE:fe(),__EXTEND_POINT__:fe()};function te(e){return Te(e,null,void 0)}function gt(e,t){return t.locale!=null?Rt(t.locale):Rt(e.locale)}let rt;function Rt(e){if(C(e))return e;if($(e)){if(e.resolvedOnce&&rt!=null)return rt;if(e.constructor.name==="Function"){const t=e();if(Yn(t))throw te(Z.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return rt=t}else throw te(Z.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw te(Z.NOT_SUPPORT_LOCALE_TYPE)}function xr(e,t,n){return[...new Set([n,...G(t)?t:U(t)?Object.keys(t):C(t)?[t]:[n]])]}function fn(e,t,n){const r=C(n)?n:Ue,s=e;s.__localeChainCache||(s.__localeChainCache=new Map);let c=s.__localeChainCache.get(r);if(!c){c=[];let o=[n];for(;G(o);)o=kt(c,o,t);const u=G(t)||!v(t)?t:t.default?t.default:null;o=C(u)?[u]:u,G(o)&&kt(c,o,!1),s.__localeChainCache.set(r,c)}return c}function kt(e,t,n){let r=!0;for(let s=0;s<t.length&&x(r);s++){const c=t[s];C(c)&&(r=$r(e,t[s],n))}return r}function $r(e,t,n){let r;const s=t.split("-");do{const c=s.join("-");r=Yr(e,c,n),s.splice(-1,1)}while(s.length&&r===!0);return r}function Yr(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const s=t.replace(/!/g,"");e.push(s),(G(n)||v(n))&&n[s]&&(r=n[s])}return r}const Gr="9.14.0",Ve=-1,Ue="en-US",Dt="",Mt=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function Xr(){return{upper:(e,t)=>t==="text"&&C(e)?e.toUpperCase():t==="vnode"&&U(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&C(e)?e.toLowerCase():t==="vnode"&&U(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&C(e)?Mt(e):t==="vnode"&&U(e)&&"__v_isVNode"in e?Mt(e.children):e}}let _n;function Ft(e){_n=e}let mn;function Kr(e){mn=e}let dn;function Hr(e){dn=e}let En=null;const vt=e=>{En=e},jr=()=>En;let Ut=0;function Br(e={}){const t=$(e.onWarn)?e.onWarn:Kn,n=C(e.version)?e.version:Gr,r=C(e.locale)||$(e.locale)?e.locale:Ue,s=$(r)?Ue:r,c=G(e.fallbackLocale)||v(e.fallbackLocale)||C(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s,o=v(e.messages)?e.messages:{[s]:{}},u=v(e.datetimeFormats)?e.datetimeFormats:{[s]:{}},i=v(e.numberFormats)?e.numberFormats:{[s]:{}},d=j({},e.modifiers||{},Xr()),O=e.pluralRules||{},T=$(e.missing)?e.missing:null,L=x(e.missingWarn)||De(e.missingWarn)?e.missingWarn:!0,h=x(e.fallbackWarn)||De(e.fallbackWarn)?e.fallbackWarn:!0,R=!!e.fallbackFormat,D=!!e.unresolving,A=$(e.postTranslation)?e.postTranslation:null,y=v(e.processor)?e.processor:null,F=x(e.warnHtmlMessage)?e.warnHtmlMessage:!0,_=!!e.escapeParameter,E=$(e.messageCompiler)?e.messageCompiler:_n,I=$(e.messageResolver)?e.messageResolver:mn||yr,N=$(e.localeFallbacker)?e.localeFallbacker:dn||xr,p=U(e.fallbackContext)?e.fallbackContext:void 0,S=e,b=U(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,K=U(S.__numberFormatters)?S.__numberFormatters:new Map,ee=U(S.__meta)?S.__meta:{};Ut++;const w={version:n,cid:Ut,locale:r,fallbackLocale:c,messages:o,modifiers:d,pluralRules:O,missing:T,missingWarn:L,fallbackWarn:h,fallbackFormat:R,unresolving:D,postTranslation:A,processor:y,warnHtmlMessage:F,escapeParameter:_,messageCompiler:E,messageResolver:I,localeFallbacker:N,fallbackContext:p,onWarn:t,__meta:ee};return w.datetimeFormats=u,w.numberFormats=i,w.__datetimeFormatters=b,w.__numberFormatters=K,w}function Lt(e,t,n,r,s){const{missing:c,onWarn:o}=e;if(c!==null){const u=c(e,n,t,s);return C(u)?u:t}else return t}function he(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Jr(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function Qr(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(Jr(e,t[r]))return!0;return!1}function at(e){return n=>qr(n,e)}function qr(e,t){const n=t.b||t.body;if((n.t||n.type)===1){const r=n,s=r.c||r.cases;return e.plural(s.reduce((c,o)=>[...c,wt(e,o)],[]))}else return wt(e,n)}function wt(e,t){const n=t.s||t.static;if(n)return e.type==="text"?n:e.normalize([n]);{const r=(t.i||t.items).reduce((s,c)=>[...s,lt(e,c)],[]);return e.normalize(r)}}function lt(e,t){const n=t.t||t.type;switch(n){case 3:{const r=t;return r.v||r.value}case 9:{const r=t;return r.v||r.value}case 4:{const r=t;return e.interpolate(e.named(r.k||r.key))}case 5:{const r=t;return e.interpolate(e.list(r.i!=null?r.i:r.index))}case 6:{const r=t,s=r.m||r.modifier;return e.linked(lt(e,r.k||r.key),s?lt(e,s):void 0,e.type)}case 7:{const r=t;return r.v||r.value}case 8:{const r=t;return r.v||r.value}default:throw new Error(`unhandled node type on format message part: ${n}`)}}const Nn=e=>e;let Ne=Object.create(null);const Le=e=>U(e)&&(e.t===0||e.type===0)&&("b"in e||"body"in e);function gn(e,t={}){let n=!1;const r=t.onError||Zn;return t.onError=s=>{n=!0,r(s)},{...pr(e,t),detectError:n}}const Zr=(e,t)=>{if(!C(e))throw te(Z.NOT_SUPPORT_NON_STRING_MESSAGE);{x(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||Nn)(e),s=Ne[r];if(s)return s;const{code:c,detectError:o}=gn(e,t),u=new Function(`return ${c}`)();return o?u:Ne[r]=u}};function zr(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&C(e)){x(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||Nn)(e),s=Ne[r];if(s)return s;const{ast:c,detectError:o}=gn(e,{...t,location:!1,jit:!0}),u=at(c);return o?u:Ne[r]=u}else{const n=e.cacheKey;if(n){const r=Ne[n];return r||(Ne[n]=at(e))}else return at(e)}}const Wt=()=>"",se=e=>$(e);function Vt(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:s,messageCompiler:c,fallbackLocale:o,messages:u}=e,[i,d]=ct(...t),O=x(d.missingWarn)?d.missingWarn:e.missingWarn,T=x(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn,L=x(d.escapeParameter)?d.escapeParameter:e.escapeParameter,h=!!d.resolvedMessage,R=C(d.default)||x(d.default)?x(d.default)?c?i:()=>i:d.default:n?c?i:()=>i:"",D=n||R!=="",A=gt(e,d);L&&ea(d);let[y,F,_]=h?[i,A,u[A]||{}]:Ln(e,i,A,o,T,O),E=y,I=i;if(!h&&!(C(E)||Le(E)||se(E))&&D&&(E=R,I=E),!h&&(!(C(E)||Le(E)||se(E))||!C(F)))return s?Ve:i;let N=!1;const p=()=>{N=!0},S=se(E)?E:Tn(e,i,F,E,I,p);if(N)return E;const b=ra(e,F,_,d),K=Wr(b),ee=ta(e,S,K);return r?r(ee,i):ee}function ea(e){G(e.list)?e.list=e.list.map(t=>C(t)?bt(t):t):U(e.named)&&Object.keys(e.named).forEach(t=>{C(e.named[t])&&(e.named[t]=bt(e.named[t]))})}function Ln(e,t,n,r,s,c){const{messages:o,onWarn:u,messageResolver:i,localeFallbacker:d}=e,O=d(e,r,n);let T={},L,h=null;const R="translate";for(let D=0;D<O.length&&(L=O[D],T=o[L]||{},(h=i(T,t))===null&&(h=T[t]),!(C(h)||Le(h)||se(h)));D++)if(!Qr(L,O)){const A=Lt(e,t,L,c,R);A!==t&&(h=A)}return[h,L,T]}function Tn(e,t,n,r,s,c){const{messageCompiler:o,warnHtmlMessage:u}=e;if(se(r)){const d=r;return d.locale=d.locale||n,d.key=d.key||t,d}if(o==null){const d=()=>r;return d.locale=n,d.key=t,d}const i=o(r,na(e,n,s,r,u,c));return i.locale=n,i.key=t,i.source=r,i}function ta(e,t,n){return t(n)}function ct(...e){const[t,n,r]=e,s={};if(!C(t)&&!X(t)&&!se(t)&&!Le(t))throw te(Z.INVALID_ARGUMENT);const c=X(t)?String(t):(se(t),t);return X(n)?s.plural=n:C(n)?s.default=n:v(n)&&!we(n)?s.named=n:G(n)&&(s.list=n),X(r)?s.plural=r:C(r)?s.default=r:v(r)&&j(s,r),[c,s]}function na(e,t,n,r,s,c){return{locale:t,key:n,warnHtmlMessage:s,onError:o=>{throw c&&c(o),o},onCacheKey:o=>Wn(t,n,o)}}function ra(e,t,n,r){const{modifiers:s,pluralRules:c,messageResolver:o,fallbackLocale:u,fallbackWarn:i,missingWarn:d,fallbackContext:O}=e,L={locale:t,modifiers:s,pluralRules:c,messages:h=>{let R=o(n,h);if(R==null&&O){const[,,D]=Ln(O,h,t,u,i,d);R=o(D,h)}if(C(R)||Le(R)){let D=!1;const y=Tn(e,h,t,R,h,()=>{D=!0});return D?Wt:y}else return se(R)?R:Wt}};return e.processor&&(L.processor=e.processor),r.list&&(L.list=r.list),r.named&&(L.named=r.named),X(r.plural)&&(L.pluralIndex=r.plural),L}function xt(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:s,onWarn:c,localeFallbacker:o}=e,{__datetimeFormatters:u}=e,[i,d,O,T]=ot(...t),L=x(O.missingWarn)?O.missingWarn:e.missingWarn;x(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn;const h=!!O.part,R=gt(e,O),D=o(e,s,R);if(!C(i)||i==="")return new Intl.DateTimeFormat(R,T).format(d);let A={},y,F=null;const _="datetime format";for(let N=0;N<D.length&&(y=D[N],A=n[y]||{},F=A[i],!v(F));N++)Lt(e,i,y,L,_);if(!v(F)||!C(y))return r?Ve:i;let E=`${y}__${i}`;we(T)||(E=`${E}__${JSON.stringify(T)}`);let I=u.get(E);return I||(I=new Intl.DateTimeFormat(y,j({},F,T)),u.set(E,I)),h?I.formatToParts(d):I.format(d)}const In=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ot(...e){const[t,n,r,s]=e,c={};let o={},u;if(C(t)){const i=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!i)throw te(Z.INVALID_ISO_DATE_ARGUMENT);const d=i[3]?i[3].trim().startsWith("T")?`${i[1].trim()}${i[3].trim()}`:`${i[1].trim()}T${i[3].trim()}`:i[1].trim();u=new Date(d);try{u.toISOString()}catch{throw te(Z.INVALID_ISO_DATE_ARGUMENT)}}else if(xn(t)){if(isNaN(t.getTime()))throw te(Z.INVALID_DATE_ARGUMENT);u=t}else if(X(t))u=t;else throw te(Z.INVALID_ARGUMENT);return C(n)?c.key=n:v(n)&&Object.keys(n).forEach(i=>{In.includes(i)?o[i]=n[i]:c[i]=n[i]}),C(r)?c.locale=r:v(r)&&(o=r),v(s)&&(o=s),[c.key||"",u,c,o]}function $t(e,t,n){const r=e;for(const s in n){const c=`${t}__${s}`;!r.__datetimeFormatters.has(c)||r.__datetimeFormatters.delete(c)}}function Yt(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:s,onWarn:c,localeFallbacker:o}=e,{__numberFormatters:u}=e,[i,d,O,T]=it(...t),L=x(O.missingWarn)?O.missingWarn:e.missingWarn;x(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn;const h=!!O.part,R=gt(e,O),D=o(e,s,R);if(!C(i)||i==="")return new Intl.NumberFormat(R,T).format(d);let A={},y,F=null;const _="number format";for(let N=0;N<D.length&&(y=D[N],A=n[y]||{},F=A[i],!v(F));N++)Lt(e,i,y,L,_);if(!v(F)||!C(y))return r?Ve:i;let E=`${y}__${i}`;we(T)||(E=`${E}__${JSON.stringify(T)}`);let I=u.get(E);return I||(I=new Intl.NumberFormat(y,j({},F,T)),u.set(E,I)),h?I.formatToParts(d):I.format(d)}const pn=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function it(...e){const[t,n,r,s]=e,c={};let o={};if(!X(t))throw te(Z.INVALID_ARGUMENT);const u=t;return C(n)?c.key=n:v(n)&&Object.keys(n).forEach(i=>{pn.includes(i)?o[i]=n[i]:c[i]=n[i]}),C(r)?c.locale=r:v(r)&&(o=r),v(s)&&(o=s),[c.key||"",u,c,o]}function Gt(e,t,n){const r=e;for(const s in n){const c=`${t}__${s}`;!r.__numberFormatters.has(c)||r.__numberFormatters.delete(c)}}Or();/*!
  * vue-i18n v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const aa="9.14.0";function sa(){typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Me().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Me().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const On=Vr.__EXTEND_POINT__,ae=We(On);ae(),ae(),ae(),ae(),ae(),ae(),ae(),ae(),ae();const bn=Z.__EXTEND_POINT__,B=We(bn),J={UNEXPECTED_RETURN_TYPE:bn,INVALID_ARGUMENT:B(),MUST_BE_CALL_SETUP_TOP:B(),NOT_INSTALLED:B(),NOT_AVAILABLE_IN_LEGACY_MODE:B(),REQUIRED_VALUE:B(),INVALID_VALUE:B(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:B(),NOT_INSTALLED_WITH_PROVIDE:B(),UNEXPECTED_ERROR:B(),NOT_COMPATIBLE_LEGACY_VUE_I18N:B(),BRIDGE_SUPPORT_VUE_2_ONLY:B(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:B(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:B(),__EXTEND_POINT__:B()};function z(e,...t){return Te(e,null,void 0)}const ut=le("__translateVNode"),ft=le("__datetimeParts"),_t=le("__numberParts"),la=le("__setPluralRules");le("__intlifyMeta");const ca=le("__injectWithOption"),mt=le("__dispose");function Ce(e){if(!U(e))return e;for(const t in e)if(!!Fe(e,t))if(!t.includes("."))U(e[t])&&Ce(e[t]);else{const n=t.split("."),r=n.length-1;let s=e,c=!1;for(let o=0;o<r;o++){if(n[o]in s||(s[n[o]]={}),!U(s[n[o]])){c=!0;break}s=s[n[o]]}c||(s[n[r]]=e[t],delete e[t]),U(s[n[r]])&&Ce(s[n[r]])}return e}function hn(e,t){const{messages:n,__i18n:r,messageResolver:s,flatJson:c}=t,o=v(n)?n:G(r)?{}:{[e]:{}};if(G(r)&&r.forEach(u=>{if("locale"in u&&"resource"in u){const{locale:i,resource:d}=u;i?(o[i]=o[i]||{},Re(d,o[i])):Re(d,o)}else C(u)&&Re(JSON.parse(u),o)}),s==null&&c)for(const u in o)Fe(o,u)&&Ce(o[u]);return o}function oa(e){return e.type}function ia(e,t,n){let r=U(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=hn(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const s=Object.keys(r);s.length&&s.forEach(c=>{e.mergeLocaleMessage(c,r[c])});{if(U(t.datetimeFormats)){const c=Object.keys(t.datetimeFormats);c.length&&c.forEach(o=>{e.mergeDateTimeFormat(o,t.datetimeFormats[o])})}if(U(t.numberFormats)){const c=Object.keys(t.numberFormats);c.length&&c.forEach(o=>{e.mergeNumberFormat(o,t.numberFormats[o])})}}}function Xt(e){return Un(wn,null,e,0)}const Kt=()=>[],ua=()=>!1;let Ht=0;function jt(e){return(t,n,r,s)=>e(n,r,nn()||void 0,s)}function Cn(e={},t){const{__root:n,__injectWithOption:r}=e,s=n===void 0,c=e.flatJson,o=ke?Fn:vn,u=!!e.translateExistCompatible;let i=x(e.inheritLocale)?e.inheritLocale:!0;const d=o(n&&i?n.locale.value:C(e.locale)?e.locale:Ue),O=o(n&&i?n.fallbackLocale.value:C(e.fallbackLocale)||G(e.fallbackLocale)||v(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:d.value),T=o(hn(d.value,e)),L=o(v(e.datetimeFormats)?e.datetimeFormats:{[d.value]:{}}),h=o(v(e.numberFormats)?e.numberFormats:{[d.value]:{}});let R=n?n.missingWarn:x(e.missingWarn)||De(e.missingWarn)?e.missingWarn:!0,D=n?n.fallbackWarn:x(e.fallbackWarn)||De(e.fallbackWarn)?e.fallbackWarn:!0,A=n?n.fallbackRoot:x(e.fallbackRoot)?e.fallbackRoot:!0,y=!!e.fallbackFormat,F=$(e.missing)?e.missing:null,_=$(e.missing)?jt(e.missing):null,E=$(e.postTranslation)?e.postTranslation:null,I=n?n.warnHtmlMessage:x(e.warnHtmlMessage)?e.warnHtmlMessage:!0,N=!!e.escapeParameter;const p=n?n.modifiers:v(e.modifiers)?e.modifiers:{};let S=e.pluralRules||n&&n.pluralRules,b;b=(()=>{s&&vt(null);const f={version:aa,locale:d.value,fallbackLocale:O.value,messages:T.value,modifiers:p,pluralRules:S,missing:_===null?void 0:_,missingWarn:R,fallbackWarn:D,fallbackFormat:y,unresolving:!0,postTranslation:E===null?void 0:E,warnHtmlMessage:I,escapeParameter:N,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};f.datetimeFormats=L.value,f.numberFormats=h.value,f.__datetimeFormatters=v(b)?b.__datetimeFormatters:void 0,f.__numberFormatters=v(b)?b.__numberFormatters:void 0;const g=Br(f);return s&&vt(g),g})(),he(b,d.value,O.value);function ee(){return[d.value,O.value,T.value,L.value,h.value]}const w=be({get:()=>d.value,set:f=>{d.value=f,b.locale=d.value}}),Q=be({get:()=>O.value,set:f=>{O.value=f,b.fallbackLocale=O.value,he(b,d.value,f)}}),xe=be(()=>T.value),$e=be(()=>L.value),Ye=be(()=>h.value);function Ge(){return $(E)?E:null}function Xe(f){E=f,b.postTranslation=f}function Ke(){return F}function He(f){f!==null&&(_=jt(f)),F=f,b.missing=_}const ne=(f,g,W,Y,ce,Pe)=>{ee();let de;try{s||(b.fallbackContext=n?jr():void 0),de=f(b)}finally{s||(b.fallbackContext=void 0)}if(W!=="translate exists"&&X(de)&&de===Ve||W==="translate exists"&&!de){const[Pn,ya]=g();return n&&A?Y(n):ce(Pn)}else{if(Pe(de))return de;throw z(J.UNEXPECTED_RETURN_TYPE)}};function Ie(...f){return ne(g=>Reflect.apply(Vt,null,[g,...f]),()=>ct(...f),"translate",g=>Reflect.apply(g.t,g,[...f]),g=>g,g=>C(g))}function je(...f){const[g,W,Y]=f;if(Y&&!U(Y))throw z(J.INVALID_ARGUMENT);return Ie(g,W,j({resolvedMessage:!0},Y||{}))}function Ae(...f){return ne(g=>Reflect.apply(xt,null,[g,...f]),()=>ot(...f),"datetime format",g=>Reflect.apply(g.d,g,[...f]),()=>Dt,g=>C(g))}function Be(...f){return ne(g=>Reflect.apply(Yt,null,[g,...f]),()=>it(...f),"number format",g=>Reflect.apply(g.n,g,[...f]),()=>Dt,g=>C(g))}function Je(f){return f.map(g=>C(g)||X(g)||x(g)?Xt(String(g)):g)}const Qe={normalize:Je,interpolate:f=>f,type:"vnode"};function qe(...f){return ne(g=>{let W;const Y=g;try{Y.processor=Qe,W=Reflect.apply(Vt,null,[Y,...f])}finally{Y.processor=null}return W},()=>ct(...f),"translate",g=>g[ut](...f),g=>[Xt(g)],g=>G(g))}function Se(...f){return ne(g=>Reflect.apply(Yt,null,[g,...f]),()=>it(...f),"number format",g=>g[_t](...f),Kt,g=>C(g)||G(g))}function Ze(...f){return ne(g=>Reflect.apply(xt,null,[g,...f]),()=>ot(...f),"datetime format",g=>g[ft](...f),Kt,g=>C(g)||G(g))}function ze(f){S=f,b.pluralRules=S}function et(f,g){return ne(()=>{if(!f)return!1;const W=C(g)?g:d.value,Y=_e(W),ce=b.messageResolver(Y,f);return u?ce!=null:Le(ce)||se(ce)||C(ce)},()=>[f],"translate exists",W=>Reflect.apply(W.te,W,[f,g]),ua,W=>x(W))}function tt(f){let g=null;const W=fn(b,O.value,d.value);for(let Y=0;Y<W.length;Y++){const ce=T.value[W[Y]]||{},Pe=b.messageResolver(ce,f);if(Pe!=null){g=Pe;break}}return g}function pe(f){const g=tt(f);return g!=null?g:n?n.tm(f)||{}:{}}function _e(f){return T.value[f]||{}}function me(f,g){if(c){const W={[f]:g};for(const Y in W)Fe(W,Y)&&Ce(W[Y]);g=W[f]}T.value[f]=g,b.messages=T.value}function Oe(f,g){T.value[f]=T.value[f]||{};const W={[f]:g};if(c)for(const Y in W)Fe(W,Y)&&Ce(W[Y]);g=W[f],Re(g,T.value[f]),b.messages=T.value}function nt(f){return L.value[f]||{}}function a(f,g){L.value[f]=g,b.datetimeFormats=L.value,$t(b,f,g)}function l(f,g){L.value[f]=j(L.value[f]||{},g),b.datetimeFormats=L.value,$t(b,f,g)}function m(f){return h.value[f]||{}}function P(f,g){h.value[f]=g,b.numberFormats=h.value,Gt(b,f,g)}function V(f,g){h.value[f]=j(h.value[f]||{},g),b.numberFormats=h.value,Gt(b,f,g)}Ht++,n&&ke&&(st(n.locale,f=>{i&&(d.value=f,b.locale=f,he(b,d.value,O.value))}),st(n.fallbackLocale,f=>{i&&(O.value=f,b.fallbackLocale=f,he(b,d.value,O.value))}));const M={id:Ht,locale:w,fallbackLocale:Q,get inheritLocale(){return i},set inheritLocale(f){i=f,f&&n&&(d.value=n.locale.value,O.value=n.fallbackLocale.value,he(b,d.value,O.value))},get availableLocales(){return Object.keys(T.value).sort()},messages:xe,get modifiers(){return p},get pluralRules(){return S||{}},get isGlobal(){return s},get missingWarn(){return R},set missingWarn(f){R=f,b.missingWarn=R},get fallbackWarn(){return D},set fallbackWarn(f){D=f,b.fallbackWarn=D},get fallbackRoot(){return A},set fallbackRoot(f){A=f},get fallbackFormat(){return y},set fallbackFormat(f){y=f,b.fallbackFormat=y},get warnHtmlMessage(){return I},set warnHtmlMessage(f){I=f,b.warnHtmlMessage=f},get escapeParameter(){return N},set escapeParameter(f){N=f,b.escapeParameter=f},t:Ie,getLocaleMessage:_e,setLocaleMessage:me,mergeLocaleMessage:Oe,getPostTranslationHandler:Ge,setPostTranslationHandler:Xe,getMissingHandler:Ke,setMissingHandler:He,[la]:ze};return M.datetimeFormats=$e,M.numberFormats=Ye,M.rt=je,M.te=et,M.tm=pe,M.d=Ae,M.n=Be,M.getDateTimeFormat=nt,M.setDateTimeFormat=a,M.mergeDateTimeFormat=l,M.getNumberFormat=m,M.setNumberFormat=P,M.mergeNumberFormat=V,M[ca]=r,M[ut]=qe,M[ft]=Ze,M[_t]=Se,M}const Tt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function fa({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,s)=>[...r,...s.type===tn?s.children:[s]],[]):t.reduce((n,r)=>{const s=e[r];return s&&(n[r]=s()),n},{})}function An(e){return tn}const _a=dt({name:"i18n-t",props:j({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>X(e)||!isNaN(e)}},Tt),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||It({useScope:e.scope,__useComponent:!0});return()=>{const c=Object.keys(n).filter(T=>T!=="_"),o={};e.locale&&(o.locale=e.locale),e.plural!==void 0&&(o.plural=C(e.plural)?+e.plural:e.plural);const u=fa(t,c),i=s[ut](e.keypath,u,o),d=j({},r),O=C(e.tag)||U(e.tag)?e.tag:An();return en(O,d,i)}}}),Bt=_a;function ma(e){return G(e)&&!C(e[0])}function Sn(e,t,n,r){const{slots:s,attrs:c}=t;return()=>{const o={part:!0};let u={};e.locale&&(o.locale=e.locale),C(e.format)?o.key=e.format:U(e.format)&&(C(e.format.key)&&(o.key=e.format.key),u=Object.keys(e.format).reduce((L,h)=>n.includes(h)?j({},L,{[h]:e.format[h]}):L,{}));const i=r(e.value,o,u);let d=[o.key];G(i)?d=i.map((L,h)=>{const R=s[L.type],D=R?R({[L.type]:L.value,index:h,parts:i}):[L.value];return ma(D)&&(D[0].key=`${L.type}-${h}`),D}):C(i)&&(d=[i]);const O=j({},c),T=C(e.tag)||U(e.tag)?e.tag:An();return en(T,O,d)}}const da=dt({name:"i18n-n",props:j({value:{type:Number,required:!0},format:{type:[String,Object]}},Tt),setup(e,t){const n=e.i18n||It({useScope:e.scope,__useComponent:!0});return Sn(e,t,pn,(...r)=>n[_t](...r))}}),Jt=da,Ea=dt({name:"i18n-d",props:j({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Tt),setup(e,t){const n=e.i18n||It({useScope:e.scope,__useComponent:!0});return Sn(e,t,In,(...r)=>n[ft](...r))}}),Qt=Ea;function Na(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function ga(e){const t=o=>{const{instance:u,modifiers:i,value:d}=o;if(!u||!u.$)throw z(J.UNEXPECTED_ERROR);const O=Na(e,u.$),T=qt(d);return[Reflect.apply(O.t,O,[...Zt(T)]),O]};return{created:(o,u)=>{const[i,d]=t(u);ke&&e.global===d&&(o.__i18nWatcher=st(d.locale,()=>{u.instance&&u.instance.$forceUpdate()})),o.__composer=d,o.textContent=i},unmounted:o=>{ke&&o.__i18nWatcher&&(o.__i18nWatcher(),o.__i18nWatcher=void 0,delete o.__i18nWatcher),o.__composer&&(o.__composer=void 0,delete o.__composer)},beforeUpdate:(o,{value:u})=>{if(o.__composer){const i=o.__composer,d=qt(u);o.textContent=Reflect.apply(i.t,i,[...Zt(d)])}},getSSRProps:o=>{const[u]=t(o);return{textContent:u}}}}function qt(e){if(C(e))return{path:e};if(v(e)){if(!("path"in e))throw z(J.REQUIRED_VALUE,"path");return e}else throw z(J.INVALID_VALUE)}function Zt(e){const{path:t,locale:n,args:r,choice:s,plural:c}=e,o={},u=r||{};return C(n)&&(o.locale=n),X(s)&&(o.plural=s),X(c)&&(o.plural=c),[t,u,o]}function La(e,t,...n){const r=v(n[0])?n[0]:{},s=!!r.useI18nComponentName;(x(r.globalInstall)?r.globalInstall:!0)&&([s?"i18n":Bt.name,"I18nT"].forEach(o=>e.component(o,Bt)),[Jt.name,"I18nN"].forEach(o=>e.component(o,Jt)),[Qt.name,"I18nD"].forEach(o=>e.component(o,Qt))),e.directive("t",ga(t))}const Ta=le("global-vue-i18n");function ka(e={},t){const n=x(e.globalInjection)?e.globalInjection:!0,r=!0,s=new Map,[c,o]=Ia(e),u=le("");function i(T){return s.get(T)||null}function d(T,L){s.set(T,L)}function O(T){s.delete(T)}{const T={get mode(){return"composition"},get allowComposition(){return r},async install(L,...h){if(L.__VUE_I18N_SYMBOL__=u,L.provide(L.__VUE_I18N_SYMBOL__,T),v(h[0])){const A=h[0];T.__composerExtend=A.__composerExtend,T.__vueI18nExtend=A.__vueI18nExtend}let R=null;n&&(R=Pa(L,T.global)),La(L,T,...h);const D=L.unmount;L.unmount=()=>{R&&R(),T.dispose(),D()}},get global(){return o},dispose(){c.stop()},__instances:s,__getInstance:i,__setInstance:d,__deleteInstance:O};return T}}function It(e={}){const t=nn();if(t==null)throw z(J.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw z(J.NOT_INSTALLED);const n=pa(t),r=ba(n),s=oa(t),c=Oa(e,s);if(c==="global")return ia(r,e,s),r;if(c==="parent"){let i=ha(n,t,e.__useComponent);return i==null&&(i=r),i}const o=n;let u=o.__getInstance(t);if(u==null){const i=j({},e);"__i18n"in s&&(i.__i18n=s.__i18n),r&&(i.__root=r),u=Cn(i),o.__composerExtend&&(u[mt]=o.__composerExtend(u)),Aa(o,t,u),o.__setInstance(t,u)}return u}function Ia(e,t,n){const r=yn();{const s=r.run(()=>Cn(e));if(s==null)throw z(J.UNEXPECTED_ERROR);return[r,s]}}function pa(e){{const t=Rn(e.isCE?Ta:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw z(e.isCE?J.NOT_INSTALLED_WITH_PROVIDE:J.UNEXPECTED_ERROR);return t}}function Oa(e,t){return we(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function ba(e){return e.mode==="composition"?e.global:e.global.__composer}function ha(e,t,n=!1){let r=null;const s=t.root;let c=Ca(t,n);for(;c!=null;){const o=e;if(e.mode==="composition"&&(r=o.__getInstance(c)),r!=null||s===c)break;c=c.parent}return r}function Ca(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function Aa(e,t,n){kn(()=>{},t),Dn(()=>{const r=n;e.__deleteInstance(t);const s=r[mt];s&&(s(),delete r[mt])},t)}const Sa=["locale","fallbackLocale","availableLocales"],zt=["t","rt","d","n","tm","te"];function Pa(e,t){const n=Object.create(null);return Sa.forEach(s=>{const c=Object.getOwnPropertyDescriptor(t,s);if(!c)throw z(J.UNEXPECTED_ERROR);const o=Mn(c.value)?{get(){return c.value.value},set(u){c.value.value=u}}:{get(){return c.get&&c.get()}};Object.defineProperty(n,s,o)}),e.config.globalProperties.$i18n=n,zt.forEach(s=>{const c=Object.getOwnPropertyDescriptor(t,s);if(!c||!c.value)throw z(J.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${s}`,c)}),()=>{delete e.config.globalProperties.$i18n,zt.forEach(s=>{delete e.config.globalProperties[`$${s}`]})}}sa();__INTLIFY_JIT_COMPILATION__?Ft(zr):Ft(Zr);Kr(Rr);Hr(fn);export{ka as c,It as u};
