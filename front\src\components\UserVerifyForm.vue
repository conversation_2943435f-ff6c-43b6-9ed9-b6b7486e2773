<template>
  <q-form @submit.prevent="verifyUser" v-if="modelValue">
    <!-- username -->
    <div class="row q-mb-md">
      <q-input
        type="text"
        v-model="username"
        :label="t('account')"
        stack-label
        outlined
        class="full-width"
        required
      />
    </div>
    <!-- password -->
    <div class="row q-mb-xl">
      <q-input
        :type="isPwd ? 'password' : 'text'"
        v-model="password"
        :label="t('password')"
        stack-label
        outlined
        class="full-width"
        required
      >
        <template v-slot:append>
          <q-icon
            :name="isPwd ? 'visibility_off' : 'visibility'"
            class="cursor-pointer"
            @click="isPwd = !isPwd"
          />
        </template>
      </q-input>
    </div>
    <div class="row q-mb-md">
      <q-btn
        type="submit"
        :label="t('submit')"
        no-caps
        color="primary"
        size="lg"
        class="full-width"
        :loading="isSubmit"
      />

      <q-btn
        type="button"
        @click="cancel"
        :label="t('cancel')"
        color="negative"
        no-caps
        size="lg"
        class="full-width q-mt-md"
        :loading="isSubmit"
      />
    </div>
  </q-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { AuthApi } from '@/api/auth';

const { t } = useI18n();

defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits(['update:modelValue', 'login']);

const username = ref('');
const password = ref('');
const isPwd = ref(true);

const isSubmit = ref(false);
const verifyUser = async () => {
  try {
    isSubmit.value = true;
    const response = await AuthApi.verifyUser({
      username: username.value,
      password: password.value,
    });

    init();

    emit('login', response.result);
    emit('update:modelValue', false);
  } finally {
    isSubmit.value = false;
  }
};

const cancel = () => {
  init();
  emit('update:modelValue', false);
};

const init = () => {
  username.value = '';
  password.value = '';
};
</script>
