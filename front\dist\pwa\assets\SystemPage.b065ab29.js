import{bk as u,d as g,r as p,o as y,p as b,v as x,m as d,j as t,n as w,G as q,t as s,B as Q,q as h,aV as V,Q as c,C as v,aK as S,y as k,H as C,b2 as B}from"./index.09f89dc4.js";import{Q as N}from"./QForm.c51f3f04.js";import{Q as P}from"./QPage.ce1b4cb5.js";import{u as z}from"./vue-i18n.1783a0cb.js";const _={get:()=>u.get("v1/systems"),update:r=>u.put("v1/systems",r)},A={class:"row q-mb-md"},G={class:"col-12 text-h6 text-bold text-black"},I={class:"row items-center"},M={href:"https://myaccount.google.com/apppasswords",target:"_blank",class:"self-center q-ml-sm"},T={class:"row q-mb-md"},U={class:"col-12 col-md-10"},j={class:"row q-mb-md"},D={class:"col-12 col-md-10"},W=g({__name:"SystemPage",setup(r){const{t:i}=z(),a=p({gmail:"",gmail_app_password:""}),o=p(!1),m=async()=>{try{o.value=!0;const n=await _.get();a.value=n.result}finally{o.value=!1}},f=async()=>{try{o.value=!0,await _.update(a.value),B.create({type:"positive",message:i("success"),position:"top"})}finally{o.value=!1,m()}};return y(()=>{m()}),(n,e)=>(b(),x(P,{class:"q-pt-md"},{default:d(()=>[t(C,{flat:"",square:"",bordered:"",class:"q-mx-auto",style:{width:"800px","max-width":"100%"}},{default:d(()=>[t(N,{onSubmit:w(f,["prevent"]),greedy:""},{default:d(()=>[t(q,null,{default:d(()=>[s("div",A,[s("div",G,[s("div",I,[e[2]||(e[2]=Q(" Gmail STMP ")),s("a",M,[t(h,{name:"help",size:"sm",color:"primary"})])]),t(V,{color:"black",size:"2px",class:"q-mb-sm"})])]),s("div",T,[e[3]||(e[3]=s("div",{class:"col-12 col-md-2 text-subtitle1 text-md-center"}," Gmail ",-1)),s("div",U,[t(c,{type:"email",modelValue:a.value.gmail,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value.gmail=l),outlined:"",dense:"","hide-bottom-space":""},null,8,["modelValue"])])]),s("div",j,[e[4]||(e[4]=s("div",{class:"col-12 col-md-2 text-subtitle1 text-md-center"}," App Password ",-1)),s("div",D,[t(c,{modelValue:a.value.gmail_app_password,"onUpdate:modelValue":e[1]||(e[1]=l=>a.value.gmail_app_password=l),outlined:"",dense:"","hide-bottom-space":"",rules:[l=>!!l||!a.value.gmail||v(i)("error.required")]},null,8,["modelValue","rules"])])])]),_:1}),t(S,{align:"right",class:"q-px-lg q-py-md"},{default:d(()=>[t(k,{type:"submit",label:v(i)("save"),color:"positive",size:"md",loading:o.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1})]),_:1}))}});export{W as default};
