<template>
  <div class="date-range-picker row items-center">
    <!-- 開始日期 -->
    <q-input
      v-if="showFromDate"
      v-model="internalDateRange.from"
      :label="fromLabelComputed"
      mask="date"
      :rules="computedFromRules"
      dense
      readonly
      :disable="disable"
      :class="inputClass"
    >
      <template v-slot:prepend>
        <q-icon name="event" class="cursor-pointer">
          <q-popup-proxy
            ref="fromDatePopup"
            cover
            transition-show="scale"
            transition-hide="scale"
          >
            <q-date
              v-model="internalDateRange.from"
              :options="fromDateOptionsComputed"
              :mask="dateMask"
              @update:model-value="handleFromDateUpdate"
              :today-btn="showTodayBtn"
              :disable="disable"
            >
              <div class="row items-center justify-end q-gutter-sm">
                <q-btn
                  v-if="showClearButton"
                  :label="t('clear')"
                  color="negative"
                  flat
                  @click="clearFromDate"
                />
                <q-btn
                  v-if="showCloseButton"
                  :label="t('close')"
                  color="primary"
                  flat
                  v-close-popup
                />
              </div>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </template>
    </q-input>

    <span v-if="showSeparator" :class="separatorClass">
      {{ separatorText }}
    </span>

    <!-- 結束日期 -->
    <q-input
      v-if="showToDate"
      v-model="internalDateRange.to"
      :label="toLabelComputed"
      mask="date"
      :rules="computedToRules"
      dense
      readonly
      :disable="disable"
      :class="inputClass"
    >
      <template v-slot:prepend>
        <q-icon name="event" class="cursor-pointer">
          <q-popup-proxy
            ref="toDatePopup"
            cover
            transition-show="scale"
            transition-hide="scale"
          >
            <q-date
              v-model="internalDateRange.to"
              :options="toDateOptionsComputed"
              :mask="dateMask"
              @update:model-value="handleToDateUpdate"
              :today-btn="showTodayBtn"
              :disable="disable"
            >
              <div class="row items-center justify-end q-gutter-sm">
                <q-btn
                  v-if="showClearButton"
                  :label="t('clear')"
                  color="negative"
                  flat
                  @click="clearToDate"
                />
                <q-btn
                  v-if="showCloseButton"
                  :label="t('close')"
                  color="primary"
                  flat
                  v-close-popup
                />
              </div>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </template>
    </q-input>
  </div>
</template>

<script lang="ts" setup>
import { ValidationRule } from 'quasar';
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 定義 props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ from: '', to: '' }),
  },
  dateMask: {
    type: String,
    default: 'YYYY/MM/DD',
  },
  fromLabel: {
    type: String,
    default: '',
  },
  toLabel: {
    type: String,
    default: '',
  },
  fromRules: {
    type: Array as () => ValidationRule[],
    default: () => ['date'],
  },
  toRules: {
    type: Array as () => ValidationRule[],
    default: () => ['date'],
  },
  autoClose: {
    type: Boolean,
    default: true,
  },
  showTodayBtn: {
    type: Boolean,
    default: true,
  },
  showClearButton: {
    type: Boolean,
    default: true,
  },
  showCloseButton: {
    type: Boolean,
    default: true,
  },
  showFromDate: {
    type: Boolean,
    default: true,
  },
  showToDate: {
    type: Boolean,
    default: true,
  },
  showSeparator: {
    type: Boolean,
    default: true,
  },
  separatorText: {
    type: String,
    default: '～',
  },
  separatorClass: {
    type: String,
    default: 'q-mx-md',
  },
  inputClass: {
    type: String,
    default: '',
  },
  disable: {
    type: Boolean,
    default: false,
  },
  // 自定義日期範圍限制
  customFromDateOptions: {
    type: Function,
    default: null,
  },
  customToDateOptions: {
    type: Function,
    default: null,
  },
  // 是否啟用日期範圍限制檢查
  enableDateRangeValidation: {
    type: Boolean,
    default: true,
  },
  // 是否允許日期為空
  allowEmptyDate: {
    type: Boolean,
    default: true,
  },
});

// 定義事件
const emit = defineEmits([
  'update:modelValue',
  'from-date-change',
  'to-date-change',
  'date-clear',
]);

// 內部狀態
const internalDateRange = ref({
  from: props.modelValue.from || '',
  to: props.modelValue.to || '',
});

// refs 用於控制彈出窗口
const fromDatePopup = ref();
const toDatePopup = ref();

const fromLabelComputed = computed(() => {
  return props.fromLabel || t('datePicker.from');
});

const toLabelComputed = computed(() => {
  return props.toLabel || t('datePicker.to');
});

// 計算日期限制函數
const fromDateOptionsComputed = computed(() => {
  return (date: string) => {
    // 如果提供了自定義檢查函數，優先使用
    if (props.customFromDateOptions) {
      return props.customFromDateOptions(date, internalDateRange.value);
    }

    // 標準範圍檢查：開始日期不能晚於結束日期
    if (!props.enableDateRangeValidation || !internalDateRange.value.to)
      return true;
    const selectedDate = new Date(date);
    const endDate = new Date(internalDateRange.value.to);
    return selectedDate <= endDate;
  };
});

const toDateOptionsComputed = computed(() => {
  return (date: string) => {
    // 如果提供了自定義檢查函數，優先使用
    if (props.customToDateOptions) {
      return props.customToDateOptions(date, internalDateRange.value);
    }

    // 標準範圍檢查：結束日期不能早於開始日期
    if (!props.enableDateRangeValidation || !internalDateRange.value.from)
      return true;
    const selectedDate = new Date(date);
    const startDate = new Date(internalDateRange.value.from);
    return selectedDate >= startDate;
  };
});

// 計算後的規則：如果啟用允許空值，則為空時不驗證
const computedFromRules = computed(() => {
  if (props.allowEmptyDate) {
    return props.fromRules.map((rule) => {
      if (rule === 'date') {
        return (val: string) =>
          !val ||
          val === '' ||
          /^\d{4}\/\d{1,2}\/\d{1,2}$/.test(val) ||
          t('error.invalidDate');
      }
      return rule;
    });
  }
  return props.fromRules;
});

const computedToRules = computed(() => {
  if (props.allowEmptyDate) {
    return props.toRules.map((rule) => {
      if (rule === 'date') {
        return (val: string) =>
          !val ||
          val === '' ||
          /^\d{4}\/\d{1,2}\/\d{1,2}$/.test(val) ||
          t('error.invalidDate');
      }
      return rule;
    });
  }
  return props.toRules;
});

// 關閉日期選擇彈出窗口的處理函數
const handleFromDateUpdate = () => {
  emit('from-date-change', internalDateRange.value.from);
  updateModelValue();

  if (props.autoClose) {
    setTimeout(() => {
      fromDatePopup.value?.hide();
    }, 50);
  }
};

const handleToDateUpdate = () => {
  emit('to-date-change', internalDateRange.value.to);
  updateModelValue();

  if (props.autoClose) {
    setTimeout(() => {
      toDatePopup.value?.hide();
    }, 50);
  }
};

// 清除日期的處理函數
const clearFromDate = () => {
  internalDateRange.value.from = '';
  emit('from-date-change', '');
  emit('date-clear', 'from');
  updateModelValue();

  fromDatePopup.value?.hide();
};

const clearToDate = () => {
  internalDateRange.value.to = '';
  emit('to-date-change', '');
  emit('date-clear', 'to');
  updateModelValue();

  toDatePopup.value?.hide();
};

// 更新父組件中的值
const updateModelValue = () => {
  emit('update:modelValue', {
    from: internalDateRange.value.from,
    to: internalDateRange.value.to,
  });
};

// 監聽外部 modelValue 的變化
watch(
  () => props.modelValue,
  (newVal) => {
    if (
      newVal.from !== internalDateRange.value.from ||
      newVal.to !== internalDateRange.value.to
    ) {
      internalDateRange.value = {
        from: newVal.from || '',
        to: newVal.to || '',
      };
    }
  },
  { deep: true }
);
</script>
